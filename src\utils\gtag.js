import { getCookies } from '@/kit/cookie'
/* eslint-disable */
export default function sendGtag(el, player) {
  let str = ''
  // 根据regType 兑换成 login_type
  const loginType = {
    11: 1,
    10: 2,
    28: 3,
    32: 4,
    39: 5
  }
  let options = null
  let action = 'click'

  str += getCookies('mod') + '|'
  str += getCookies('lang') + '|'
  str += (localStorage['vipType'] ? localStorage['vipType'] : '') + '|'
  str +=
    (loginType[localStorage['regType']]
      ? loginType[localStorage['regType']]
      : 0) + '|'
  str += getCookies('QC005') + '|'
  str += uid ? uid : '' // uid 为_document.js 中定义

  if (player) {
    options = {
      event_category: 'player',
      event_label: str
    }
    action = 'play'
  } else {
    options = {
      event_category:
        el.getAttribute('gtag-category') ||
        el.getAttribute('data-gtag-category'),
      event_label: str
    }

    // 佩瑜要求 所有都加上此选项
    // if (
    //   el.getAttribute('gtag-interaction') ||
    //   el.getAttribute('data-gtag-interaction')
    // ) {
    options['non_interaction'] = true
    // }
    if (
      el.getAttribute('gtag-action') ||
      el.getAttribute('data-gtag-interaction')
    ) {
      action =
        el.getAttribute('gtag-action') ||
        el.getAttribute('data-gtag-interaction')
    }
  }

  gtag('event', action, options)
}
