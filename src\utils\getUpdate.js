export default function getUpdate(
  option,
  updateTo,
  count,
  termTpisode,
  updateTotalEpisode
) {
  updateTo = updateTo || ''
  count = count || ''
  if (option.chnId === '') {
    return ''
  }
  if (option.chnId === 1) {
    return ''
  }
  if (option.chnId === 6) {
    // 综艺类 %s Episodes 如果没有term_episode语料会兜底走07-11这种形式
    const str = `${option.publishTime.substring(
      4,
      6
    )}-${option.publishTime.substring(6, 8)}`
    return termTpisode ? termTpisode.replace('%s', str) : str
  }
  if (option.tvCount !== undefined) {
    if (option.tvCount <= 0) {
      return ''
    }
    if (option.tvCount !== option.total) {
      if (updateTotalEpisode && option.total !== 0) {
        const temp = updateTotalEpisode
          .replace('%s', option.tvCount)
          .replace('%d', option.total)
        return temp
      }
      return updateTo.replace('%s', option.tvCount)
    } else {
      return count.replace('%s', option.total)
    }
  }
  return ''
}
