import styled from 'styled-components'

const contentWidth = '504px'
const middleWidth = '532px'
const PayTypeWrapper = styled.div`
  .bank-pay-err {
    box-sizing: border-box;
    width: ${contentWidth};
    min-height: 44px;
    background: #FEEBEB;
    border: 1px solid #FEC5C2;
    border-radius: 6px;
    margin: 12px auto;
    padding: 12px;
    display: flex;
    align-items: center;
    .err-img {
      flex-shrink: 0;
      display: inline-block;
      vertical-align: middle;
      width: 20px;
      height: 20px;
      background: url('//www.iqiyipic.com/lequ/********/icon_error.png') no-repeat;
      background-size: cover;
      margin-right: 8px;
    }
    .err-desc {
      line-height: 16px;
      font-size: 14px;
      color: #FE3D33;
      letter-spacing: 0;
      font-weight: 400;
      vertical-align: middle;
    }
  }
  .paytype-center {
    box-sizing: border-box;
    margin: 16px auto 0;
    padding: 16px 0;
    /* background: #ffffff; */
    /* padding: 24px; */
    .type-item.selected {
      background: #FCF2E6;
      border: 3px solid #EDB97B;
      box-shadow: 0px 1px 4px 0px rgba(0,0,0,0.02);
      padding: 0 14px;
    }
    .btn-wrap {
      position: relative;
      width: ${contentWidth};
      margin: 0 auto;
      margin-bottom: 16px;
      &:last-child {
        margin-bottom: 0;
      }
    }
    .label {
      position: absolute;
      top: -4px;
      left: 16px;
      font-size: 12px;
      color: #111319;
      text-align: center;
      line-height: 12px;
      height: 20px;
      padding: 4px;
      box-sizing: border-box;
      background: #F2BF83;
      box-shadow: 0px 1px 4px 0px rgba(242,191,131,0.5);
      border-radius: 4px;
      border-top-right-radius: 0;
      border-top-left-radius: 0;
      max-width: 472px;
      & > span {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 100%;
        display: inline-block;
        vertical-align: middle;
      }
      &:before {
        display: block;
        content: ' ';
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-right: 3px solid #91724E;
        position: absolute;
        left: -2px;
        top: 0px;
        transform: rotate(225deg);
      }
    }
    .type-item {
      display: flex;
      align-items: center;
      background: #FFFFFF;
      border: 1px solid #E9E9E9;
      box-shadow: 0px 1px 4px 0px rgba(0,0,0,0.02);
      border-radius: 6px;
      box-sizing: border-box;
      height: 80px;
      padding: 0 16px;
      position: relative;
      cursor: pointer;
      outline: none;
      &:hover:not(.selected) {
        background: #FFFFFF;
        border: 2px solid #EDB97B;
        box-shadow: 0px 1px 4px 0px rgba(0,0,0,0.02);
        padding: 0 15px;
      }
      img {
        width: 36px;
        margin-right: 12px;
        border-radius: 6px;
        border: 1px solid #E6E6E6
      }
      .name {
        width: 152px;
        line-height: 19px;
        font-size: 16px;
        color: #222222;
        letter-spacing: 0;
        font-weight: 500;
      }
      .des {
        width: 248px;
        margin-left: 24px;
        line-height: 16px;
        font-size: 14px;
        color: #666666;
        letter-spacing: 0;
        text-align: right;
        font-weight: 400;
      }
    }
  }
  .more-options {
    margin-top: -4px;
    a {
      color: #bb8b51;
      font-size: 12px;
      line-height: 18px;
      text-decoration: underline;
    }
  }
  .autorenew-terms {
    width: ${contentWidth};
    margin: 12px auto 0;
    font-size: 12px;
    color: #666666;
    line-height: 18px;
    word-break: break-word;
  }
  .cash-agreement {
    margin: 16px 200px 0;
    position: relative;
    font-size: 14px;
    color: #222222;
    line-height: 16px;
    display: flex;
    align-items: center;
    font-weight: 400;
    .icon-check {
      display: inline-block;
      width: 16px;
      height: 16px;
      margin-right: 8px;
      cursor: pointer;
    }
    .nomal {
      background: url('//www.iqiyipic.com/lequ/20210625/icon-check-normal.png') no-repeat;
      background-size: cover;
    }
    .checked {
      background: url('//www.iqiyipic.com/lequ/********/icon-check-selected.png') no-repeat;
      background-size: cover;
    }
    .error {
      background: url('//www.iqiyipic.com/lequ/20210625/icon-check-red.png') no-repeat;
      background-size: cover;
    }
    .service {
      line-height: 16px;
      cursor: pointer;
      margin-left: 4px;
      font-size: 14px;
      color: #E09E51;
      letter-spacing: 0;
      font-weight: 400;
      &:hover {
        text-decoration: underline;
      }
    }
  }
  .checkout-error {
    margin: 8px 200px 0;
    line-height: 16px;
    font-size: 14px;
    color: #FE3D33;
    letter-spacing: 0;
    font-weight: 400;
  }
  @media screen and (max-width: 1023px) {
    .paytype-center {
      .label {
        max-width: 500px;
      }
      .btn-wrap {
        width: ${middleWidth};
      }
    }
    .bank-pay-err,
    .autorenew-terms {
      width: ${middleWidth};
    }
    .cash-agreement,
    .checkout-error {
      margin-left: 48px;
      margin-right: 48px;
    }
    .paytype-center {
      .type-item {
        .name {
          width: 160px;
        }
        .des {
          width: 268px;
        }
      }
    }
  }
`
export default PayTypeWrapper
