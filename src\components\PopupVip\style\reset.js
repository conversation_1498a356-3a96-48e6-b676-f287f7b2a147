import style from 'styled-components'

const GlobalStyle = style.div`
  margin: 0;
  padding: 0;
  outline: 0;
  p,
  ul, li,
  button { /*table elements 表格元素*/
    margin: 0;
    padding: 0;
    outline: 0;
  }

  i {
    font-style: normal;
  }

  /*重置列表元素,li元素的display为list-item，如果单独使用li会出现list-style，将li嵌套在ul或ol中则继承父元素的none*/
  ul {
    list-style: none;
  }
  /*Forms 使得表单元素能继承字体和字体大小*/
  button {
    font-family: inherit;
    font-size: 100%;
    &:focus {
      outline: none;
    }
  }

  button {
    -webkit-appearance: button; /* Corrects inability to style clickable 'input' types in iOS.*/
  }

  img {
    border: none;
    vertical-align: top;
  }
  /*a标签 去掉链接默认样式*/
  a {
    text-decoration: none;
  }

  a:link, a:visited, a:hover, a:active {
    text-decoration: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
`
export default GlobalStyle
