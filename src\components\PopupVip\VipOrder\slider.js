import React, { useState, useRef } from 'react'
import { ArrowLeft, ArrowRight } from '@/components/svgs'
import SliderStyle from './style/slider'

const Slider = ({ pageNum, children, pbInfoStr, handlePageChange }) => {
  const slider = useRef(null)
  const content = useRef(null)
  const [page, setPage] = useState(0)
  const sliderWidth = (slider.current ? slider.current.clientWidth : 0) + 16 
  return (
    <SliderStyle>
      <div
        className="left"
        onClick={() => {
          setPage(page - 1)
          if (handlePageChange) handlePageChange(page - 1)
        }}
        role="button"
        tabIndex="0"
        style={{ display: page ? 'flex' : 'none' }}
        data-pb={`rpage=cashier_popup&block=extend_info&${pbInfoStr}`}
        rseat="previous"
      >
        {/* <ArrowLeft /> */}
      </div>
      <div
        className="slider"
        ref={ref => {
          slider.current = ref
        }}
      >
        <div
          className="slider-content"
          ref={ref => {
            content.current = ref
          }}
          style={{ transform: `translateX(-${page * sliderWidth}px)` }}
        >
          {children}
        </div>
      </div>
      <div
        className="right"
        onClick={() => {
          setPage(page + 1)
          if(handlePageChange) handlePageChange(page + 1)
        }}
        role="button"
        tabIndex="0"
        style={{ display: page + 1 < pageNum ? 'flex' : 'none' }}
        data-pb={`rpage=cashier_popup&block=extend_info&${pbInfoStr}`}
        rseat="next"
      >
        {/* <ArrowRight /> */}
      </div>
    </SliderStyle>
  )
}

export default Slider
