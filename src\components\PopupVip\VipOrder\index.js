import React, { useRef, useEffect, useCallback } from 'react'
import qs from 'qs'
import { getViewWidth } from '@/kit/dom'
import { rebuildCommonUrl } from '@/kit/common'
import { isLogin } from '@/utils/userInfo'
import { sendBlockPb, sendClickPb } from '@/utils/pingBack'
import Style from './style'
import { RedeemIcon, AlertIcon } from '../style/icon'

import EmptyComponent from './emptyComponent'
import TilePackages from './tilePackages'
import GroupPackages from './groupPackages'
import Slider from './slider'

const VipOrder = props => {
  if (!props.vipInfo) return <EmptyComponent />
  const vipInfo = props.vipInfo || {}
  const {
    vipLangPkg,
    cashierLangPkg,
    handleSubmit,
    error,
    vipType,
    productSetCode, 
    clearProductCode,
    selectedPkg,
    setSelectPkg,
    pbInfo
  } = props

  const {
    vipPkgList = [],
    storeNodeLocations = {},
    vipTypes,
    // isPopupMixed,
    firstStepHeader,
    firstStepTip,
    vipRedeemCodeLocation,
    firstStepBottomLine
  } = vipInfo

  // const [selectedPkg, setSelectPkg] = useState({})
  const isPopupMixed = false // 移除分类样式
  // const [selectedPkg, setSelectPkg] = useState({})
  useEffect(() => {
    sendBlockPb('cashier_popup', {
      bstp: 56,
      tjPb: {
        ...pbInfo,
        v_pid: '',
        v_prod: ''
      }
    })
  }, [])

  useEffect(() => {
    let recommendIndex = -1
    // 有登录之前的选中套餐直接下一步
    if (productSetCode) {
      recommendIndex = vipPkgList && vipPkgList.findIndex(
        item => item.productSetCode === productSetCode
      )
      clearProductCode()
    }
    if (recommendIndex !== -1 && vipPkgList.length) {

      setSelectPkg(vipPkgList[recommendIndex])
      handleSubmit(vipPkgList[recommendIndex])
    } else {
      recommendIndex = vipPkgList && vipPkgList.findIndex(
        item => item.recommend && (vipType ? item.vipTypeId === Number(vipType) : true)
      )
      if (recommendIndex === -1) recommendIndex = 0
      setSelectPkg(vipPkgList[recommendIndex])
    }
    sendPkgBlockPb(0)
  }, [vipPkgList, vipType])

  const handleNextStep = useCallback(() => {
    handleSubmit(selectedPkg)
  }, [selectedPkg])

  const hasSendPbPage = useRef([])

  const sendPkgBlockPb = page => {
    if (hasSendPbPage.current.includes(page)) return

    if (isPopupMixed) {
      vipTypes.slice(page * 2, page * 3).forEach(type => {
        const typeId = Number(type.id)
        vipPkgList
          .filter(item => item.vipTypeId === typeId)
          .forEach(pkg => {
            sendBlockPb('product_type', {
              rpage: 'cashier_popup',
              bstp: 56,
              tjPb: {
                position: pkg.index,
                ...pbInfo,
                v_pid: pkg.pid,
                v_prod: pkg.productSetCode
              }
            })
          })
      })
    } else {
      const pageSize = getViewWidth() < 1024 ? 2 : 3
      vipPkgList.slice(page * pageSize, (page + 1) * pageSize).forEach(pkg => {
        sendBlockPb('product_type', {
          rpage: 'cashier_popup',
          bstp: 56,
          tjPb: {
            position: pkg.index,
            ...pbInfo,
            v_pid: pkg.pid,
            v_prod: pkg.productSetCode
          }
        })
      })
    }
    hasSendPbPage.current = [...hasSendPbPage.current, page]
  }

  const singleType =
    isPopupMixed &&
    vipPkgList.every(item => item.vipTypeId === vipPkgList[0].vipTypeId)
  const pbInfoStr = qs.stringify(pbInfo) + '&bstp=56'
  return (
    <Style className={singleType ? 'single' : ''}>
      <div className="scroll-container">
        <div className="scroll-content">
          {/* <div className="step">{vipLangPkg.pcashier_plan_stepTitle}</div> */}
          <div className="step-title">{firstStepHeader}</div>
          <div className="step-detail">
            <div>
              <span className="step-detail-text">{firstStepTip}</span>
            </div>
            {vipRedeemCodeLocation && (
              <a
                className="redeem-code"
                href={rebuildCommonUrl(cashierLangPkg.redeem_url)}
                target="_blank"
                rel="noreferrer"
                rseat="vip_exchange"
                data-pb={`rpage=cashier_popup&block=extend_info&${pbInfoStr}`}
              >
                {/* <RedeemIcon /> */}
                {vipRedeemCodeLocation}
              </a>
            )}
          </div>
          {error && (
            // <div className="error">
            //   <AlertIcon />
            //   {error}
            // </div>
            <div className={'bank-pay-err'}>
              <i className="err-img" />
              <span className="err-desc">{error}</span>
            </div>
          )}
          <Slider
            pageNum={
              isPopupMixed
                ? vipTypes.length / 2
                : vipPkgList.length / (getViewWidth() < 1024 ? 2 : 3)
            }
            pbInfoStr={pbInfoStr}
            handlePageChange={sendPkgBlockPb}
          >
            {isPopupMixed ? (
              <GroupPackages
                vipPkgList={vipPkgList}
                vipTypes={vipTypes}
                storeNodeLocations={storeNodeLocations}
                handleSelect={setSelectPkg}
                selectId={selectedPkg.id}
                singleType={singleType}
                pbInfo={pbInfo}
              />
            ) : (
              <TilePackages
                vipPkgList={vipPkgList}
                handleSelect={setSelectPkg}
                selectedPkg={selectedPkg}
                pbInfo={pbInfo}
              />
            )}
          </Slider>
        </div>
      </div>
      <button
        type="button"
        className="containue"
        onClick={handleNextStep}
        data-pb={`rpage=cashier_popup&block=extend_info&${pbInfoStr}`}
        rseat="continue"
      >
        {vipLangPkg.pcashier_plan_continue}
      </button>
      {firstStepBottomLine ? (
        <div className="bottom-text">{firstStepBottomLine}</div>
      ) : (
        ''
      )}
    </Style>
  )
}

export default VipOrder

