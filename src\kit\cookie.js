import { parseCookies, setCookie } from 'nookies'
import { isServer } from '@/kit/device'

const isValid<PERSON><PERSON> = key => {
  // http://www.w3.org/Protocols/rfc2109/rfc2109
  // Syntax:  General
  // The two state management headers, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>, have common
  // syntactic properties involving attribute-value pairs.  The following
  // grammar uses the notation, and tokens DIGIT (decimal digits) and
  // token (informally, a sequence of non-special, non-white space
  // characters) from the HTTP/1.1 specification [RFC 2068] to describe
  // their syntax.
  // av-pairs   = av-pair *(";" av-pair)
  // av-pair    = attr ["=" value] ; optional value
  // attr       = token
  // value      = word
  // word       = token | quoted-string

  // http://www.ietf.org/rfc/rfc2068.txt
  // token      = 1*<any CHAR except CTLs or tspecials>
  // CHAR       = <any US-ASCII character (octets 0 - 127)>
  // CTL        = <any US-ASCII control character
  //              (octets 0 - 31) and DEL (127)>
  // tspecials  = "(" | ")" | "<" | ">" | "@"
  //              | "," | ";" | ":" | "\" | <">
  //              | "/" | "[" | "]" | "?" | "="
  //              | "{" | "}" | SP | HT
  // SP         = <US-ASCII SP, space (32)>
  // HT         = <US-ASCII HT, horizontal-tab (9)>

  return new RegExp(
    '^[^\\x00-\\x20\\x7f\\(\\)<>@,;:\\\\\\"\\[\\]\\?=\\{\\}\\/\\u0080-\\uffff]+\x24'
  ).test(key)
}

const getRaw = key => {
  if (!isServer && isValidKey(key)) {
    const reg = new RegExp('(^| )' + key + '=([^;]*)(;|\x24)')
    const result = reg.exec(document.cookie)
    if (result) {
      return result[2] || null
    }
  }

  return null
}

const setRaw = (key, value, options) => {
  if (isServer || !isValidKey(key)) {
    return
  }

  options = options || {}
  let expires = options.expires
  if (typeof options.expires === 'number') {
    expires = new Date()
    expires.setTime(expires.getTime() + options.expires)
  }

  document.cookie =
    key +
    '=' +
    value +
    (options.path ? '; path=' + options.path : '') +
    (expires ? '; expires=' + expires.toGMTString() : '') +
    (options.domain ? '; domain=' + options.domain : '') +
    (options.secure ? '; secure' : '')
}

const setCookie005 = () => {
  const chars = [
    '0',
    '1',
    '2',
    '3',
    '4',
    '5',
    '6',
    '7',
    '8',
    '9',
    'a',
    'b',
    'c',
    'd',
    'e',
    'f'
  ]
  let nums = ''
  const expiresDate = new Date()
  expiresDate.setTime(expiresDate.getTime() + 90 * 365 * 24 * 3600 * 1000)
  for (let i = 0; i < 32; i++) {
    const id = Math.floor(Math.random() * 16)
    nums += chars[id]
  }
  return nums
}

export const getCookies = (key, ctx) => {
  let cookies
  if (isServer) {
    cookies = parseCookies(ctx)[key]
  } else {
    const value = getRaw(key)
    if (typeof value === 'string') {
      cookies = decodeURIComponent(value)
    }
  }

  if (!cookies && key === 'QC005') {
    cookies = setCookie005()
  }

  return cookies
}

export const domainName = () => {
  let host = ''
  if (isServer) {
    // host = global.hostName // 这里拿到的online.qiyi.qae，所以手动改成'.iqiyi.com'
    host = '.iq.com'
  } else {
    host = window.location.hostname
  }
  host = host.split(':')[0]
  const domain = host
    .split('.')
    .splice(1)
    .join('.')
  return domain
}

export const setCookies = (key, value, options = {}, ctx) => {
  options.domain = domainName()
  options.path = options.path || '/'
  if (isServer) {
    if (options.time) {
      options.maxAge = options.time / 1000
    }
    ctx = ctx || global.ctx
    setCookie(ctx, key, value, options)
  } else {
    if (options.time) {
      options.expires = options.time
    }

    setRaw(key, encodeURIComponent(value), options)
  }
}

// 客户端删除Cookie
export const delClientCookies = (key, options = {}) => {
  if (isServer || !isValidKey(key)) {
    return
  }
  options.domain = domainName()
  options.path = options.path || '/'
  const expires = new Date()
  expires.setTime(expires.getTime() - 1000)

  document.cookie =
    key +
    '=' +
    '' +
    (options.path ? '; path=' + options.path : '') +
    (expires ? '; expires=' + expires.toGMTString() : '') +
    (options.domain ? '; domain=' + options.domain : '') +
    (options.secure ? '; secure' : '')
}
