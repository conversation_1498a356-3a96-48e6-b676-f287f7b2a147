{"name": "@iqiyi-ibd/global-vip-sdk", "version": "1.3.0", "description": "", "main": "dist/index.js", "files": ["dist"], "scripts": {"start": "cross-env NODE_ENV=development webpack-dev-server --hot", "dev": "cross-env NODE_ENV=development webpack-dev-server --hot", "build": "cross-env NODE_ENV='production' webpack", "watch": "cross-env NODE_ENV='production' webpack --watch", "ci:dev": "sh script/ci.sh dev", "ci:prodmaster": "sh script/ci.sh prod"}, "repository": {"type": "git", "url": "ssh://**********************:10022/tp-tw/web/global-vip-sdk.git"}, "author": "<EMAIL>", "license": "MIT", "dependencies": {"@babel/plugin-proposal-private-methods": "^7.18.6", "@babel/runtime": "^7.12.5", "@babel/runtime-corejs2": "^7.12.5", "@iqiyi/qdsf": "^1.1.1", "axios": "^0.21.1", "md5": "^2.3.0", "nookies": "^2.0.6", "prettier": "^3.4.2", "prop-types": "^15.8.1", "qs": "^6.10.1", "react": "^17.0.1", "react-dom": "^17.0.1", "react-intersection-observer": "^9.4.0", "react-load-script": "^0.0.6", "react-redux": "^8.0.5", "react-transition-group": "^4.4.5", "redux": "^4.2.0", "redux-thunk": "^2.4.2", "styled-components": "^5.2.1"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@babel/plugin-transform-runtime": "^7.12.10", "@babel/preset-env": "^7.12.11", "@babel/preset-react": "^7.12.10", "@iqiyi-ibd/global-login-sdk": "^0.3.56", "babel-loader": "^8.2.2", "babel-plugin-named-asset-import": "^0.3.7", "clean-webpack-plugin": "^3.0.0", "cross-env": "^7.0.3", "css-loader": "^5.0.1", "eslint": "^7.17.0", "eslint-loader": "^4.0.2", "file-loader": "^6.2.0", "html-webpack-plugin": "^4.5.1", "less": "^4.1.0", "less-loader": "^7.2.1", "linaria": "^3.0.0-beta.1", "mini-css-extract-plugin": "^1.3.3", "paths": "^0.1.1", "postcss-flexbugs-fixes": "^4.0.1", "postcss-loader": "^3.0.0", "postcss-normalize": "^7.0.1", "postcss-preset-env": "^6.7.0", "react-dev-utils": "^11.0.1", "resolve-url-loader": "^3.1.2", "sass-loader": "^10.1.1", "style-loader": "^2.0.0", "url-loader": "^4.1.1", "webpack": "^4.39.1", "webpack-cli": "^3.3.12", "webpack-dev-server": "^3.11.1", "webpack-node-externals": "^2.5.2"}}