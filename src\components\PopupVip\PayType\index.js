import React from 'react'
import qs from 'qs'

import { removeProtocol } from '@/kit/url'
import { Checkbox, CheckboxError, CheckboxSelected } from '@/components/svgs'
import { sendBlockPb } from '@/utils/pingBack'
import PayTypeWrapper from './style'

class PayType extends React.Component {
  constructor(props) {
    super(props)
    this.state = {}
  }

  componentDidMount() {
    sendBlockPb('pay_type', {
      bstp: 56,
      tjPb: {
        ...this.props.pbInfo
      }
    })
    this.props.currentPayTypeOptions.forEach((item, index) => {
      sendBlockPb('pay_type_select', {
        bstp: 56,
        rpage: 'pay_type',
        tjPb: {
          ...this.props.pbInfo,
          position: index,
          pay_type: item.payType
        }
      })
    })

  }

  render() {
    const {
      vipLangPkg,
      vipInfo,
      currentPayTypeOptions,
      error,
      isAgree,
      setIsAgree,
      checkErr,
      changePayType,
      continueBtnHandle,
      pbInfo
    } = this.props
    const {
      secondStepHeader,
      secondStepTip,
      vipServiceAgreement,
      autorenewTermsAndConditions
    } = vipInfo
    const selectedPayType = currentPayTypeOptions.find(item => item.recommend) || {}
    const selectedPayTypeIndex = currentPayTypeOptions.findIndex(item => item.recommend)
    const pbInfoStr = qs.stringify(pbInfo) + '&bstp=56'
    return (
      <PayTypeWrapper>
        {/* <div className="step-2">{vipLangPkg.pcashier_payment_stepTitle}</div> */}
        <div className="scroll-container">
          <div className="scroll-content">
            <div className="step-title">{secondStepHeader}</div>
            <div className="step-tip">{secondStepTip}</div>
            {(error === '') ? null : (
              <div className={`bank-pay-err ${error === '' ? 'dn' : ''}`}>
                <i className="err-img" />
                <span className="err-desc">{error}</span>
              </div>
            )}
            <div className="paytype-center">
              {currentPayTypeOptions.map((item, index) => {
                return (
                  <div className="btn-wrap" key={index}>
                    <div
                      role="button"
                      tabIndex="0"
                      key={item.name}
                      className={`type-item ${item.recommend === 1 || currentPayTypeOptions.length === 1
                        ? ' selected'
                        : ''
                        }`}
                      onClick={() => changePayType(index)}
                      rseat={`${selectedPayTypeIndex}:${index}`}
                      data-pb={`rpage=pay_type&block=pay_type_select&${qs.stringify({...pbInfo, position: index, pay_type: item.payType})}`}
                    >
                      <img
                        alt=""
                        src={removeProtocol(item.iconUrl)}
                        rseat={`${selectedPayTypeIndex}:${index}`}
                        data-pb={`rpage=pay_type&block=pay_type_select&${qs.stringify({...pbInfo, position: index, pay_type: item.payType})}`}
                      />
                      <span className="name">{item.name}</span>
                      <span className="des">{item.subPromotion}</span>
                    </div>
                    {item.promotion && (
                      <p className="label">
                        <span>{item.promotion}</span>
                      </p>
                    )}
                  </div>
                )
              })}
              {/* <div className="more-options">
                <a
                  target="_blank"
                  rel="noopener noreferrer"
                  rseat="more_payment_collection"
                  data-pb={`rpage=web_popup_casher_pay&block=extend_info&bstp=56&abtest=${abtest}&fc=${params.fc || ''}&fv=${params.fv || ''}`}
                  href={feedbackUrl}
                >
                  {vipLangPkg.pcashier_payment_moreOptions}
                </a>
              </div> */}
            </div>
          </div>
        </div>
        {autorenewTermsAndConditions && autorenewTermsAndConditions.text && (
          <p className="autorenew-terms">{autorenewTermsAndConditions.text}</p>
        )}
        {vipServiceAgreement && (
          <>
            <div className="cash-agreement">
              <div
                className={`icon-check ${!checkErr && !isAgree ? 'nomal' : (isAgree ? 'checked' : 'error')}`}
                role="button"
                tabIndex="0"
                onClick={e => {
                  e.preventDefault()
                  setIsAgree(!isAgree)
                }}
              >
                {/* {!checkErr && !isAgree && <Checkbox />}
                {isAgree && <CheckboxSelected />}
                {checkErr && <CheckboxError />} */}
              </div>
              {vipLangPkg.pcashier_payment_iagree}
              <a
                className="service"
                target="_blank"
                rel="noopener noreferrer"
                href={vipServiceAgreement.url}
              >
                {vipServiceAgreement.text}
              </a>
            </div>
            {checkErr && (
              <div className="checkout-error">
                {vipLangPkg.pcashier_payment_iagree_notice}
              </div>
            )}
          </>
        )}
        <button
          type="submit"
          className="containue"
          rseat="continue"
          data-pb={`rpage=pay_type&block=extend_info&${pbInfoStr}`}
          onClick={continueBtnHandle}
        >
          {vipLangPkg.pcashier_plan_continue}
        </button>
      </PayTypeWrapper>
    )
  }
}

export default PayType
