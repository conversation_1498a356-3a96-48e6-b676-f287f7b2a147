/**
 * @file sdk加载器
 */

import $script from 'scriptjs'

const scripts = {}
const getter = (item, type) => {
    return typeof item === 'string' ? item : item[type]
}

/**
 * 加载sdk
 * @param  {Object}   sdk
 *         {url: String, sdk的加载地址}
 * @param  {Object}   options
 *         {loadAlways: Boolan, 是否总是加载。如果为false,则如果判断加载过一次该sdk，就不会再加载了}
 * @param  {Function} callback 回调函数，skd加载完毕后将会执行的函数，该函数没有任何参数
 * @return {void}
 */
export const loadSDK = (sdk, options = { loadAlways: false }, callback) => {
    if (options.loadAlways || !scripts[sdk.url]) {
        $script(sdk.url, () => {
            scripts[sdk.url] = sdk
            if (callback) {
                callback()
            }
        })
    } else if (callback) {
        callback()
    }
}
/*
    获取组合对象别名
 */
const createAlias = item => {
    const alias = getter(item, 'alias')
    const moduleName = getter(item, 'name')
    return {
        alias,
        moduleName,
    }
}
/*
    粘合方法
 */
const combiner = sdk => {
    const res = {}
    if (sdk.modules && sdk.modules.length) {
        sdk.modules.forEach(item => {
            const { alias, moduleName } = createAlias(item)
            if (window[moduleName]) {
                res[alias] = window[moduleName]
            }
        })
    }
    return res
}

/*
    组合新实例
 */
const combinerInstance = sdk => {
    const res = {}
    if (sdk.modules && sdk.modules.length) {
        sdk.modules.forEach(item => {
            const { alias, moduleName } = createAlias(item)
            if (window[moduleName]) {
                let Instance
                if (typeof window[moduleName] === 'object') {
                    Instance = function() {}
                    Instance.prototype = window[moduleName]
                    res[alias] = new Instance()
                } else if (typeof window[moduleName] === 'function') {
                    Instance = function() {
                        return window[moduleName].apply(this, arguments)
                    }
                    res[alias] = Instance
                }
            }
        })
    }
    return res
}
/*
    批量加载
 */
const loadBatch = (list, options = { loadAlways: false }) => {
    const defs = []
    let _mods = []
    list.forEach(item => {
        if (item.modules) {
            _mods = [..._mods, ...item.modules]
        }
        defs.push(
            new Promise(resolve => {
                loadSDK(item, options, () => {
                    if (options.newInstance) {
                        resolve(combinerInstance(item))
                    } else {
                        resolve(combiner(item))
                    }
                })
            })
        )
    })
    return Promise.all(defs).then(data => {
        const res = {}
        data.forEach(item => {
            Object.keys(item).forEach(function(k) {
                res[k] = item[k]
            })
        })
        return Promise.resolve(res)
    })
}

/*
    sdk适配器
 */
export const sdkAdpater = {
    /*
        get 获取sdk
        @return 返回
     */
    get(sdk, options = { loadAlways: false }) {
        if (!Array.isArray(sdk)) {
            return new Promise(resolve => {
                loadSDK(sdk, options, () => {
                    // 返回新实例
                    if (options.newInstance) {
                        resolve(combinerInstance(sdk))
                    } else {
                        resolve(combiner(sdk))
                    }
                })
            })
        }
        return loadBatch(sdk, options)
    },
}
