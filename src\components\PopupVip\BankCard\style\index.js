import styled from 'styled-components'

const largeWidth = '504px'
const middleWidth = '532px';

const BankCardWrapper = styled.div`
  div {
    margin: 0;
    padding: 0;
    font-size: 0;
  }
  input:focus{
    outline: none;
  }
  .info {
    width: 370px;
  }
  .vip-info {
    box-sizing: border-box;
    position: relative;
    border-bottom: 1px solid #e6e6e6;
    width: ${largeWidth};
    /* height: 96px; */
    border-radius: 4px;
    margin: 16px auto 0;
    padding: 16px 0;
    .name {
      line-height: 16px;
      font-size: 14px;
      color: #222222;
      font-weight: 700;
    }
    .desc {
      margin-top: 4px;
      font-size: 14px;
      color: #666666;
      line-height: 15px;
      font-weight: 400;
    }
    .vip-price {
      width: 120px;
      margin-left: 12px;
      position: absolute;
      text-align: right;
      top: 16px;
      right: 0px;  
      .symbol {
        text-align: right;
        font-size: 14px;
        color: #222222;
        letter-spacing: 0;
        line-height: 18px;
        font-weight: 700;
      }
      .price {
        font-size: 18px;
        color: #222222;
        letter-spacing: 0;
        line-height: 21px;
        font-weight: 700;
      }
      .price-margin {
        margin-right: 4px;
      }
    }
    .vip-original-price {
      width: 120px;
      margin-left: 12px;
      text-decoration: line-through;
      position: absolute;
      bottom: 16px;
      right: 0px;
      font-size: 12px;
      color: #999999;
      letter-spacing: 0;
      font-weight: 400;
      line-height: 14px;
      text-align: right;
      .price-margin {
        margin-right: 2px;
      }
    }
  }
  .bank-pay-err {
    box-sizing: border-box;
    width: ${largeWidth};
    min-height: 44px;
    background: #FEEBEB;
    border: 1px solid #FEC5C2;
    border-radius: 6px;
    margin: 12px auto;
    padding: 12px;
    display: flex;
    align-items: center;
    .err-img {
      flex-shrink: 0;
      display: inline-block;
      vertical-align: middle;
      width: 20px;
      height: 20px;
      background: url('//www.iqiyipic.com/lequ/********/icon_error.png') no-repeat;
      background-size: cover;
      margin-right: 8px;
    }
    .err-desc {
      line-height: 16px;
      font-size: 14px;
      color: #FE3D33;
      letter-spacing: 0;
      font-weight: 400;
      vertical-align: middle;
    }
  }
  .bank-card-content {
    box-sizing: border-box;
    margin: 0 auto;
    /* overflow: hidden; */
    width: ${largeWidth};
    border-radius: 4px;
  }
  .bank-img {
    display: flex;
    justify-content: space-between;
    padding: 16px 0;
    img {
      display: inline-block;
      width: 36px;
      height: 24px;
      margin-right: 12px;
    }
    & > img {
      width: 56px;
      height: 24px;
    }
  }
  .title {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    line-height: 16px;
    font-size: 14px;
    color: #222222;
    letter-spacing: 0;
    font-weight: 400;
  }
  .input-cardNum {
    box-sizing: border-box;
    height: 44px;
    width: ${largeWidth};
    border: 1px solid #E6E6E6;
    border-radius: 4px;
    font-size: 16px;
    color: rgb(34, 34, 34);
    padding: 12px;
    &:focus {
      border-color: #EDB97B;
    }
  }
  .input-expiry,
  .input-cvc {
    width: 156px;
    box-sizing: border-box;
    height: 44px;
    border: 1px solid #E6E6E6;
    border-radius: 4px;
    font-size: 16px;
    color: rgb(34, 34, 34);
    padding: 12px;
    &:focus {
      border-color: #EDB97B;
    }
  }
  .row {
    display: flex;
    margin-top: 12px;
    padding-bottom: 16px;
  }
  .expiry-row {
    display: flex;
  }
  .two-column {
    width: 156px;
    overflow: hidden;
  }
  .slash {
    width: 20px;
    font-size: 14px;
    color: #222222;
    line-height: 45px;
    text-align: center;
  }
  .cvc-block {
    margin-left: 16px;
  }
  .bankcard-err {
    display: none;
    font-size: 14px;
    color: #FE3D33;
    line-height: 16px;
    margin-top: 8px;
    width: 504px;
    font-weight: 400;
  }
  .other-err {
    display: none;
    font-size: 14px;
    color: #FE3D33;
    line-height: 16px;
    margin-top: 8px;
    width: 156px;
    font-weight: 400;
  }
  .state-err {
    input {
      border-color: #FE3D33;
    }
    .bankcard-err,
    .other-err {
      display: block;
    }
  }

  .disabled {
    cursor: no-drop;
  }
  .tip {
    font-size: 12px;
    color: #666666;
    width: ${largeWidth};
    margin: 0 auto;
  }
  .bank-bubble {
    display: inline-block;
    vertical-align: middle;
    margin-left: 2px;
    .icon-bubble {
      display: inline-block;
      vertical-align: middle;
      margin-left: 2px;
      position: relative;
      /* top: 1px; */
      display: inline-block;
      width: 16px;
      height: 16px;
      background: url('//www.iqiyipic.com/lequ/********/icon_info.png') no-repeat;
      background-size: cover;
      cursor: pointer;
    }
    .bubble-desc {
      display: none;
      &:after {
        position: absolute;
        display: inline-block;
        bottom: -8px;
        left: 33px;
        width: 0;
        height: 0px;
        content: '';
        border-style: solid;
        border-width: 8px;
        border-color: #fff #fff transparent transparent;
        transform: rotate(135deg);
        box-shadow: 2px -2px 4px 0 rgba(0, 0, 0, 0.1);
      }
    }
    .desc-show {
      position: absolute;
      left: -30px;
      bottom: 32px;
      box-sizing: border-box;
      width: 164px;
      padding: 16px;
      border-radius: 4px;
      background-color: #fff;
      box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.24);
    }
    .bank-bubble-txt {
      color: #202329;
      font-size: 12px;
      line-height: 20px;
      margin-bottom: 10px;
    }
    .bank-bubble-pic {
      display: block;
      width: 75px;
      height: 48px;
    }
    &:hover {
      .bubble-desc {
        display: block;
      }
    }
  }
  @media screen and (max-width: 1023px) {
    .info {
      width: 400px;
    }
    .vip-info,
    .bank-pay-err,
    .bank-card-content,
    .input-cardNum,
    .bankcard-err,
    .tip {
      width: ${middleWidth}
    }
    .input-expiry,
    .input-cvc,
    .two-column,
    .other-err {
      width: 165px;
    }
    /* .row {
      margin-bottom: 80px;
    } */
  }
`
export default BankCardWrapper
