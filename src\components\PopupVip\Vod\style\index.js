import styled from 'styled-components'

const Style = styled.div`
  .error {
    line-height: 44px;
    height: 44px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    padding: 0 16px;
    background: #FEEBEB;
    border: 1px solid #FE3D33;
    border-radius: 4px;
    font-size: 14px;
    color: #FE3D33;
    margin-bottom: 8px;
    svg {
      fill: #FE3D33;
      margin-right: 8px;
    }
  }
  .error-h5{
    display: none
  }
  .scroll-box {
    max-height: calc(85vh - 40px - 108px);
    overflow-y: auto;
    margin-bottom: 32px;
    padding: 0px 80px;
    box-sizing: border-box;
  }
  .main-box {
    padding: 40px;
    background: #fff;
    box-shadow: 0 2px 4px 0 rgba(0,0,0,0.05);
    border-radius: 4px;
  }

  .title {
    line-height: 29px;
    font-size: 24px;
    color: #222222;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-weight: bold;
  }
  .price {
    margin-top: 24px;
    padding-bottom: 24px;
    display: flex;
    align-items: baseline;
    font-size: 32px;
    color: #222222;
    font-weight: bold;
    line-height: 44px;
    border-bottom: 1px solid #E6E6E6;
  }
  .origin-price {
    margin-left: 8px;
    font-size: 14px;
    color: #999999;
    font-weight: normal;
    text-decoration: line-through;
  }
  .time {
    padding: 24px 0;
    border-bottom: 1px solid #E6E6E6;
    font-size: 16px;
    color: #222222;
    line-height: 19px;
  }
  .label {
    line-height: 16px;
    margin: 24px 0 8px;
    font-size: 14px;
    color: #999999;
  }
  .paytype-card {
    display: flex;
    align-items: center;
    padding: 12px 16px 12px 14px;
    box-sizing: border-box;
    min-height: 56px;
    border: 1px solid #E6E6E6;
    border-radius: 4px;
    margin-bottom: 8px;
    &:last-child {
      margin-bottom: 0
    }
    &.selected {
      border: 1px solid #222;
      .select-icon {
        border: 5px solid #222;
      }
    }
    &:hover {
      background: #F9F9F9;
    }
  }
  .card-icon {
    width: 28px;
    height: 28px;
    margin-right: 12px;
    border-radius: 6px;
    border: 1px solid #E6E6E6;
    box-sizing: border-box;
  }
  .card-main {
    flex-grow: 1
  }
  .card-name {
    font-size: 16px;
    color: #222222;
  }
  .card-promotion {
    margin-top: 2px;
    font-size: 12px;
    color: #E09E51;
  }
  .select-icon {
    box-sizing: border-box;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 1px solid #E6E6E6;
  }
  @media screen and (max-width: 1919px) {
    .scroll-box {
      padding: 0px 64px;
    }
    .main-box {
      padding: 32px 24px;
    }
    .price {
      padding-bottom: 32px;
    }
  }
  @media screen and (max-width: 1023px) {
    .main-box {
      padding: 32px 24px 40px;
    }
  }
  @media screen and (max-width: 767px) {
    .error {
      display: none
    }
    .error-h5 {
      display: block;
      width: 256px;
      margin: 0 auto 8px;
      font-size: 13px;
      color: #FE3D33;
    }
    .scroll-box {
      height: 245px;
      margin-bottom: 16px;
      padding: 0;
      &.iserror {
        height: 215px;
        margin-bottom: 8px;
      }
    }
    .main-box {
      margin: 0;
      padding: 0 15px;
      box-shadow: none;
    }
    .title {
      font-size: 16px;
    }
    .price {
      margin-top: 16px;
      padding-bottom: 12px;
      font-size: 22px;
      line-height: 30px;
      align-items: center
    }
    .origin-price {
      font-size: 13px;
    }
    .time {
      padding: 0 0 16px;
      border-top: none;
      font-size: 13px;
      line-height: 15px;
    }
    .label {
      line-height: 15px;
      margin: 16px 0 4px;
      font-size: 13px;
    }
    .paytype-card {
      display: flex;
      align-items: center;
      padding: 13px 0 11px;
      border: none;
      border-bottom: 1px solid #E6E6E6;
      border-radius: 0;
      margin-bottom: 0;
      &.selected {
        border: none;
        border-bottom: 1px solid #E6E6E6;
      }
      &:hover {
        background: none
      }
    }
    .card-name {
      font-size: 13px;
    }
  }
`
export default Style