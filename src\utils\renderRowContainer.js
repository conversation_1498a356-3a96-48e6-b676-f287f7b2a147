import React from 'react'
import RowBlock from '@/components/common/RowBlock'
import HotPlay from '@/components/pages/HotPlay'
import VipAdInfo from '@/components/common/Vip/VipAdInfo'
import ChannelRecommend from '@/components/pages/channel/ChannelRecommend'
import HomeHistory from '@/components/pages/home/<USER>'
// import VarietyRecommend from '@/components/pages/channel/VarietyRecommend'

function array2Json(arr) {
  const json = {}
  arr.forEach(item => {
    if (item.id) {
      json[item.id] = item
    }
  })
  return json
}

// 浅拷贝，解决问题：若配置的资源位相同，res-batch 会干掉相同的，只保留一份。导致修改rowName会相互影响
function simpleClone(initalObj) {
  const obj = {}
  for (const i in initalObj) {
    obj[i] = initalObj[i]
  }
  return obj
}

// 渲染row栏
/*
  @params param.rpage 
  @params param.resources 页面排版
  @params param.intlDataCol 数据
  @params param.channelRecommendList Recommend数据
  @params param.historyData historyData数据
*/
export function renderRowContainer({
  rpage = 'home',
  resources = [],
  intlDataCol = [],
  channelRecommendList,
  historyData
} = {}) {
  const intlDataColJson = array2Json(intlDataCol)
  return resources.map((item, index) => {
    const curData = simpleClone(intlDataColJson[item.id])

    if (!item) {
      // 无数据直接返回
      return ''
    }

    if (curData) {
      curData['rowName'] = item['name']
      curData['rpage'] = rpage
    }

    if (item.type === 'NormalPortrait') {
      if (index === resources.length - 1 && !item.id) {
        // 频道页为你推荐 最后一条可能没有配置id

        return (
          <ChannelRecommend
            imgType="3"
            hoverType="horizontal"
            rowName={item['name']}
            channelId="2"
            block={`${rpage}_recommend`}
            rpage={rpage}
            channelRecommendList={channelRecommendList}
            gtagCategory={`${rpage}_${resources[index]['type']}`}
          />
        )
      }
      // 热门推荐 xxx精选等
      if (curData && curData.videos && curData.videos.length) {
        return (
          <RowBlock
            imgType="1"
            hoverType="horizontal"
            data={curData}
            blockName={`R:${item.id}`}
            gtagCategory={`${rpage}_${resources[index]['type']}`}
            lazyLoad="true"
            key={curData.rowName}
          />
        )
      }
      return ''
    } else if (item.type === 'ContinueWatch') {
      // 继续看
      return (
        <HomeHistory
          rpage="home"
          data={historyData}
          gtagCategory="home_continue_watch"
          key={historyData.rowName}
        />
      )
    } else if (item.type === 'Hot' && item.id) {
      // 人气榜
      if (curData && curData.videos && curData.videos.length) {
        return (
          <HotPlay
            data={curData}
            gtagCategory={`${rpage}_${resources[index]['type']}`}
            key={curData.rowName}
          />
        )
      }
      return ''
    } else if (item.type === 'VipBanner') {
      return <VipAdInfo interfaceCode="banner" channelId="2" rpage="drama" />
    } else if (item.type === 'NormalLandscape') {
      if (item.id) {
        return (
          <RowBlock
            imgType="2"
            hoverType="normal"
            data={curData}
            blockName={`R:${item.id}`}
            gtagCategory={`${rpage}_${resources[index]['type']}`}
            lazyLoad="true"
            key={curData.rowName}
          />
        )
      }

      if (index === resources.length - 1 && !item.id) {
        // 频道页为你推荐 最后一条可能没有配置id
        return (
          <ChannelRecommend
            channelRecommendList={channelRecommendList}
            rowName={item['name']}
            imgType="4"
            hoverType="normal"
            gtagCategory="variety_show_normal_landscape"
            rpage={rpage}
            block={`${rpage}_recommend`}
          />
        )
      }
      return ''
    } else {
      return ''
    }
  })
}
