import styled from 'styled-components'

const Style = styled.div`
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
  /* height: 96px; */
  padding: 17px 0;
  border-bottom: 1px solid #E6E6E6;
  .text {
    max-width: 70%;
  }
  .name {
    font-size: 16px;
    color: #222;
    line-height: 16px;
    font-weight: bold;
  }
  .desc {
    margin-top: 4px;
    font-size: 14px;
    color: #666666;
    line-height: 16px;
  }
  .vip-price {
    display: flex;
    align-items: center;
    color: #222;
    font-weight: bold;
    .symbol {
      font-size: 14px;
      text-align: right;
      line-height: 10px;
    }
    .price {
      font-size: 18px;
      text-align: right;
      line-height: 21px;
    }
  }
  .vip-original-price {
    margin-top: 2px;
    text-decoration: line-through;
    font-size: 12px;
    color: #999999;
    line-height: 14px;
    text-align: right;
  }
  @media screen and (max-width: 1023px) {
    width: 532px;
    
  }
  @media screen and (max-width: 767px) {
    width: auto;
  }
`
export default Style
