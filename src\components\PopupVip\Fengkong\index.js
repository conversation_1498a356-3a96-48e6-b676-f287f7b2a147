import React from 'react'
import Wrapper from './style/style'
const jsList = [
  {
    url:
      // 'https://security.iq.com/static/intl/verifycenter/js/verifycenter.js',
      'https://security.iq.com/static/iq/verifycenter/js/verifycenter.js'
  }
]
const cssList = [
  {
    url:
      // 'https://security.iq.com/static/intl/verifycenter/css/verifycenter.css',
      'https://security.iqiyi.com/static/iq/v2/verifycenter/css/verifycenter.css'
  }
]
class Fengkong extends React.Component {
  state = {
    verifySDKInstance: null,
    isLoading: true
  }

  componentDidMount() {
    this.initSDK()
  }

  newVerifyCenter = () => {
    const { verifySDKInstance } = this.state
    const { ptid, token, id, verifySliderSus, dfp, lang } = this.props
    const _this = this
    const baseParams = {
      wrapper: document && document.getElementById(id),
      type: false,
      toolbar: "refresh",
      callback(data) {
        if (data.code === 'A00000') {
          // 要remove 验证中心sdk实例
          if (verifySDKInstance) {
            verifySDKInstance.remove()
          }
          verifySliderSus && verifySliderSus(data.token)
        }
      },
      DomLoadCallback() {
        _this.setState({ isLoading: false })
      },
      closeCallback() {
        if (verifySDKInstance) {
          verifySDKInstance.remove()
        }
      },

      ptid,
      agentType: 479, // 和主站h5保持一致
      token,
      dfp: dfp || (window.dfp && window.dfp.tryGetFingerPrint()) || '',
      language: lang,
      width: 320,
      height: 188,
      zoom: 1.2,
      el: document && document.getElementById(id),
    }
    if (verifySDKInstance) {
      verifySDKInstance.remove()
    }
    // console.log('到这一步了======', window.VerifyCenter)
    if (window.VerifyCenter) {
      this.setState({ verifySDKInstance: new window.VerifyCenter(baseParams) })
    }
  }

  initSDK = async () => {
    // console.log('owowowowowwowowowo');
    if (window.VerifyCenter) {
      // console.log('这里执行的位置')
      this.newVerifyCenter()
    } else {
      // let getCss = null
      // let getJS = null
      // let css = await import('@/utils/cssLoader')
      // let js = await import('@/utils/sdkAdapter')
      // getCss = css.cssLoader.get(cssList)
      // getJS = js.sdkAdpater.get(jsList)
      // import('@/utils/cssLoader').then((e) => {
      //   console.log(e, '============****888888***')
      //   getCss = e.cssLoader.get(cssList)
      //   resolve()
      // })
      // then((e) => {
      //   console.log(e, '============****999999***')
      //   getJS = e.sdkAdpater.get(jsList)
      //   resolve()
      // })
      //       import { cssLoader } from '@/utils/cssLoader'
      // import { sdkAdpater } from '@/utils/sdkAdapter'
      //       const getCss = cssLoader.get(cssList)
      //       const getJS = sdkAdpater.get(jsList)
      // const urlArray = [getCss, getJS]
      // Promise.all(urlArray).then((data) => {
      //   console.log(data, '========////////')
      //   this.newVerifyCenter()
      // })
    }
  }

  render() {
    const { isLoading } = this.state
    return isLoading ? (
      <Wrapper>
        <div className="passport-loading">
          <span className="icon-passport icon-passport-outside__loading passport-loading-outside__img" />
          <span className="passport-loading-outside__txt">Loading...</span>
        </div>
      </Wrapper>
    ) : null
  }
}
export default Fengkong
