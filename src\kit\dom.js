/*
    dom 类方法
 */

/*
    getScrollTop    获取元素滚动条距离顶部位置
    @param  element dom元素
 */
import Browser from '@/kit/browser'
import { isServer } from '@/kit/device'

export const getElmScrollTop = element => {
  if (element === window) {
    return Math.max(window.pageYOffset || 0, document.documentElement.scrollTop)
  }
  return element.scrollTop
}

/*
    getComputedStyle    获取计算后样式属性
 */
export const getComputedStyle = () => {
  return document.defaultView.getComputedStyle
}

/*
    getScrollEventTarget    获取滚动目标
 */
export const getScrollEventTarget = element => {
  let currentNode = element

  while (
    currentNode &&
    currentNode.tagName !== 'HTML' &&
    currentNode.tagName !== 'BODY' &&
    currentNode.nodeType === 1
  ) {
    const overflowY = getComputedStyle(currentNode).overflowY
    if (overflowY === 'scroll' || overflowY === 'auto') {
      return currentNode
    }
    currentNode = currentNode.parentNode
  }
  return window
}

/*
    getVisibleHeight    获取可视区高度
 */
export const getVisibleHeight = element => {
  if (element === window) {
    return document.documentElement.clientHeight
  }

  return element.clientHeight
}

/*
    isAttached  判断元素是否已经创建
 */
export const isAttached = element => {
  let currentNode = element.parentNode
  while (currentNode) {
    if (currentNode.tagName === 'HTML') {
      return true
    }
    if (currentNode.nodeType === 11) {
      return false
    }
    currentNode = currentNode.parentNode
  }
  return false
}

export const getPosition = element => {
  let box
  try {
    box = element.getBoundingClientRect()
  } catch (e) {
    return {}
  }
  const doc = element.ownerDocument
  const body = doc.body
  const html = doc.documentElement
  const clientTop = html.clientTop || body.clientTop || 0
  const clientLeft = html.clientLeft || body.clientLeft || 0
  const top =
    box.top +
    (window.pageYOffset || html.scrollTop || body.scrollTop) -
    clientTop
  const left =
    box.left +
    (window.pageXOffset || html.scrollLeft || body.scrollLeft) -
    clientLeft
  return { top, left }
}

export const getTop = (el, offsetParent) => {
  if (offsetParent) {
    return getPosition(el).top - getPosition(offsetParent).top
  }
  return getPosition(el).top
}
export const getLeft = (el, offsetParent) => {
  if (offsetParent) {
    return getPosition(el).left - getPosition(offsetParent).left
  }
  return getPosition(el).left
}
export const getWidth = element => {
  return element ? element.offsetWidth : 0
}
export const getHeight = element => {
  return element.offsetHeight
}
export const getViewHeight = () => {
  const doc = document
  const client =
    doc.compatMode === 'BackCompat' ? doc.body : doc.documentElement
  return window.innerHeight || client.clientHeight
}
export const getViewWidth = () => {
  if (isServer) {
    return ''
  }
  const browser = new Browser()
  const doc = document
  const client =
    doc.compatMode === 'BackCompat' ? doc.body : doc.documentElement
  if (browser.SAFARI) {
    return client.clientWidth
  } else {
    return window.innerWidth || client.clientWidth
  }
}
export const getScrollTop = () => {
  const context = global
  const doc = context.document
  // A shortcut, in case we’re using Internet Explorer 6 in Strict Mode
  const de = doc.documentElement
  // If the pageYOffset of the browser is available, use that
  return (
    context.pageYOffset ||
    // Otherwise, try to get the scroll top off of the root node
    (de && de.scrollTop) ||
    // Finally, try to get the scroll top off of the body element
    doc.body.scrollTop
  )
}
export const getScrollLeft = () => {
  const context = global
  const doc = context.document
  // A shortcut, in case we’re using Internet Explorer 6 in Strict Mode
  const de = doc.documentElement
  // If the pageXOffset of the browser is available, use that
  return (
    context.pageXOffset ||
    // Otherwise, try to get the scroll left off of the root node
    (de && de.scrollLeft) ||
    // Finally, try to get the scroll left off of the body element
    doc.body.scrollLeft
  )
}
export const isInScreen = elem => {
  if (!elem) {
    return false
  }
  const top = getTop(elem)
  const left = getLeft(elem)
  const width = getWidth(elem)
  const height = getHeight(elem)
  const bottom = top + height
  const right = left + width
  const centerX = left + width / 2
  const centerY = top + height / 2
  const viewTop = getScrollTop()
  const viewLeft = getScrollLeft()
  const viewRight = viewLeft + getViewWidth()
  const viewBottom = viewTop + getViewHeight()
  // 没有宽和高
  if (!width && !height) {
    return false
  }
  // 左上角可见
  if (
    top >= viewTop &&
    top <= viewBottom &&
    left >= viewLeft &&
    left <= viewRight
  ) {
    return true
  }
  // 右上角可见
  if (
    top >= viewTop &&
    top <= viewBottom &&
    right >= viewLeft &&
    right <= viewRight
  ) {
    return true
  }
  // 左下角可见
  if (
    bottom >= viewTop &&
    bottom <= viewBottom &&
    left >= viewLeft &&
    left <= viewRight
  ) {
    return true
  }
  // 右下角可见
  if (
    bottom >= viewTop &&
    bottom <= viewBottom &&
    right >= viewLeft &&
    right <= viewRight
  ) {
    return true
  }
  // 中心点可见
  if (
    centerY >= viewTop &&
    centerY <= viewBottom &&
    centerX >= viewLeft &&
    centerX <= viewRight
  ) {
    return true
  }
  return false
}

export const addClass = (el, value) => {
  let classNames
  let i
  let l
  let setClass

  if (value && typeof value === 'string') {
    classNames = value.split(/\s+/)
    if (el.nodeType === 1) {
      if (!el.className && classNames.length === 1) {
        el.className = value
      } else {
        setClass = ' ' + el.className + ' '
        for (i = 0, l = classNames.length; i < l; i++) {
          if (!~setClass.indexOf(' ' + classNames[i] + ' ')) {
            setClass += classNames[i] + ' '
          }
        }
        el.className = setClass.trim()
      }
    }
  }
}

export const removeClass = (el, value) => {
  let classNames
  let className
  let i
  let l

  if ((value && typeof value === 'string') || value === undefined) {
    classNames = (value || '').split(/\s+/)

    if (el.nodeType === 1 && el.className) {
      if (value) {
        className = (' ' + el.className + ' ').replace(/[\n\t\r]/g, ' ')
        for (i = 0, l = classNames.length; i < l; i++) {
          className = className.replace(' ' + classNames[i] + ' ', ' ')
        }
        el.className = className.trim()
      } else {
        el.className = ''
      }
    }
  }
}

export const hasClass = (el, selector) => {
  const className = ' ' + selector + ' '
  if (
    el.nodeType === 1 &&
    (' ' + el.className + ' ').replace(/[\n\t\r]/g, ' ').indexOf(className) > -1
  ) {
    return true
  }
  return false
}

export const centerPopCss = el => {
  if (el.style.display === 'none') {
    el.style.display = 'block'
  }
  const width = getWidth(el)
  const height = getHeight(el)
  const viewWidth = getViewWidth()
  const viewHeight = getViewHeight()
  let left = ''
  let top = ''
  left = (viewWidth - width) / 2 + getScrollLeft()
  top = (viewHeight - height) / 2
  if (top < 0) {
    top = 30
  }
  left += 'px'
  top += 'px'
  // el.style.display = "none";
  return { left, top }
}
