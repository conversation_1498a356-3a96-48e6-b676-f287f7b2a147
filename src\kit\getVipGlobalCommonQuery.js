/**
 * NEW_VIP
 */
import { useContext } from 'react'

import { getTimeZone } from '@/utils/common'

/**
 * 获取国际站接口所需的通用参数值
 *
 * @param pageInfoOrContext 页面信息
 */

export function getVipGlobalCommonQuery(pageInfoOrContext) {
  const { lang, mod, qc005, bossCode } =
    typeof pageInfoOrContext.lang === 'undefined'
      ? useContext(pageInfoOrContext)
      : pageInfoOrContext
  return {
    platform: bossCode,
    app_lm: mod,
    lang,
    timeZone: getTimeZone(mod).toString(),
    deviceId: qc005 || ''
  }
}

/**
 * 获取国际站接口所需的通用参数值，并用&拼接为url使用的参数
 *
 * @param pageInfoOrContext 页面信息
 * @param needs 可选，按需返回字符串
 *
 * eg:
 *  getVipGlobalCommonQueryToString(pageInfoOrContext, ['app_lm', 'lang'])
 *  return 'app_lm=something&lang=something'
 */

export function getVipGlobalCommonQueryToString(pageInfoOrContext, needs) {
  const commonQuertObj = getVipGlobalCommonQuery(pageInfoOrContext)
  return Object.keys(commonQuertObj)
    .filter(key => (needs ? needs.indexOf(key) > -1 : true))
    .map(key => `${key}=${commonQuertObj[key]}`)
    .join('&')
}
