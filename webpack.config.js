const path = require('path')
const fs = require('fs')
const nodeExternals = require('webpack-node-externals')
const NODE_ENV = process.env.NODE_ENV // 获取环境变量
console.log(NODE_ENV)
const isEnvDevelopment = NODE_ENV === 'development'
const isEnvProduction = NODE_ENV === 'production'
const HtmlWebpackPlugin = require('html-webpack-plugin')
const { CleanWebpackPlugin } = require('clean-webpack-plugin') // 每次构建清除上一次打包出来的文件
const MiniCssExtractPlugin = require('mini-css-extract-plugin')
const getCSSModuleLocalIdent = require('react-dev-utils/getCSSModuleLocalIdent')
const postcssNormalize = require('postcss-normalize')
const shouldUseSourceMap = process.env.GENERATE_SOURCEMAP !== 'false'
const plugins = isEnvProduction
  ? [new CleanWebpackPlugin()]
  : [
      new CleanWebpackPlugin(),
      new HtmlWebpackPlugin({
        template: 'public/index.html'
      })
    ]

// style files regexes
const cssRegex = /\.css$/
const cssModuleRegex = /\.module\.css$/
const sassRegex = /\.(scss|sass)$/
const sassModuleRegex = /\.module\.(scss|sass)$/
const lessRegex = /\.less$/
const lessModuleRegex = /\.module\.less$/

// common function to get style loaders
const getStyleLoaders = (cssOptions, preProcessor) => {
  const loaders = [
    isEnvDevelopment && require.resolve('style-loader'),
    // isEnvProduction && {
    //   loader: MiniCssExtractPlugin.loader,
    //   options: paths.publicUrlOrPath.startsWith('.')
    //     ? { publicPath: '../../' }
    //     : {},
    // },
    {
      loader: require.resolve('css-loader'),
      options: cssOptions
    },
    {
      loader: require.resolve('postcss-loader'),
      options: {
        ident: 'postcss',
        plugins: () => [
          require('postcss-flexbugs-fixes'),
          require('postcss-preset-env')({
            autoprefixer: {
              flexbox: 'no-2009'
            },
            stage: 3
          }),
          postcssNormalize()
        ],
        sourceMap: isEnvProduction ? shouldUseSourceMap : isEnvDevelopment
      }
    }
  ].filter(Boolean)
  if (preProcessor) {
    loaders.push(
      {
        loader: require.resolve('resolve-url-loader'),
        options: {
          sourceMap: isEnvProduction ? shouldUseSourceMap : isEnvDevelopment,
          root: path.join(__dirname, '../src')
        }
      },
      {
        loader: require.resolve(preProcessor),
        options: {
          sourceMap: true
        }
      }
    )
  }
  return loaders
}

module.exports = {
  mode: isEnvProduction ? 'production' : 'development',
  entry: isEnvProduction ? './src/components/index.js' : './src/app.js',
  output: {
    filename: 'index.js',
    path: path.resolve(__dirname, './dist'),
    // publicPath: '/dist/',
    globalObject: 'this',
    libraryTarget: isEnvProduction ? 'umd' : undefined // 包需要被module.exports
  },
  module: {
    rules: [
      // Disable require.ensure as it's not a standard language feature.
      { parser: { requireEnsure: false } },

      // First, run the linter.
      // It's important to do this before Babel processes the JS.
      {
        test: /\.(js|mjs|jsx|ts|tsx)$/,
        enforce: 'pre',
        use: [
          {
            options: {
              formatter: require.resolve('react-dev-utils/eslintFormatter'),
              eslintPath: require.resolve('eslint'),
              resolvePluginsRelativeTo: __dirname
            },
            loader: require.resolve('eslint-loader')
          }
        ],
        include: path.join(__dirname, '../src')
      },
      {
        // "oneOf" will traverse all following loaders until one will
        // match the requirements. When no loader matches it will fall
        // back to the "file" loader at the end of the loader list.
        oneOf: [
          // "url" loader works like "file" loader except that it embeds assets
          // smaller than specified limit in bytes as data URLs to avoid requests.
          // A missing `test` is equivalent to a match.
          {
            test: [/\.bmp$/, /\.gif$/, /\.jpe?g$/, /\.png$/],
            loader: require.resolve('url-loader'),
            options: {
              name: 'static/imgs/[name].[hash:8].[ext]'
            }
          },
          // Process application JS with Babel.
          // The preset includes JSX, Flow, TypeScript, and some ESnext features.
          {
            test: /\.(js|mjs|jsx|ts|tsx)$/,
            exclude: /node_modules/,
            use: [
              {
                loader: 'babel-loader',
                options: {
                  plugins: [
                    [
                      require.resolve('babel-plugin-named-asset-import'),
                      {
                        loaderMap: {
                          svg: {
                            ReactComponent:
                              '@svgr/webpack?-svgo,+titleProp,+ref![path]'
                          }
                        }
                      }
                    ]
                  ]
                }
              },
              {
                loader: 'linaria/loader',
                options: {
                  sourceMap: process.env.NODE_ENV !== 'production'
                }
              }
            ]
          },
          {
            test: cssRegex,
            exclude: cssModuleRegex,
            use: getStyleLoaders({
              importLoaders: 1,
              sourceMap: isEnvProduction && shouldUseSourceMap
            }),
            sideEffects: true
          },
          {
            test: cssModuleRegex,
            use: getStyleLoaders({
              importLoaders: 1,
              sourceMap: isEnvProduction && shouldUseSourceMap,
              modules: true,
              getLocalIdent: getCSSModuleLocalIdent
            })
          },
          {
            test: sassRegex,
            exclude: sassModuleRegex,
            use: getStyleLoaders(
              {
                importLoaders: 2,
                sourceMap: isEnvProduction && shouldUseSourceMap
              },
              'sass-loader'
            ),
            sideEffects: true
          },
          {
            test: sassModuleRegex,
            use: getStyleLoaders(
              {
                importLoaders: 2,
                sourceMap: isEnvProduction && shouldUseSourceMap,
                modules: {
                  getLocalIdent: getCSSModuleLocalIdent
                }
              },
              'sass-loader'
            )
          },
          {
            test: lessRegex,
            exclude: lessModuleRegex,
            use: getStyleLoaders(
              {
                importLoaders: 2,
                sourceMap: isEnvProduction && shouldUseSourceMap
              },
              'less-loader'
            ),
            sideEffects: true
          },
          // that fall through the other loaders.
          {
            loader: require.resolve('file-loader'),
            exclude: [/\.(js|mjs|jsx|ts|tsx)$/, /\.html$/, /\.json$/],
            options: {
              name: 'static/media/[name].[hash:8].[ext]'
            }
          }
          // ** STOP ** Are you adding a new loader?
          // Make sure to add the new loader(s) before the "file" loader.
        ]
      }
    ]
  },
  plugins,
  resolve: {
    alias: {
      '@': path.join(__dirname, './src')
    }
  },
  devtool: 'source-map', // 开启调试
  devServer: {
    contentBase: path.join(__dirname, './build'),
    compress: true,
    port: 443,
    hot: true,
    disableHostCheck: true,
    host: '0.0.0.0',
    open: true, // 自动打开浏览器
    https: {
      key: fs.readFileSync('./ssl/server.key'), // ssl文件路径
      cert: fs.readFileSync('./ssl/server.crt') // ssl文件路径
    },
    proxy: {
      // '/pay-product-international': {
      //   target: 'https://pay-test.iqiyi.com',
      //   secure: false,
      //   changeOrigin: true,
      //   // pathRewrite: {'^/pay-product-international/mastercard/m4m/queryCardInfos' : ''},
      //   cookieDomainRewrite: {
      //     // 将目标服务器设定的cookie的域重写为原请求域
      //     '*': 'chs.iq.com'
      //   },
      //   headers: {
      //     host: 'pay-test.iqiyi.com', // 可能需要设置 headers 来满足目标服务器校验 Host 头部的需求
      //   },
      //   onProxyReq: (proxyReq, req) => {
      //     // 确保携带原始请求的cookie
      //     if (req.headers.cookie) {
      //       proxyReq.setHeader('cookie', req.headers.cookie)
      //     }
      //   }
      // }
    }
  },
  externals: NODE_ENV === 'production' ? [nodeExternals()] : []
}
