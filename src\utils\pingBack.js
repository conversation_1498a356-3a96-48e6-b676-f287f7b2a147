import Logger from '@/utils/logger'
import { getDevice, isServer } from '@/kit/device'
import { getCookies, setCookies } from '@/kit/cookie'
import { queryFromUrl } from '@/kit/url'

let setIntlPbVfmTag = 1

export const p1Val = () => {
  let p1 = '1_10_222'
  if (getDevice() === 'mobile') {
    p1 = '2_20_223'
  }
  return p1
}
/*
    http://wiki.qiyi.domain/pages/viewpage.action?pageId=6070838
*/
export const sendBlockPb = (block, param) => {
  if (!window.isNotSeo) {
    return false
  }
  const { rpage, tjPb, bstp } = param || {}
  let params = {
    t: 21,
    bstp: bstp || 0,
    p1: p1Val(),
    u: getCookies('QC005'),
    pu: '',
    jsuid: getCookies('QC006'),
    rn: Math.random(),
    block,
    rpage: rpage || '',
    purl: window.location.href,
    rfr: document.referrer,
    lrfr: getCookies('QC007'),
    mod: getCookies('mod'),
    lang: getCookies('lang'),
    stime: new Date().getTime(),
    timezone: new Date().toUTCString()
  }
  if (tjPb) {
    if (tjPb.pbType === 'tj') {
      params.bstp = 24
      delete tjPb.pbType
    }
    if(tjPb.abtest) {
      tjPb.abtest = encodeURIComponent(tjPb.abtest)
    }
    params = Object.assign(params, tjPb)
  }
  const { vfm } = utmPbParams()
  if (vfm) {
    params.vfm = vfm
  }

  callLogger(params)
  return true
}

export const sendClickPb = (param = {}) => {
  if (!window.isNotSeo) {
    return false
  }
  let params = {
    t: 20,
    bstp: param.bstp || 0,
    p1: p1Val(),
    u: getCookies('QC005'),
    pu: '',
    tn: Math.random(),
    rseat: param.rseat || '',
    rpage: param.rpage || '',
    block: param.block || '',
    shrtp: param.shrtp || '',
    shrtgt: param.shrtgt || '',
    s2: param.s2 || '',
    mod: getCookies('mod'),
    lang: getCookies('lang'),
    purl: window.location.href
  }
  if (param.tjPb) {
    params = Object.assign(params, param.tjPb)
  }
  const { vfm } = utmPbParams()
  if (vfm) {
    params.vfm = vfm
  }
  // const options = {
  //   url: '//msg-intl.qy.net/act'
  // }
  // Logger.server(params, options)
  callLogger(params)
  return true
}

export const sendLog = (url, params = {}) => {
  if (typeof url !== 'string') {
    return
  }

  const img = new Image()
  const paramsArr = []

  try {
    Object.keys(params).forEach(paramName => {
      paramsArr.push(`${paramName}=${encodeURIComponent(params[paramName])}`)
    })
  } catch (e) {
    console.log(e)
  }

  try {
    img.src = `${url}?${paramsArr.join('&')}`
  } catch (e) {
    console.log(e)
  }
}

export const callLogger = params => {
  const options = {
    url: '//msg-intl.qy.net/act'
  }
  if (window && window.dfp && window.lib && window.mainQaInstance) {
    const Qa = window.mainQaInstance // 在global_pcw_qa.js 1798行 注入
    Qa.getHu(hu => {
      Qa.getFingerPrint(dfp => {
        params['dfp'] = dfp
        params['hu'] = hu
        params['pu'] = Qa.getUserInfoUid()
        params['de'] = Qa.getQtsid()
        params['ce'] = Qa.getWeid()

        Logger.server(params, options)
      })
    })
  } else {
    Logger.server(params, options)
  }
}

export const utmPbParams = () => {
  let url = ''
  if (isServer) {
    url = global.pageReqUrl
  } else {
    url = window.location.href
  }
  if (!url) {
    return {
      vfm: '',
      encodeVfm: ''
    }
  }
  const utmSource = queryFromUrl(url, 'utm_source')
  const utmMedium = queryFromUrl(url, 'utm_medium')
  const utmCampaign = queryFromUrl(url, 'utm_campaign')
  const utmContent = queryFromUrl(url, 'utm_content')
  let vfm = utmSource ? `utm_source=${utmSource}&` : ''
  vfm += utmMedium ? `utm_medium=${utmMedium}&` : ''
  vfm += utmCampaign ? `utm_campaign=${utmCampaign}&` : ''
  vfm += utmContent ? `utm_content=${utmContent}&` : ''
  vfm = vfm ? vfm.substr(0, vfm.length - 1) : ''
  const intlPbVfm = isServer
    ? getCookies('intlPbVfm', global.ctx)
    : getCookies('intlPbVfm')
  if (vfm && setIntlPbVfmTag) {
    setCookies('intlPbVfm', vfm, {
      time: 1000 * 60 * 60 * 6
    })
    setIntlPbVfmTag = 0
  } else if (!vfm) {
    vfm = intlPbVfm || ''
  }
  return {
    vfm,
    encodeVfm: encodeURIComponent(vfm)
  }
}
