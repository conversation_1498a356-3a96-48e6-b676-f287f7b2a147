import React from 'react'
import Style from './style/empty'

export default () => {
  return (
    <Style>
      <div className="box">
        <div className="title"></div>
        <div className="price"></div>
        {[0, 1].map((i) => (
          <>
            <div key={i+1} className="name"></div>
            <div key={i+2} className="detail"></div>
          </>
        ))}
        <div className="last"></div>
      </div>
    </Style>
  )
}