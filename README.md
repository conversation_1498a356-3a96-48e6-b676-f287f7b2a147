# 国际站收银台SDK


## 概述
-国际站PCW/H5收银台的react的NPM包，只限于react项目使用。国际站收银台分为落地页和弹窗模式，弹窗暂不适配H5。

## 发布版本

^0.0.3


## 入口参数，全部非必填
|  字段名称   | 含义  | 含义  |
|  ----  | ----  | ----  |
| cashierType  | 收银台类型 | 0-拉起弹窗收银台，1-拉起落地页收银台。若不传入则默认进入弹窗和落地页收银台AB测试逻辑。 |
| vipType  | 会员类型，用于区别一类会员身份 | 请求收银台checkout接口透传 |
| fc  | 内部订单来源标识 | 请求收银台checkout接口和下单接口时透传 |
| fv  | 外部订单来源标识 | 请求收银台checkout接口和下单接口时透传 |
| amount  | 套餐时长 | 请求收银台checkout接口透传 |
| payAutoRenew  | 套餐时长 | 请求收银台checkout接口透传 |
| albumId  | 来源视频id | 在dopay下单时拼接在fr_version字段中 |
| fr  | 在dopay接口时直接透传至fr_version字段 | 在dopay下单时拼接在fr_version字段中 |

## 本地调试

1. src/app.js中已经引入来自src/components的组件
2. 'sudo npm run dev'启动本地调试，会自动打开0.0.0.0, 因为qae接口建议访问host文件里配置的XXX.iq.com地址
3. 点击页面按钮（src/components/NewButton组件），会触发SDK收银台逻辑，调试模式没有cookie所以需要在url里加query验证


## SDK暴露接口

1. PopupVip: reactDOM组件
2. createVipOrder: 真正触发展示收银台逻辑的API，入口参数也是在改API传入

## 应用global-vip-sdk

1. 项目安装@iqiyi-ibd/global-vip-sdk
2. 项目根节点引入PopupVip,如下

    ```javascript

    const PopupVip = dynamic(
        import('@iqiyi-ibd/global-vip-sdk').then(mod => mod.PopupVip),
        {
            ssr: false
        }
    )
    return (
    <>
      <Head>
          <meta httpEquiv="X-UA-Compatible" content="IE=edge" />
          <meta charSet="utf-8" />
          <meta
          name="viewport"
          content="initial-scale=1.0, width=device-width, user-scalable=no, minimal-ui"
          key="viewport"
          />
          <title>国际站</title>
      </Head>
      <GlobalStyle />
      <LayoutWrapper>
      </LayoutWrapper>
      <PopupVip />
    </>
    )
    ```
3. 触发事件执行createVipOrder({})
    ```javascript
    import { createVipOrder } from '@iqiyi-ibd/global-vip-sdk'
    
    onClick={e => {
        e.preventDefault()
        createVipOrder({
            fc: '850066c8640127c5'
        })
    }}
    ```
