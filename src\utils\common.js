export const rpageObj = {
  '/': 'home',
  '/play': 'play',
  '/drama': 'drama',
  '/movie': 'movie',
  '/anime': 'anime',
  '/variety-show': 'variety_show',
  '/vip/codekey': 'redeem_vip',
  '/vip/order': 'web_casher',
  '/search': 'search',
  '/settings': 'settings',
  '/vip': 'vip',
  '/child': 'child',
  '/yule': 'yule',
  '/jilupian': 'jilupian'
}

// export const channelIdObj = {
//   '/drama': '2',
//   '/movie': '1',
//   '/anime': '4',
//   '/variety-show': '6'
// }

export const channelNameObj = {
  '2': '/drama',
  '1': '/movie',
  '4': '/anime',
  '6': '/variety-show',
  '15': '/child',
  '7': '/yule',
  '3': '/jilupian'
}

export const vipPlatform = {
  mo: 'afebdf80ec0a1ed7',
  hk: 'a5476813e822bb3a',
  mm: '9fae8177a7783b53',
  sg: 'a2abae5f403398b6',
  vn: 'ae14f2ce43aae128',
  bn: '81fe99535371495f',
  kh: 'baa8a03092406a55',
  id: '8d81b5b396903ee3',
  la: '90f550eeaf76e536',
  my: '9c7c2c85707da9df',
  ph: 'aa08388020727a3c',
  th: 'ab3c50bb8ae6fd40',
  ca: '845fc7fae6080135',
  us: 'a99836edce7456da',
  intl: 'ae8b397dc5bed0f2'
}

/**
 * NEW_VIP
 */

/**
 * 获取当前时区
 */
export function getTimeZone() {
  return (
    'GMT' +
    String(~(new Date().getTimezoneOffset() / 60) + 1).padStart(2, '+') +
    ':00'
  )
}

/**
 * 格式化时间方法
 *
 * @param date
 * @param pattern
 */

export function formatDate(date, pattern = 'yyyy/MM/dd') {
  let d
  if (typeof date === 'number') {
    d = new Date(date)
  } else if (typeof date === 'undefined') {
    return ''
  } else {
    d = date
  }

  const year = d.getFullYear()
  const month = padding0(d.getMonth() + 1)
  const day = padding0(d.getDate())

  const hour = padding0(d.getHours())
  const min = padding0(d.getMinutes())
  const second = padding0(d.getSeconds())

  const res = pattern
    .replace('yyyy', year.toString())
    .replace('MM', month.toString())
    .replace('dd', day.toString())
    .replace('HH', hour.toString())
    .replace('mm', min.toString())
    .replace('ss', second.toString())

  return res
}

/**
 * 将小于10的数字用0填充至两位字符
 * eg: 1 => '01' ; 10 => '10'
 *
 * @param num
 */

function padding0(num) {
  return num >= 10 ? String(num) : `0${num}`
}

/**
 * 判断是否是一个函数
 *
 * @param arg
 */

export function isFunction(arg) {
  if (arg) {
    // eslint-disable-next-line no-constant-condition
    if (typeof /./ !== 'function') {
      return typeof arg === 'function'
    } else {
      return Object.prototype.toString.call(arg) === '[object Function]'
    }
  }
  return false
}

export const dataUrltoFile = (dataurl, filename) => {
  const arr = dataurl.split(',')
  const mime = arr[0].match(/:(.*?);/)[1]
  const bstr = window.atob(arr[1])
  let n = bstr.length
  const u8arr = new Uint8Array(n)
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n)
  }
  // eslint-disable-next-line no-undef
  return new File([u8arr], filename, { type: mime })
}

export const tvnMods = [
  'th',
  'ph',
  'my',
  'la',
  'id',
  'kh',
  'bn',
  'vn',
  'sg',
  'mm'
]

export const channelData = pkg => {
  return {
    '2': {
      name: pkg.navigation_drama,
      url: 'https://www.iq.com/drama'
    },
    '1': {
      name: pkg.navigation_movie,
      url: 'https://www.iq.com/movie'
    },
    '4': {
      name: pkg.navigation_anime,
      url: 'https://www.iq.com/anime'
    },
    '6': {
      name: pkg.navigation_variety_show,
      url: 'https://www.iq.com/variety-show'
    },
    '15': {
      name: pkg.childtab,
      url: 'https://www.iq.com/kids'
    },
    '7': {
      name: pkg.yuletab,
      url: 'https://www.iq.com/yule'
    },
    '3': {
      name: pkg.jilupiantab,
      url: 'https://www.iq.com/documentary'
    }
  }
}

/**
 *
 * @param {*} pathname window.location.pathname
 * @description 通过pathname返回对象的chnid
 */
export const channelIds = pathname => {
  const ids = {
    '/': 0,
    '/movie': 1,
    '/drama': 2,
    '/documentary': 3,
    '/anime': 4,
    '/variety-show': 6,
    '/kids': 15
  }

  if (ids[pathname]) return ids[pathname]

  for (const path in ids) {
    if (pathname.includes(path)) return ids[path]
  }
  return 0
}


export function getPtid() {
  const ua = navigator.userAgent.toLowerCase()
  const platform = navigator.platform.toLowerCase()
  const client = '002'
  const brand = '101'
  const license = '00'
  const localCode = '00'
  const left = '000000'
  let hard = '01'
  if (/(iphone)|(android)/i.test(ua)) {
    hard = '02'
  } else if (/pad/i.test(ua)) {
    hard = '03'
  }
  let osCode = '01'
  if (/android/i.test(platform)) {
    osCode = '02'
  }
  if (/mac/i.test(platform)) {
    osCode = '08'
  }
  if (/ios/i.test(platform)) {
    osCode = '03'
  }
  return hard + osCode + client + brand + license + localCode + left
}

export function isEmptyStr(s) {
	if (s == undefined || s === '') {
		return true
	}
	return false
}