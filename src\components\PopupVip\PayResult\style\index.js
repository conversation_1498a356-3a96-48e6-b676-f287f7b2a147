import styled from 'styled-components'

const PayResultWrapper = styled.div`
  /* min-height: 560px; */
  .step-icon {
    margin: 0 auto 12px;
    width: 40px;
    height: 40px;
    background: url('//www.iqiyipic.com/lequ/20210630/icon_success.png') no-repeat;
    background-size: cover;
  }
  .success-info {
    box-sizing: border-box;
    margin: 0 auto 16px;
    padding: 12px 24px 0;
    width: 504px;
    background: #FFFFFF;
    border: 1px solid #E9E9E9;
    box-shadow: 0px 1px 4px 0px rgba(0,0,0,0.02);
    border-radius: 6px;
  }
  .success-detail {
    font-size: 14px;
    color: #222222;
    letter-spacing: 0;
    font-weight: 700;
    line-height: 40px;
  }
  .item-row {
    padding: 16px 0;
    font-size: 14px;
    line-height: 20px;
    display: flex;
    &:last-child {
      border-bottom: 1px solid #E9E9E9;
    }
    .item-key {
      width: 80px;
      line-height: 16px;
      font-size: 14px;
      color: #999999;
      letter-spacing: 0;
      font-weight: 400;
      display: inline-block;
    }
    .item-value {
      display: inline-block;
      margin-left: 12px;
      height: 16px;
      font-size: 14px;
      color: #222222;
      letter-spacing: 0;
      font-weight: 400;
    }
  }
  .total-price {
    height: 66px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    .total {
      line-height: 16px;
      font-size: 14px;
      color: #999999;
      letter-spacing: 0;
      font-weight: 400;
    }
    .price {
      margin-left: 8px;
      line-height: 19px;
      font-size: 16px;
      color: #222222;
      letter-spacing: 0;
      font-weight: 700;
    }
  }
  @media screen and (max-width: 1023px) {
    .success-info {
      width: 532px;
    }
  }
  @media screen and (max-width: 767px) {
    .success-info {
      border: 0;
      border-top: 1px solid #E6E6E6;
      width: 311px;
      margin: 0 auto;
      padding: 8px 0;
    }
    .success-detail {
      padding: 8px 0;
      line-height: 15px;
      font-size: 13px;
      color: #222222;
      letter-spacing: 0;
      font-weight: 700;
    }
    .item-row {
      padding: 8px 0;
      display: block;
      &:last-child {
        border-bottom: 0;
      }
      .item-key {
        display: block;
        width: 100%;
        line-height: 16px;
        font-size: 14px;
        color: #999999;
        letter-spacing: 0;
        font-weight: 400;
      }
      .item-value {
        display: block;
        margin-left: 0;
        margin-top: 4px;
        line-height: 16px;
        font-size: 14px;
        color: #222222;
        letter-spacing: 0;
        font-weight: 400;
      }
    }
    .total-price {
      display: block;
      text-align: left;
      height: auto;
      padding: 8px 0;
      .total {
        display: block
        line-height: 16px;
        font-size: 14px;
        color: #999999;
        letter-spacing: 0;
        font-weight: 400;
      }
      .price {
        margin-top: 4px;
        margin-left: 0;
        display: block;
        line-height: 16px;
        font-size: 14px;
        color: #222222;
        letter-spacing: 0;
        font-weight: 400;
      }
    }
  }
`
export default PayResultWrapper
