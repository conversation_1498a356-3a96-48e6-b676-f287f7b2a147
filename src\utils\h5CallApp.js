import { detectH5Ua } from '@/kit/device'
import { queryFromUrl } from '@/kit/url'

class H5CallApp {
  constructor(opt) {
    this.countNum = 100
    this.time = 3000
    this.h5Ua = detectH5Ua()
    this.appUrl = 'iqyinter://mobile/register_business/qyclient'
    this.guideDownUrl = '//go.onelink.me/4Hx2/2bcca82b'
    this.playDownUrl = opt.playDownUrl || '//go.onelink.me/4Hx2/share'
    this.footerDownUrl = opt.homeDownUrl || '//go.onelink.me/4Hx2/share' // 暂时未用到，先改成能通的
    this.channelDownUrl =
      opt.channelDownUrl || '//go.onelink.me/4Hx2/iqyiChannel'
    this.downAppTag = ''
    this.pluginParams = ''
    this.callAppFailure = '' // 拉起失败默认下载app，值为downloadTip时展示下载提示浮层
    this.pageKey = opt.pageKey // 频道页所需
    this.ctype = opt.ctype || '0' // 视频类型 0点播 1专题 3直播
    this.payMark = opt.payMark || '0' // 是否vip视频，0否，1是vip，2会员付费，3用券
    this.videoType = opt.videoType || '0' // 是否全景，0普通，1全景
    this.albumId = opt.albumid || opt.albumId || ''
    this.tvId = opt.tvid || opt.tvId || ''
    this.mod = opt.mod || 'intl'
  }

  callApp(tag) {
    let _url = this.appUrl
    let shPltf = queryFromUrl(window.location.href, 'sh_pltf') // 统计用参数 ******************提的需求
    if (shPltf) {
      shPltf = `&sh_pltf=${shPltf}`
    }
    if (tag === 'play') {
      _url = this.playDownUrl // 改成AF吊起
      // 播放页拉起app
      const albumid = this.albumId || 0
      let tvid = this.tvId || 0
      const mod = this.mod || ''
      if (tvid === albumid) {
        tvid = 0
      }
      const bizParams =
        `aid=${this.albumId}&tvid=${this.tvId}&ctype=${this.ctype}&` +
        `pc=${this.payMark}&video_type=${this.videoType}&from_type=27&` +
        `from_sub_type=10&mod=${mod}${shPltf}`
      const paramObj = {
        biz_id: '102',
        biz_plugin: 'qiyiplayer',
        biz_params: {
          biz_sub_id: '101',
          biz_params: bizParams
        },
        biz_extend_params: '',
        biz_dynamic_params: '',
        biz_statistics: ''
      }
      let paramStr = JSON.stringify(paramObj)
      paramStr = encodeURIComponent(paramStr)
      this.pluginParams = encodeURIComponent(paramStr)
      const oneLinkUrl = this.playDownUrl + `?pluginParams=${this.pluginParams}`
      _url = oneLinkUrl
    } else if (tag === 'home' || tag === 'footer') {
      // 大首页拉起app
      _url =
        this.footerDownUrl +
        `?c=iqiyiOrganic&af_dp=iqyinter://mobile/register_business/qyclient&af_web_dp=https://www.iq.com&af_force_deeplink=true&af_adset=float&function=float`
      // _url = "iqyinter://mobile/home?&ftype=27&subtype=147";
    } else if (tag === 'channel') {
      const bizParams = `page_key=${this.pageKey}&c=iqiyiOrganic&af_adset=float&function=float`
      const paramObj = {
        biz_plugin: 'global',
        biz_params: {
          biz_sub_id: '100',
          biz_params: bizParams
        }
      }
      let paramStr = JSON.stringify(paramObj)
      paramStr = encodeURIComponent(paramStr)
      this.pluginParams = encodeURIComponent(paramStr)
      _url = this.channelDownUrl + `?pluginParams=${this.pluginParams}`
    }
    this.openApp(_url)
  }

  openApp(url) {
    window.location.href = url
  }
}

export default H5CallApp
