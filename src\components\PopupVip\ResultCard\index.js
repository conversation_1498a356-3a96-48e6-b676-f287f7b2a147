import React from 'react'
import ResultCardWrapper from './style'
import { connect } from 'react-redux'
import ImgCard from './ImgCard'
import CouponCard from './CouponCard'
import RedPkgCard from './RedPkgCard'
const ResultCard = ({ cardData, orderInfo }) => {
  return (
    <ResultCardWrapper>
      {cardData.type === 'marketingCard' ? (
        cardData.tempType === 1 ? (
          <ImgCard cardData={cardData} />
        ) : (
          <RedPkgCard cardData={cardData} />
        )
      ) : (
        ''
      )}
      {cardData.type === 'couponCard'
        ? cardData.details.length > 0 &&
          cardData.details
            .sort((a, b) => a.sort - b.sort)
            .map((item, index) => {
              return <CouponCard cardData={item} key={index} orderInfo={orderInfo}></CouponCard>
            })
        : ''}
    </ResultCardWrapper>
  )
}

const mapStateToProps = (state) => {
  return {
    data: state.user
  }
}

export default connect(mapStateToProps)(ResultCard)
