import styled from 'styled-components'

const GopayWrapper = styled.div`
  width: 100%;
  .new-vip-result-header {
    height: 24px;
    .back {
      font-size: 16px;
      color: #666666;
      font-weight: 400;
      position: absolute;
      top: 12px;
      left: 12px;
      display: block;
      width: 24px;
      height: 24px;
      background: url('//www.iqiyipic.com/lequ/20210622/icon-nav-back-normal.png')
        no-repeat;
      background-size: cover;
      &:hover {
        background: url('//www.iqiyipic.com/lequ/20210622/icon-nav-back-hover.png')
          no-repeat;
        background-size: cover;
      }
    }
    .close {
      position: absolute;
      top: 12px;
      right: 12px;
      width: 24px;
      height: 24px;
      background: url('//www.iqiyipic.com/lequ/20210622/icon-nav-close-normal.png')
        no-repeat;
      background-size: cover;
      cursor: pointer;
      &:hover {
        background: url('//www.iqiyipic.com/lequ/20210622/icon-nav-close-hover.png')
          no-repeat;
        background-size: cover;
      }
    }
  }
  .gopay-modal-wrapper {
    width: 420px;
    margin: 0 auto;
  }
  .gopay-pkg-title {
    margin-top: 40px;
    font-size: 14px;
    color: #222222;
    text-align: center;
  }
  .gopay-pkg-price {
    display: flex;
    justify-content: center;
    align-items: baseline;
    width: 100%;
    font-size: 32px;
    color: #222222;
    font-weight: 800;
    text-align: center;
  }
  .pkg-symbol {
    font-size: 20px;
    color: #222222;
  }
  /* 输入框部分样式 */
  .gopay-detail-wrapper {
    /* padding: 0 240px; */
    margin-top: 48px;
    box-sizing: border-box;
  }
  .gopay-icon {
    width: 78px;
    height: 24px;
  }
  .gopay-title {
    margin: 12px 0 12px;
    font-size: 16px;
    color: #222222;
    font-weight: 700;
  }

  .banknumber-input-wrapper {
    position: relative;

    display: block;
    margin-bottom: 8px;
    flex: 1;
    font-size: 16px;
    font-weight: 400;
    .banknumber-input-label {
      position: relative;
      box-sizing: border-box;
      border: 1px solid #e6e6e6;
      border-radius: 4px;
      transform-origin: bottom center;
      transition: border 0.3s;

      /* border: 1px solid #ff0000; */
      // overflow: hidden;
      display: flex;
      outline: none;
      &:focus-within {
        border: 1px solid #f2bf83;
      }
    }
  }
  .has-error {
    .banknumber-input-label {
      border: 1px solid #fe3b30;
    }
  }
  .gopay-title-wrapper {
    display: flex;
    align-items: center;
  }
  .gopay-back {
    margin-left: 12px;
    /* margin-bottom: 12px; */
    font-size: 14px;
    color: #e09e51;
    cursor: pointer;
  }
  .pay-error-outer {
    margin-bottom: 12px;
    /* padding: 0 40px; */
  }
  .pay-error-wrapper {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 8px;
    /* margin-top: 12px; */
    background: rgba(254, 59, 48, 0.08);
    border-radius: 4px;
  }
  .error-icon {
    width: 14px;
    height: 14px;
    margin-right: 4px;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAIKADAAQAAAABAAAAIAAAAACshmLzAAADc0lEQVRYCeVXO28TQRCeuTsrQAFCVJCKBqRQmYRHJBweghZHStLkX1DRUASJhop/gUAkUkILghAjhYcjVyBCQ5VQAaIBorNvmBnf7u2d7+FIoBQZybp9zHzz3dzs7BhgvwvuJgA0NVGHXm8agBqAMMq2J2L7bSDYAsAW+P4yrrU7w+IORYAa9VmI6B4BnB4GmEE3wcM72OosVumXEqCp8ychCh8S0cUqoLx9RHwDXm0e1959yduXtUIC/NaXIYIlAjpmjBHhB4d6BTx/Bcj/CMGhbd3r/uJP0T0DFN1kxCYRHLU2gN/AgxmOxiuz5j5zCcTOn7Hzmijzm/xh4Acw4t3H5xs/XYDsmK6PH4Gd6DYTvcWRO6D2gCGTuJFHYoCAhr0XvjdvzgpbUMNpXO20s87K5nSlPgEhLXPeSLIyf46EXzuX/RzeAIh88zjs6nzk4IXdOleHQlhs5QVYFJOxs/5SEZBsp4ieKICEPYBGkXPWHQOiOQWsBY/wZXszCy5zjUQXWvZzeDjnno50BPioWRD+5kXOVYedc7ItyA+63RlrlxkohuSPEdcHr1kC1Bg/a865ZjsnnLHJfSKyeizkpSJplu1TkldOEIv40IIWb1oCEEVNa8BHrSrb+YgmBIBKCSiWHF8jWk37k4SAlFcjcs53I1RcTywMek/t2PGVEOjX9r6OFJkqQTcCVcqyH3ywWo6vhEBysbBuXOGsRd7AyQFviAikMc0lliRhnovSNYySHKDyHFCcw2GSJ1wUDHZgBvyUun5K51rb4bOzNzgMgiUMu1qquVZr7RhUcla+h8ftDOGrGScE9D6PCWBvjBVKCcSF564Bqn7yZWWk70tnCQFpJoCu6mrUkyO5bPTznqlKGPiPcXXjU56eXZOb0or46kuShNzJmEVOqabeanYhdzCbVEIqrIRiqViMaVEcX5aAtFGcJVrP9T6XK7VMiEuRESTfDHOfjKWYvCk+3JbNElBDbqMsgNzncqUWSeAtcnldkB+rFLZeisFYFsb1wYtMKC10qb5uWjDe3NIr9cW6XqlpzeoZXZschZ3fb/nM9XsCbtHwdWfStUxHQHakh5PmgUUNBaAsEqKYI2rjOtfWrDafVR2IgCjsaUtmGMYkhm9KpXbI8f0XTaklsZdtuSEhT47G3vwxcUkokf/w1yzrY//N/wIHNoMQJWFG8wAAAABJRU5ErkJggg==)
      no-repeat center;
    background-size: 100% 100%;
  }
  .pay-error-tip {
    font-size: 12px;
    color: #fe3b30;
  }
  .areacode {
    position: absolute;
    top: 50%;
    left: 12px;
    transform: translateY(-50%);
    font-size: 16px;
    color: #222222;
  }
  .phonenumber-input-input {
    display: block;
    box-sizing: border-box;
    margin: 0;
    border: none;
    outline: none;
    // border-radius: 4px;
    padding: 0 12px 12px 60px;
    width: 100%;
    height: 60px;
    border-top: solid 27px transparent;
    // color: rgba(var(--pure-material-onsurface-rgb, 0, 0, 0), 0.87);
    color: #222222;
    box-shadow: none;
    border-radius: 4px;
    font-size: 16px;
    transition: border-bottom 0.2s, background-color 0.2s;
    -webkit-appearance: none;
    &[type='password'] {
      font-size: 20px;
    }
    &:not(:focus):placeholder-shown + span {
      font-size: 14px;
      line-height: 60px;
    }
  }
  .phonenumber-input-desc {
    font-size: 12px;
    line-height: 36px;
    color: #999999;
    position: absolute;
    width: 90%;
    height: 100%;
    top: 0;
    left: 46px;
    right: 0;
    bottom: 0;
    display: block;
    box-sizing: border-box;
    padding: 0 12px;
    pointer-events: none;
    /* border: 1px solid #FFFF00; */
    transition: color 0.2s, font-size 0.2s, line-height 0.2s;
  }
  .phonenumber-wrapper {
    display: flex;
    align-items: center;
    margin-top: 12px;
    margin-bottom: 20px;
  }
  .phonenumber {
    font-size: 20px;
    color: #222222;
    font-weight: 700;
  }
  .change-phone {
    margin-left: 8px;
    font-size: 14px;
    color: #e09e51;
    cursor: pointer;
  }
  .mobile-error {
    color: #fe3d33;
    margin-top: 8px;
    font-size: 12px;
  }
  .gopay-desc {
    margin-top: 8px;
    font-size: 12px;
    color: #999999;
  }
  .gopay-btn {
    display: flex;
    flex: 1;
    justify-content: center;
    align-items: center;
    width: 100%;
    padding: 12px 0;
    margin-top: 12px;
    background: #f2bf83;
    border-radius: 4px;
    font-size: 16px;
    color: #111319;
    border: none;
    cursor: pointer;
    &:hover {
      background: #f4cb9b;
      color: #404247;
    }
  }
  .buy-btn-text {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    font-size: 16px;
    color: #111319;
    /* line-height: 50px; */
    text-align: center;
  }
  .buy-loading {
    width: 20px;
    height: 20px;
    animation: Loading 1s linear infinite;
  }
  @keyframes Loading {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
`
export default GopayWrapper
