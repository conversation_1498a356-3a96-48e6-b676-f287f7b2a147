const ua = Symbol.for('private#ua')
const iPhone = Symbol.for('private#iPhone')
const trident = Symbol.for('private#trident')
const android = Symbol.for('private#android')
const iPad = Symbol.for('private#iPad')

class Browser {
  constructor() {
    const isServer = typeof window === 'undefined'
    if (isServer) {
      this[ua] = global.deviceAgent
    } else {
      this[ua] = navigator.userAgent.toLowerCase()
    }
    // winphone手机ie浏览器的ua字符串内增加了like iphone, os x的字样，导致必须修改识别iphone的规则
    this[trident] = this[ua].match(/trident/)
    this[iPad] = !this[trident] && this[ua].match(/(ipad).*os\s([\d_]+)/)
    this[iPhone] =
      !this[trident] && !this[iPad] && this[ua].match(/(iphone\sos)\s([\d_]+)/)
    this[android] = this[ua].match(/(Android)\s+([\d.]+)/)
    this.IE11 = /rv:11/.test(this[ua])
    this.IE = /msie/.test(this[ua]) || this.IE11
    this.OPERA = /opera/.test(this[ua])
    this.MOZ = /gecko/.test(this[ua])
    this.IE6 = /msie 6/.test(this[ua])
    this.IE7 = /msie 7/.test(this[ua])
    this.IE8 = /msie 8/.test(this[ua])
    this.IE9 = /msie 9/.test(this[ua])
    this.IE10 = /msie 10/.test(this[ua])
    this.EDGE = /edge/.test(this[ua])
    this.SAFARI = /safari/.test(this[ua]) && !/chrome/.test(this[ua])

    // this.mobileSafari =
    //   (/iPhone/i.test(navigator.platform) ||
    //     /iPod/i.test(navigator.platform) ||
    //     /iPad/i.test(navigator.userAgent)) &&
    //   !!navigator.appVersion.match(/(?:Version\/)([\w._]+)/);
    this.WEBKIT = /webkit/.test(this[ua])
    // this.winXP=/windows nt 5.1/.test(_ua);
    // this.winVista=/windows nt 6.0/.test(_ua);
    this.CHROME = /chrome/.test(this[ua])
    this.iPhone = /iphone os/.test(this[ua]) && !this[trident] // iphone 的检测
    this.iPod = /iPod/i.test(this[ua]) && !this[trident] // ipod 的检测
    this.android = /android/.test(this[ua]) // android 的检测
    this.iPhone4 = /iphone os [45]_/.test(this[ua]) && !this[trident] // iphone 4的检测
    this.iPad = /ipad/.test(this[ua]) && !this[trident] // ipad的检测
    this.WP = /windows phone/.test(this[ua])
    this.baidu = /baidu/.test(this[ua]) // 百度浏览器
    this.mbaidu = /baidu/.test(this[ua]) // 百度浏览器
    this.m360 = /360/.test(this[ua]) // 360手机浏览器检测
    this.muc = /uc/.test(this[ua]) // uc手机浏览器检测
    this.mqq = /qq/.test(this[ua]) // qq浏览器检测
    if (this[android]) {
      this.version = this[android][2]
    }
    if (this[iPhone]) {
      this.ios = true
      this.version = this[iPhone][2].replace(/_/g, '.')
    }
    if (this[iPad]) {
      this.ios = true
      this.version = this[iPad][2].replace(/_/g, '.')
    }
    if (this.iPod) {
      this.ios = true
    }
    this.lePad = /lepad_hls/.test(this[ua]) // 联想lePad检测
    this.Mac = /macintosh/.test(this[ua])

    this.TT = /tencenttraveler/.test(this[ua])
    this.$360 = /360se/.test(this[ua])
    this.ff = /firefox/.test(this[ua])
    this.uc = /uc/.test(this[ua])

    this.Maxthon = false
  }
}

export default Browser
