import React from 'react'
import { mount } from 'enzyme'
import { VipGlobalPopupWithBtn } from '@/components/common/Vip/VipPopup'

const setup = option => {
  const sWrapper = mount(<VipGlobalPopupWithBtn {...option} />)
  return {
    sWrapper
  }
}

const props = {
  open: true,
  text: '1',
  btnText: '2',
  btnHrefOrClickFn: jest.fn(),
  showClose: true,
  closeFn: jest.fn()
}

const props1 = {
  text: '1',
  closeFn: jest.fn()
}

const props2 = {
  open: true,
  text: '1',
  closeFn: jest.fn()
}

describe('VipGlobalPopupWithBtn component', () => {
  it('props render', () => {
    setup(props)
  })

  it('props1 render', () => {
    setup(props1)
  })

  it('props2 render', () => {
    setup(props2)
  })
})
