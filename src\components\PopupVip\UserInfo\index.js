/**
 * @desc 新版本收银台用户信息部分
 * <AUTHOR>
 * @time 2022-01-28
 * @feature  GLOBALREQ-6796
 */

import React, { useState, useEffect } from 'react'
import { formatDate } from '@/utils/common'
import UserInfoWrapper from './style/index'
import { getUserInfo } from '../api'
import { isLogin } from '@/utils/userInfo'
const UserInfo = (props) => {
  const {
    hasCoupon = false,
    vipInfo = {},
    vipLangPkg = {},
    popLoading,
    startLogin,
    setManualRedeem,
    pbInfoStr
  } = props
  const [basicCard, setBasicCard] = useState({})
  const [hasVip, setHasVip] = useState(false)
  const [vipText, setVipText] = useState(
    vipLangPkg['PCW_VIP_1645092444144_267']
  )
  const ISLOGIN = isLogin()
  const [userInfo, setUserInfo] = useState({})
  useEffect(() => {
    async function fetData() {
      if (isLogin()) {
        const _userinfo = await getUserInfo() || {}
        setUserInfo(_userinfo)
      }
    }
    fetData()
  }, [])
  useEffect(() => {
    if (!popLoading) {
      if (vipInfo) {
        setBasicCard(vipInfo.commodityBasicCard)
        calUserVipInfo(vipInfo.userInfos)
      }
    }
  }, [popLoading])

  const calUserVipInfo = (vipList = []) => {
    // let vipExpire = 1
    // let firstVip = {}
    // let secondVip = {}
    // vipList.map((item, index) => {
    //   vipExpire = vipExpire & item.expire
    //   if (index === 0) {
    //     firstVip = item
    //   } else if (index === 1) {
    //     secondVip = item
    //   }
    // })
    // 未过期会员个数
    let notExpireList = vipList.filter((item) => !item.expire)
    const hasDeadline = vipList.filter((item) => item.vipDeadline)
    let noExpireLength = notExpireList.length
    let vipListLength = vipList.length

    if (hasDeadline.length !== 0) {
      // 会员全部过期
      if (noExpireLength < 1) {
        setHasVip(false)
        setVipText(vipLangPkg['PCW_VIP_1622013051261_600'])
      } else {
        setHasVip(true)
        let text = ''
        // 会员都没过期
        if (noExpireLength > 1) {
          notExpireList.sort((pre, next) => pre.vipDeadline - next.vipDeadline)
          let veriftyDate = false // 判断所有会员过期时间都是同一天
          const firstDate = new Date(+notExpireList[0].vipDeadline).toDateString()
          const lastDate = new Date(+notExpireList[noExpireLength - 1]?.vipDeadline).toDateString()
          console.log('lastDate-->', firstDate)
          console.log('firstDate-->', lastDate)
          if (firstDate === lastDate) {
            veriftyDate = true
          }
          if (
            notExpireList[0].vipDeadline ===
              notExpireList[noExpireLength - 1]?.vipDeadline || veriftyDate
          ) {
            text = vipLangPkg['PCW_VIP_1645410700824_272'].replace(
              '%s',
              formatDate(
                parseInt(notExpireList[noExpireLength - 1].vipDeadline, 10),
                'yyyy/MM/dd'
              )
            )
          } else {
            // 全部会员到期
            text = vipLangPkg['PCW_VIP_1645410736013_840'].replace(
              '%s',
              formatDate(
                parseInt(notExpireList[noExpireLength - 1]?.vipDeadline, 10),
                'yyyy/MM/dd'
              )
            )
          }
          setVipText(text)
        } else {
          let deadLineList = notExpireList.filter((item) => item.vipDeadline)
          let _vipItem = deadLineList[0] || {}
          text =
            _vipItem.vipTypeName +
            (vipLangPkg.PCW_VIP_1645412902825_961 || '').replace(
              /.*%s/,
              formatDate(parseInt(_vipItem.vipDeadline, 10), 'yyyy/MM/dd')
            )
          setVipText(text)
        }
        // if (firstVip.vipDeadline && secondVip.vipDeadline) {
        //   if (firstVip.vipDeadline === secondVip.vipDeadline) {
        //     // 会员时间相等
        //     text = vipLangPkg['PCW_VIP_1645410700824_272'].replace(
        //       '%s',
        //       formatDate(parseInt(firstVip.vipDeadline, 10), 'yyyy/MM/dd')
        //     )
        //     setVipText(text)
        //   } else {
        //     // 全部会员到期
        //     let time = Math.max(firstVip.vipDeadline, secondVip.vipDeadline)
        //     text = vipLangPkg['PCW_VIP_1645410736013_840'].replace(
        //       '%s',
        //       formatDate(time, 'yyyy/MM/dd')
        //     )
        //     setVipText(text)
        //   }
        // } else {
        //   // 有一个会员过期了,黄金未过期
        //   if (firstVip.vipDeadline && !secondVip.vipDeadline) {
        //     text =
        //       firstVip.vipTypeName +
        //       (vipLangPkg.PCW_VIP_1645412879051_317 || '').replace(
        //         /.*%s/,
        //         formatDate(parseInt(firstVip.vipDeadline, 10), 'yyyy/MM/dd')
        //       )
        //     setVipText(text)
        //   }
        //   // 钻石未过期
        //   if (!firstVip.vipDeadline && secondVip.vipDeadline) {
        //     text =
        //       secondVip.vipTypeName +
        //       (vipLangPkg.PCW_VIP_1645412902825_961 || '').replace(
        //         /.*%s/,
        //         formatDate(parseInt(secondVip.vipDeadline, 10), 'yyyy/MM/dd')
        //       )
        //     setVipText(text)
        //   }
        // }
      }
    }
  }

  const Login = async () => {
    if (!isLogin()) {
      startLogin()
      return
    }
  }

  const openVipCode = (url) => {
    if (url) {
      window.open(url, '_blank')
    }
  }

  return (
    <UserInfoWrapper>
      <div className="user-info-wrapper">
        {popLoading ? (
          <>
            <img
              src="//www.iqiyipic.com/new-vip/icon_head.png"
              alt=""
              className="user-avatar"
            />
            <div className="user-info-right loading-empty">
              <p className="user-name"></p>
              <p className="user-vip-info user-has-vip"></p>
            </div>
          </>
        ) : ISLOGIN ? (
          // 登录展示逻辑
          <>
            <img src={userInfo.icon} alt="" className="user-avatar" />
            <div className="user-info-right">
              <p className="user-name" title={userInfo.nickname}>
                {userInfo.nickname}
              </p>
              <p
                className={`user-vip-info ${hasVip ? 'user-has-vip' : ''}`}
                title={vipText}
              >
                {vipText}
              </p>
            </div>
          </>
        ) : (
          <>
            <img
              src="//www.iqiyipic.com/new-vip/icon_head.png"
              alt=""
              className="user-avatar"
              onClick={() => {
                Login()
              }}
            />
            <div
              className="user-info-right nologin-wrapper"
              onClick={() => {
                Login()
              }}
            >
              <p className="user-name">
                {vipLangPkg.PCW_VIP_1645091832006_959}
              </p>
              <p className="user-vip-info user-has-vip">
                {vipLangPkg.PCW_VIP_1645092444144_267}
              </p>
            </div>
          </>
        )}
      </div>
      <div className="header-btn-wrapper">
        {hasCoupon && !popLoading ? (
          <div className="coupon-btn-wrapper">
            <div className="coupon-icon-wrapper">
              <div className="coupon-icon"></div>
              <p
                rseat="coupon"
                data-pb={`rpage=cashier_popup&block=extend_info&${pbInfoStr}`}
                className="skip-link"
                onClick={() => {
                  setManualRedeem(true)
                }}
              >
                {vipLangPkg.PCW_VIP_1648808183025_426}
              </p>
            </div>
          </div>
        ) : (
          ''
        )}
        {basicCard && basicCard.showActCode === 1 ? (
          <div className="vip-code-wrapper">
            <div
              className="voucher-icon-wrapper"
              rseat="vip_exchange"
              data-pb={`rpage=cashier_popup&block=extend_info&${pbInfoStr}`}
              onClick={() => openVipCode(basicCard.skipUrl)}
            >
              <div className="voucher-icon"></div>
              <p className="skip-link">{basicCard.desc}</p>
            </div>
          </div>
        ) : (
          ''
        )}
      </div>
    </UserInfoWrapper>
  )
}

export default UserInfo
