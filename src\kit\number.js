export const formatPeriod = period => {
  if (!period) {
    return ''
  }
  period += ''
  if (period.indexOf('-') !== -1) {
    return period
  }
  return (
    period.slice(0, 4) + '-' + period.slice(4, 6) + '-' + period.slice(6, 8)
  )
}

export const formatYear = period => {
  if (!period) {
    return ''
  }
  period += ''
  return period.slice(0, 4)
}

export const isDigit = s => {
  if (!s || !/^[0-9]+$/.test(s)) {
    return false
  }
  return true
}

/**
 * 数组拆分
 */
export const sliceArray = (arr, num) => {
  const tmpArr = []
  let start = 0
  let end = start + num
  const len = arr.length
  if (num >= len) {
    return arr
  }
  while (num < len && start < len) {
    tmpArr.push(arr.slice(start, end))
    start += num
    end += num
  }
  return tmpArr
}

// 检查Date对象是否为Invalid Date
export const isValidDate = date => {
  return date instanceof Date && !Number.isNaN(date.getTime())
}

// 秒转换成小时，分钟
export const formatSeconds = s => {
  if (!isDigit(s)) {
    return '00:00:00'
  }
  const h = Math.floor(s / 3600)
  const m = Math.floor((s % 3600) / 60)
  return { h, m }
}
