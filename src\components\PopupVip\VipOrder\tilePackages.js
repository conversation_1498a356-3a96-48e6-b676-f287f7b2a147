import React from 'react'
import qs from 'qs'
import { getCookies } from '@/kit/cookie'
import TilePkgStyle from './style/tilePkg'
import { TileVipPreviledge, PkgDetail } from './common'

const TilePackages = props => {
  const { vipPkgList, handleSelect, selectedPkg, pbInfo } = props
  const selectId = selectedPkg.id
  const mod = getCookies('mod')
  return (
    <TilePkgStyle>
      {vipPkgList.map((item, i) => {
        const needPayFee = Number((item.needPayFee / 100).toFixed(2)).toString()
        const originalPrice = Number((item.originalPrice / 100).toFixed(2)).toString()
        const pbInfoStr = qs.stringify({
          ...pbInfo,
          position: item.index,
          v_pid: item.pid,
          v_prod: item.productSetCode,
          bstp: 56
        })
        return (
          <div className="card-warp" key={item.id}>
            {item.promotion && <p className="tag"><span>{item.promotion}</span></p>}
              <div
              className={'pkg-card ' + (selectId === item.id ? 'select' : '')}
              role="button"
              tabIndex={i}
              key={item.id}
              rseat={`${selectedPkg.index}:${item.index}`}
              data-pb={`rpage=cashier_popup&block=product_type&${pbInfoStr}`}
              onClick={() => {
                handleSelect(item)
              }}
            >
              <div className="pkg-card-main">
                <div className="title">
                  {item.vipTypeName}
                </div>
                <div className={`title sub-title`}>
                  {item.text3}
                </div>
                <div className="price">
                {mod === 'vn' ? (
                    <div className="real">
                      <span className="real-price">
                        {needPayFee  + ' '}
                      </span>
                      <span className="unit">{item.currencySymbol}</span>
                    </div>
                  ) : (
                    <div className="real">
                      <span className="unit">{item.currencySymbol + ' '}</span>
                      <span className="real-price">
                        {needPayFee}
                      </span>
                    </div>
                  )}
                  {item.needPayFee < item.originalPrice && (
                    <span className="origin-price">
                      {mod === 'vn'
                        ? originalPrice + ' ' + item.currencySymbol
                        : item.currencySymbol + ' ' +  originalPrice}
                    </span>
                  )}
                </div>
                <PkgDetail item={item} />
              </div>
              <TileVipPreviledge item={item.storeNodeLocations} />
            </div>
          </div>
        )
      })}
    </TilePkgStyle>
  )
}
export default TilePackages
