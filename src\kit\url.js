export const removeProtocol = url => {
  if (!url || typeof url !== 'string') {
    return url || ''
  }

  return url.replace(/^[\w]+:(?=[//])/, '')
}

export const handleImgSize = (url, size, hasProtocol) => {
  url = url || ''
  const _url = hasProtocol ? url : removeProtocol(url)
  if (_url.indexOf('.webp') !== -1) {
    return _url.replace('.webp', size)
  } else if (_url.indexOf('.jpg') !== -1) {
    return _url.replace('.jpg', size)
  } else {
    return _url
  }
}

export const queryFromUrl = (url, key) => {
  // if(!/(http(s)?:\/\/|\?)/.test(url)){ // 不加了，等会员重构
  //   //<AUTHOR> 兼容无url参数时候的状况
  //   key = url
  //   if(typeof global === 'object'){
  //     throw new Error('server side render must have url params')
  //   } else {
  //     url = window.location.href
  //   }
  // }
  const query = url.split('?')[1]
  if (query) {
    const vars = query.split('&')
    for (let i = 0; i < vars.length; i++) {
      const pair = vars[i].split('=')
      if (pair[0] === key) {
        return pair[1]
      }
    }
    return ''
  } else {
    return ''
  }
}
