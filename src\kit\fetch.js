/**
 * request
 */
import axios from 'axios'
// default config
axios.defaults.withCredentials = true
axios.defaults.timeout = 3000
axios.defaults.headers = {
  'content-type': 'application/x-www-form-urlencoded'
}
//检查响应状态
function checkStatus(response) {
  if (response.status >= 200 && response.status < 300) {
    //响应成功
    return response.data
  }
  const error = new Error(response.statusText)
  error.data = response
  throw error
}

async function $http(url, options = {}) {
  let contentType = options.contentType || ''
  const method = (options.method || 'GET').toUpperCase()
  // 为true时带cookie
  const credentials = options.credentials === true ? true : false
  const sendObj = {
    method
  }
  if (contentType === 'json') {
    sendObj['headers'] = {
      'Content-type': 'application/json;charset=UTF-8'
    }
  }
  if (options.headers) {
    sendObj.headers = {
      ...sendObj.headers,
      ...options.headers
    }
  }
  // console.log(options, '这里是几秒钟')
  if (options.timeout) {
    sendObj.timeout = options.timeout
  }
  sendObj.withCredentials = credentials
  const params = options.params
  try {
    let _res = {}
    if (method === 'POST') {
      sendObj['data'] = params
      sendObj.url = url
      _res = await axios(sendObj)
    } else {

      _res = await axios(url, options)
    }
    return checkStatus(_res)
  } catch (error) {
    // console.log('request error================',error)
    throw new Error('request error================' + error)
  }
}

export default $http
