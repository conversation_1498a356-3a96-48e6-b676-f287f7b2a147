import styled from 'styled-components'
const Wrapper = styled.div`
  .icon-passport {
    display: inline-block;
    overflow: hidden;
    font-size: 0;
    line-height: 0;
  }
  .icon-passport-outside__loading {
    background-image: url(//www.iqiyipic.com/global-passport/fix/passport-4963d29177.png);
    background-position: -180px -108px;
    width: 40px;
    height: 40px;
  }
  .passport-loading {
    position: absolute;
    height: 100%;
    width: 100%;
    top: 0;
    left: 0;
    background: transparent;
  }

  .passport-loading-outside__img {
    position: absolute;
    top: 50%;
    left: 50%;
    margin-left: -20px;
    margin-top: -30px;
    animation: loadingRotate 2s linear infinite;
  }
  .passport-loading-outside__txt {
    position: absolute;
    top: 50%;
    left: 50%;
    margin-top: 48px;
    // margin-left: -26px;
    color: #666;
    font-size: 14px;
    transform: translate(-50%, -50%);
  }
  @keyframes loadingRotate {
    0% {
      -webkit-transform: rotate(0deg);
    }
    50% {
      -webkit-transform: rotate(180deg);
    }
    100% {
      -webkit-transform: rotate(360deg);
    }
  }
`
export default Wrapper