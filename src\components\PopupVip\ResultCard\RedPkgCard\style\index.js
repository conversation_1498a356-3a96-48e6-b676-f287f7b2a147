import styled from 'styled-components'
const RedPacketWrapper = styled.div`
  width: 360px;
  margin: 0 auto;
  .redpkg-card-wrapper {
    width: 100%;
    margin-top: 16px;
    margin-bottom: 16px;
    border-radius: 6px;
    overflow: hidden;
    /* box-shadow: 0px 4px 10px 0px rgba(255,202,146,0.2); */
  }
  .redpkg-card-header {
    position: relative;
    padding-top: 36.6%;
    background: url(//www.iqiyipic.com/vip-result/img_redbag.png) no-repeat
      center;
    background-size: 100% 100%;
  }
  .red-header-content {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    flex-direction: column;
  }
  .red-value-title {
    font-size: 14px;
    color: #ffffff;
    text-align: center;
  }
  .red-value-wrapper {
    color: #ffffff;
    text-align: center;
    font-weight: 800;
  }
  .red-value-symbol {
    font-size: 24px;
    text-align: right;
  }
  .red-value-number {
    font-size: 36px;
    color: #ffffff;
    text-align: center;
    font-weight: 800;
    margin-bottom: 4px;
  }
  .redpkg-name {
    max-width: 248px;
    margin: 0 auto;
    font-size: 14px;
    color: #ffffff;
    text-align: center;
    font-weight: 700;
  }
  .red-pkg-detail {
    padding: 16px;
    border: 2px solid #f2bf83;
    border-top: 0px;
    /* box-shadow: 0px 4px 10px 0px rgba(255, 202, 146, 0.2); */
    border-radius: 0 0 6px 6px;
  }
  .red-pkg-title {
    font-size: 14px;
    color: #222222;
    font-weight: 400;
    text-align: left;
  }
  .red-pkg-share-btn {
    width: 100%;
    padding: 12px;
    margin-top: 16px;
    box-sizing: border-box;
    background-image: radial-gradient(
        circle at 85% 1e2%,
        #ff6200 0%,
        rgba(255, 0, 0, 0) 100%
      ),
      radial-gradient(circle at 0% 0%, #ffe3ae 0%, rgba(255, 205, 149, 0) 65%),
      linear-gradient(97deg, #ff6d32 0%, #ff4618 100%);
    border-radius: 4px;
    font-size: 16px;
    color: #ffffff;
    text-align: center;
    font-weight: 700;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
`

export default RedPacketWrapper
