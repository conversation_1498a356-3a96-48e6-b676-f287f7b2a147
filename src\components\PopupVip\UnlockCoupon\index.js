/**
 * @desc 新版本收银台解冻优惠券弹窗
 * <AUTHOR>
 * @time 2022-03-28
 * @feature  GLOBALREQ-6796
 */
import React, { useState } from 'react'
import $http from '@/kit/fetch'
import { getTimeZone } from '@/utils/common'
import { getCookies } from '@/kit/cookie'
import { FREEZE_COUPON } from '@/constants/interfaces'
import UnlockCouponWrapper from './style'
import Toast from '../Toast'
const lang = getCookies('lang') || 'en_us'
const mod = getCookies('mod') || 'intl'

const UnlockCoupon = (props) => {
  const [errorToast, setErrorToast] = useState('Freeze Fail')
  const [showToast, setShowToast] = useState(false)
  const [hasClick, setHasClick] = useState(false)
  const { vipLangPkg, createOrder, couponCode, hideFreeze } = props
  const freezeCoupon = async () => {
    if (hasClick) return
    setHasClick(true)
    const params = {
      timestamp: new Date().getTime(),
      couponCode,
      lang,
      app_lm: mod,
      timeZone: getTimeZone(getCookies('mod'))
    }
   
    const freezeRes = await $http(FREEZE_COUPON, { params })
    if (freezeRes.code === 'A00000') {
      setHasClick(false)
      hideFreeze()
      createOrder()
    } else {
      setHasClick(false)
      setErrorToast(freezeRes.message || 'Freeze Fail')
      setShowToast(true)
      setTimeout(() => {
        setShowToast(false)
      }, 800)
    }
  }
  return (
    <UnlockCouponWrapper>
      <div className="unlock-content-wrapper">
        <p className="unlock-content-title">
          {vipLangPkg.PCW_VIP_1648809866370_173}
        </p>
        <button className="btn-item unlock-btn" onClick={() => freezeCoupon()}>
          {vipLangPkg.PCW_VIP_1648807712780_205}
        </button>
        <button className="btn-item cancel-btn" onClick={() => hideFreeze()}>
          {vipLangPkg.PCW_VIP_1645426708050_573}
        </button>
      </div>
      {showToast ? <Toast msg={errorToast} /> : ''}
    </UnlockCouponWrapper>
  )
}
export default UnlockCoupon
