import React from 'react'
import { getCookies } from '@/kit/cookie'
import GloupPkgStyle from './style/gloupPkg'
import { VipPreviledge, PkgDetail } from './common'

const GroupPackages = props => {
  const {
    singleType,
    vipPkgList,
    handleSelect,
    storeNodeLocations,
    vipTypes,
    selectedPkg,
    pbInfo
  } = props
  const mod = getCookies('mod')
  const selectId = selectedPkg.id

  // const [selectId, setSelectId] = useState()
  if (!vipTypes) return null
  const vipPkgGroups = vipTypes.map(type => {
    const typeId = Number(type.id)
    return {
      id: typeId,
      name: type.name,
      pkgs: vipPkgList.filter(item => item.vipTypeId === typeId),
      storeNodeLocations: storeNodeLocations[typeId]
    }
  })

  return (
    <GloupPkgStyle className={singleType ? 'single' : ''}>
      {vipPkgGroups.map(type => (
        <div className="card" id={type.id}>
          <div className="card-top">
            <div className="type-name">{type.name}</div>
            <VipPreviledge item={type.storeNodeLocations} />
          </div>
          {type.pkgs.map((item, i) => {
            const needPayFee = Number((item.needPayFee / 100).toFixed(2)).toString()
            const originalPrice = Number((item.originalPrice / 100).toFixed(2)).toString()
            const pbInfoStr = qs.stringify({
              ...pbInfo,
              position: item.index,
              v_pid: item.pid,
              v_prod: item.productSetCode
            })
            return (
              <div
                className={'pkg-card ' + (selectId === item.id ? 'select' : '')}
                role="button"
                tabIndex={i}
                key={item.id}
                rseat={`${selectedPkg.index}:${item.index}`}
                data-pb={`rpage=cashier_popup&block=product_type&${pbInfoStr}`}
                onClick={() => {
                  handleSelect(item)
                }}
              >
                {item.promotion && <div className="label">{item.promotion}</div>}
                <div>
                  <div className="title">{item.text3}</div>
                  <PkgDetail item={item} />
                </div>
                <div className="price">
                  <div className="price-top">
                  {mod === 'vn' ? (
                      <>
                        <span className="real-price">
                          {needPayFee}
                        </span>
                        <span className="unit">{item.currencySymbol}</span>
                      </>
                    ) : (
                      <>
                        <span className="unit">{item.currencySymbol}</span>
                        <span className="real-price">
                          {needPayFee}
                        </span>
                      </>
                    )}
                  </div>
                  {item.needPayFee < item.originalPrice && (
                    <span className="origin-price">
                      {mod === 'vn'
                        ? originalPrice + item.currencySymbol
                        : item.currencySymbol + originalPrice}
                    </span>
                  )}
                </div>
              </div>
            )
          })}
        </div>
      ))}
    </GloupPkgStyle>
  )
}

export default GroupPackages
