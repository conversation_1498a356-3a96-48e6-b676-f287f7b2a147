import styled from 'styled-components'
const DiscountPopWrapper = styled.div`
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  font-size: 100px;
  color: #fff;
  background: rgba(0, 0, 0, 0.5);
  z-index: 10;
  .pop-close {
    position: absolute;
    top: -10%;
    right: 0;
    width: 26px;
    height: 26px;
    background: url(//www.iqiyipic.com/new-vip-pop/icon_close.png) no-repeat
      center;
    background-size: 100% 100%;
    cursor: pointer;
  }
  .discount-content-wrapper {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%) scale(0.6);
    opacity: 0;
    width: 360px;
    height: 365px;
    background: url('//www.iqiyipic.com/new-vip-pop/img_red.png') no-repeat
      center;
    background-size: 100% 100%;
    background-position: center;
    box-sizing: border-box;
  }
  .shake {
    animation: outScale 0.55s ease-in-out forwards;
  }
  @keyframes outScale {
    0% {
      transform: translate(-50%, -50%) scale(0.6);
      opacity: 0;
    }
    50% {
      transform: translate(-50%, -50%) scale(1.1);
      opacity: 1;
    }
    85% {
      transform: translate(-50%, -50%) scale(0.95);
      opacity: 1;
    }
    100% {
      transform: translate(-50%, -50%) scale(1);
      opacity: 1;
    }
  }
  .numver-wrapper {
    display: flex;
  }
  .hide-pop {
    opacity: 1;
    animation: hideScale 0.55s ease-in-out forwards;
  }

  @keyframes hideScale {
    0% {
      transform: translate(-50%, -50%) scale(1);
      opacity: 1;
    }

    100% {
      transform: translate(-50%, -50%) scale(0.2);
      opacity: 0;
    }
  }

  /* 头部样式 */
  .pop-mark-wrapper {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
  }
  .mark-title {
    height: 28px;
    line-height: 18px;
    max-width: 168px;
    min-width: 68px;
    background: url(//www.iqiyipic.com/new-vip-pop/img_tag_m.png) no-repeat top;
    background-size: 100% 100%;
    font-size: 13px;
    color: #ffffff;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    text-align: center;
  }
  .mark-title-inner {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    text-align: center;
  }
  .mark-left {
    position: absolute;
    left: -14px;
    top: 0;
    width: 14px;
    height: 28px;
    background: url(//www.iqiyipic.com/new-vip-pop/img_tag_l.png) no-repeat top;
    background-size: 100% 100%;
  }
  .mark-right {
    position: absolute;
    right: -14px;
    top: 0;
    width: 14px;
    height: 28px;
    background: url(//www.iqiyipic.com/new-vip-pop/img_tag_r.png) no-repeat top;
    background-size: 100% 100%;
  }
  /* 这里是金额部分的样式 */
  .discount-wrapper {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    align-content: center;
    width: 222px;
    height: 120px;
    padding: 20px 10px 10px;
    margin: 0 auto;
  }
  .price-outter {
    display: flex;
    align-items: baseline;
    justify-content: center;
  }
  .money-symbol {
    margin-right: 4px;
    font-size: 20px;
    color: #ff4f18;
    text-align: right;
    font-weight: 900;
  }
  .price-number {
    font-size: 64px;
    color: #ff4f18;
    text-align: right;
    line-height: 64px;
    font-weight: 900;
  }
  .discount-text {
    margin-left: 8px;
    font-size: 20px;
    color: #ff4f18;
    text-align: right;
    font-weight: 900;
  }
  .discount-title {
    width: 90%;
    margin: 8px auto 0;
    /* margin-top: 8px; */
    font-size: 14px;
    line-height: 16px;
    color: #ff4f18;
    text-align: center;
    font-weight: 700;
  }

  /* 计时部分的样式 */
  .time-counter-section {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    align-content: center;
    width: 270px;
    height: 110px;
    margin: 22px auto 0;
    padding: 12px 16px 22px;
    box-sizing: border-box;
  }

  .time-counter {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    font-size: 16px;
  }
  .count-item {
    width: 24px;
    height: 24px;
    line-height: 24px;
    margin-right: 4px;
    margin-left: 4px;
    font-size: 16px;
    color: #ff4f18;
    background: #fff;
    text-align: center;
    font-weight: 600;
    border-radius: 2px;
    &:first-child {
      margin-left: 0;
    }
    &:last-child {
      margin-right: 0;
    }
  }
  .time-counter-title {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;
    padding: 0 8px;
    margin-top: 10px;
    line-height: 16px;
    font-size: 14px;
    color: #ffffff;
    text-align: center;
    text-shadow: 0 1px 2px #ff3f00;
    font-weight: 700;
  }

  /* 按钮样式 */
  .submit-btn {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    width: 238px;
    bottom: 45px;
    padding: 10px 8px;
    margin: 0 auto;
    background: url(//www.iqiyipic.com/new-vip-pop/img_btnbg.png) no-repeat
      center;
    background-size: 100% 100%;
    font-size: 16px;
    color: #07080a;
    text-align: center;
    font-weight: 700;
    box-sizing: border-box;
    cursor: pointer;
  }

  .turn_box_container {
    margin-left: 10px;
  }

  .turn_box_container {
    position: relative;
    display: inline-block;
    float: left;
    overflow: hidden;
    background-color: red;
  }

  .turn_box {
    position: absolute;
    left: 0;
    top: 0;
    height: auto;
    width: 100%;
    transform-origin: 0 0;
    transition: top 0.3s;
  }

  .turn_box_number {
    line-height: 100px;
    font-size: 66px;
    font-family: MicrosoftYaHei-Bold;
    font-weight: bold;
    color: #4898f1;
    text-align: center;
  }
`

export default DiscountPopWrapper
