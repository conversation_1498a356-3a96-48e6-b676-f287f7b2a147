import $http from '@/kit/fetch'
import { USER_INFO_URL } from '@/constants/interfaces'
import { getCookies } from '@/kit/cookie'
import { platformId } from '@/kit/common'

// action types
export const GET_USER_INFO = 'get_user_info'
export const SET_USER_INFO = 'set_user_info'

// 初始状态
const initialState = {
  userInfo: {}
}

// actions
// 设置用户信息action
export const setUserInfoAction = (data) => ({
  type: SET_USER_INFO,
  data
})
// 获取用户信息action
export const getUserInfoAction = () => {
  return async (dispatch) => {
    const mod = getCookies('mod') || 'intl'
    const lang = getCookies('lang') || 'en_us'
    const params = {
      platformId: platformId(),
      modeCode: mod,
      langCode: lang,
      deviceId: getCookies('QC005'),
      fields: 'userinfo',
      version: '1.0'
    }
    try {
      const res = await $http(USER_INFO_URL, {
        credentials: true,
        method: 'GET',
        params
      })
      if (+res.code === 0) {
        dispatch(setUserInfoAction({ userInfo: res.data.userinfo }))
      }
    } catch (err) {
      console.log(err)
    }
  }
}

// Reducer
const userReducer = (state = initialState, action) => {
  switch (action.type) {
    case SET_USER_INFO:
      return {
        ...state,
        userInfo: action.data.userInfo
      }
    default:
      return state
  }
}
export default userReducer
