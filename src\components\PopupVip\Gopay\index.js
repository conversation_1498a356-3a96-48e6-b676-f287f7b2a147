/**
 * @desc 新版本收银台gopay支付
 * <AUTHOR>
 * @time 2022-6-22
 * @feature  GLOBALREQ-9423
 */
import React, { useState, useEffect, useRef } from 'react'
import { sendBlockPb } from '@/utils/pingBack'
import { getCookies } from '@/kit/cookie'
import GopayWrapper from './style/index'

const Gopay = (props) => {
  const mod = getCookies('mod')
  const {
    pbInfo,
    pbInfoStr,
    vipLangPkg,
    orderInfo,
    mobile,
    gopayError,
    gopayLoading,
    goPayResult,
    hide,
    fetchGopayData,
    clearError
  } = props
  const [showPhone, setShowPhone] = useState(!!mobile)
  const [inputMobile, setMobile] = useState(mobile || '')
  const [submitPhone, setSubmitPhone] = useState(mobile)
  const [mobileErr, setMobileErr] = useState(false)
  const [mobileFirst, setMobileFirst] = useState(false)
  const [inputFocus, setInputFocus] = useState(false)
  const inputRef = useRef()
  useEffect(() => {
    sendBlockPb('gopay_phone_num', {
      bstp: 56,
      rpage: 'cashier_popup',
      tjPb: {
        ...pbInfo
      }
    })
  }, [])
  const changeInput = () => {
    clearError()
    setShowPhone(!showPhone)
    if (!showPhone) {
      setMobileErr(false)
      setMobileFirst(false)
      setMobile(mobile.toString().replace(/(\d+)\d{4}(\d{4})/, '$1****$2'))
      setSubmitPhone(mobile)
    } else {
      setInputFocus(true)
      setMobile('')
    }
  }
  useEffect(() => {
    if (mobile) {
      setMobile(mobile.toString().replace(/(\d+)\d{4}(\d{4})/, '$1****$2'))
      setShowPhone(!!mobile)
      setSubmitPhone(mobile)
    }
  }, [mobile])
  // 返回上一层页面
  const goBack = () => {
    goPayResult('pkgSelect')
    clearError()
  }

  // gopay手机号
  const handleMobileChange = (e) => {
    const value = e.target.value.toString()
    const valNum = value.replace(/\s/g, '')
    if (valNum[0] === '8' && valNum.length > 12) return
    if (valNum.slice(0, 2) === '08' && valNum.length > 13) return
    setMobileErr(false)
    setMobile(value)
    setSubmitPhone(value)
  }

  const checkMobile = () => {
    const number = inputMobile.replace(/\s/g, '')
    if (!number.match(/^[\d\s]*$/)) {
      setMobileErr(true)
      return true
    }
    if (number[0] !== '8' && number.slice(0, 2) !== '08') {
      setMobileErr(true)
      return true
    }
    if (number[0] === '8' && number.length < 9) {
      setMobileErr(true)
      return true
    }
    if (number.slice(0, 2) === '08' && number.length < 10) {
      setMobileErr(true)
      return true
    }
  }

  // 提交gopay信息
  const submitGopay = () => {
    if (mobileErr && !showPhone) return
    let mobileNum = submitPhone
    if (submitPhone.slice(0, 2) === '08') {
      mobileNum = submitPhone.slice(1, submitPhone.length)
    }
    fetchGopayData(mobileNum)
  }
  return (
    <GopayWrapper>
      <div className="new-vip-result-header">
        <div
          role="button"
          tabIndex="0"
          className="back"
          data-pb={`rpage=gopay_phone_num&block=gopay_phone&${pbInfoStr}`}
          rseat="back"
          onClick={goBack}
        ></div>
        <div
          className="close"
          onClick={() => {
            hide()
          }}
          rseat="close"
          data-pb={`rpage=gopay_phone_num&block=gopay_phone&${pbInfoStr}`}
        ></div>
      </div>
      <div className="gopay-modal-wrapper">
        <div className="pkg-title-wrapper">
          <p className="gopay-pkg-title">
            {orderInfo.typeName} {orderInfo.name}
          </p>
          {mod === 'vn' ? (
            <p className="gopay-pkg-price">
              {orderInfo.payPrice}
              <i className="pkg-symbol">{orderInfo.currencySymbol}</i>
            </p>
          ) : (
            <p className="gopay-pkg-price">
              <i className="pkg-symbol">{orderInfo.currencySymbol}</i>
              {orderInfo.payPrice}
            </p>
          )}
        </div>
        <section className="gopay-detail-wrapper">
          <img
            src="//www.iqiyipic.com/new-vip/icon_pay_gopay.png"
            alt=""
            className="gopay-icon"
          />
          <div className="gopay-title-wrapper">
            <p className="gopay-title">
              {vipLangPkg.p_gopay_telephone_page_title}
            </p>

            {mobile && !showPhone ? (
              <p className="gopay-back" onClick={changeInput}>
                {vipLangPkg.PCW_VIP_1645426708050_573}
              </p>
            ) : (
              ''
            )}
          </div>
          {gopayError ? (
            <div className="pay-error-outer">
              <div className="pay-error-wrapper">
                <i className="error-icon"></i>
                <p className="pay-error-tip">{gopayError}</p>
              </div>
            </div>
          ) : (
            ''
          )}

          {showPhone ? (
            <div className="phonenumber-wrapper">
              <p className="phonenumber">
                +62 {mobile.toString().replace(/(\d+)\d{4}(\d{4})/, '$1****$2')}
              </p>
              <p
                className="change-phone"
                rseat="change"
                data-pb={`rpage=gopay_phone_num&block=gopay_phone&${pbInfoStr}`}
                onClick={changeInput}
              >
                {vipLangPkg.p_gopay_telephone_page_otherNO}
              </p>
            </div>
          ) : (
            <>
              <div
                className={`banknumber-input-wrapper ${
                  mobileErr ? 'has-error' : ''
                }`}
              >
                <div className="banknumber-input-label">
                  <div className="areacode">+62</div>
                  <input
                    type="type"
                    className="phonenumber-input-input"
                    autoComplete="on"
                    placeholder=" "
                    value={inputMobile || ''}
                    onBlur={(e) => checkMobile(e)}
                    onChange={(e) => handleMobileChange(e)}
                    onFocus={(e) => {
                      if (mobileFirst) {
                        return
                      }
                      setMobile('')
                      setMobileFirst(true)
                    }}
                    autoFocus={inputFocus}
                    ref={inputRef}
                    maxLength="12"
                  />
                  <span className="phonenumber-input-desc">
                    {vipLangPkg.p_gopay_telephone_page_title}
                  </span>
                </div>
                {mobileErr && (
                  <p className="mobile-error">
                    {vipLangPkg.p_gopay_telephone_page_checkNO}
                  </p>
                )}
              </div>
            </>
          )}

          <p className="gopay-desc">
            {vipLangPkg.p_gopay_telephone_page_description}
          </p>
          <div
            className="gopay-btn"
            rseat="pay"
            data-pb={`rpage=gopay_phone_num&block=gopay_phone&${pbInfoStr}`}
            onClick={submitGopay}
          >
            {gopayLoading ? (
              <img
                className="buy-loading"
                src="//www.iqiyipic.com/new-vip/blackloading.png"
                alt=""
              />
            ) : (
              <p className="buy-btn-text">
                {vipLangPkg.p_gopay_telephone_page_payment}
              </p>
            )}
          </div>
        </section>
      </div>
    </GopayWrapper>
  )
}

export default Gopay
