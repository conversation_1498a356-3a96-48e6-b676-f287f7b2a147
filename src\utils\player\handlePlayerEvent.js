let playerObj
let cacheList = []

export const savePlayerObj = (obj) => {
  playerObj = obj
}

export const jsSwitchVideo = (tvid, vid, imageLoader) => {
  if (playerObj) {
    playerObj.switchVideo({
      vid,
      tvid,
      imageLoader
    })
  }
}

// 添加联播列表
export const addToPlayList = (data) => {
  const list = data.list || []
  if (!playerObj) {
    cacheList = list
    return
  }
  if (cacheList.length === 0 && list.length === 0) {
    return
  }
  const opt = {
    index: 0,
    source: 3,
    after: data.after || false,
    before: data.before || false,
    handle: true,
    playSource: data.playSource || 3, // 1:来源,2:剧集,3:其他
    list: cacheList.length ? cacheList : list,
    vfrm: undefined
  }
  cacheList = []
  playerObj.addVideoList(opt)
}

export const removeVideoList = () => {
  if (playerObj) {
    playerObj.removeVideoList({ list: [] })
  }
}

// 播放器暂停播放
export const pause = () => {
  if (playerObj) {
    playerObj.pause()
  }
}

// 播放器恢复播放
export const resume = () => {
  if (playerObj) {
    playerObj.resume()
  }
}

// 广告播放器暂停播放
export const pauseAds = () => {
  if (playerObj) {
    playerObj.pauseAds()
  }
}

// 广告播放器恢复播放
export const resumeAds = () => {
  if (playerObj) {
    playerObj.resumeAds()
  }
}

const handleData = (data) => {
  const tempList = []
  const list = data.list
  const vfrm = data.vfrm
  const len = list.length
  for (let i = 0; i < len; i++) {
    const obj = list[i]
    const tmpObj = {
      tvId: obj.tvId,
      vid: obj.vid,
      albumId: obj.albumId,
      imageURL: obj.imgUrl,
      title: obj.name,
      describe: obj.desc,
      cid: obj.cid || '',
      publishTime: obj.publishTime,
      channelId: obj.channelId || '',
      exclusive: obj.exclusive || 0,
      playTime: obj.playTime || '',
      vfrm
    }
    tmpObj.isMemberVideo = 'false' // 目前都是免费视频
    tempList.push(tmpObj)
  }
  return tempList
}

export const addPlayList = (data) => {
  const list = handleData(data)
  addToPlayList(list)
}
