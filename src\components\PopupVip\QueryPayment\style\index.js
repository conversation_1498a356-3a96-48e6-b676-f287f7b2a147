import styled from 'styled-components'

const largeWidth = '504px'
const middleWidth = '532px'
const smallWidth = '311px'
const QueryPaymentWrapper = styled.div`
  .img-text {
    width: ${largeWidth};
    height: 68px;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    margin: 32px auto 12px;
    background: #F6F6F6;
    padding: 16px 0;
    border-top: 1px solid #E6E6E6;
    border-bottom: 1px solid #E6E6E6;
    img {
      width: 36px;
      height: 36px;
      margin-right: 12px;
      border-radius: 6px;
    }
    .text {
      width: 312px;
      line-height: 19px;
      font-size: 16px;
      color: #222222;
      letter-spacing: 0;
      font-weight: 500;
    }
    .link {
      width: 120px;
      line-height: 16px;
      font-size: 14px;
      color: #E09E51;
      letter-spacing: 0;
      text-align: right;
      font-weight: 400;
      margin-left: 24px;
      &:hover {
        text-decoration: underline;
      }
    }
  }
  .finish-text {
    margin: 0 auto 100px;
    width: ${largeWidth};
    line-height: 18px;
    font-size: 14px;
    color: #666666;
    letter-spacing: 0;
    line-height: 18px;
    font-weight: 400;
  }
  .got-problem {
    color: #E09E51;
    white-space: nowrap;
    &:hover {
      text-decoration: underline;
    }
  }
  .query-note {
    margin: -24px auto 40px;
    width: ${largeWidth};
    font-size: 14px;
    color: #666666;
    line-height: 18px;
  }
  .query-note-767,
  .got-problem-767 {
    display: none;
  }
  @media screen and (max-width: 1023px) {
    .img-text,
    .finish-text,
    .query-note {
      width: ${middleWidth};
    }
    .img-text {
      .text {
        width: 338px;
      }
    }
    
  }
  @media screen and (max-width: 767px) {
    .img-text {
      width: ${smallWidth};
      background: #FFFFFF;
      margin-top: 8px;
      img {
        width: 30px;
        height: 30px;
        margin-right: 8px;
      }
      .text {
        line-height: 15px;
        font-size: 13px;
        color: #222222;
        letter-spacing: 0;
        font-weight: 500;
      }
      .link {
        font-size: 13px;
        line-height: 15px;
      }
    }
    .finish-text {
      font-size: 13px;
      width: ${smallWidth};
      margin-bottom: 6px;
    }
    .query-note {
      display: none;
    }
    .query-note-767 {
      display: block;
      margin: 0 auto;
      width: ${smallWidth};
      line-height: 15px;
      font-size: 13px;
      color: #666666;
      letter-spacing: 0;
      line-height: 15px;
      font-weight: 400;
    }
    .got-problem-767 {
      display: block;
      width: ${smallWidth};
      height: 36px;
      margin: -10px auto 6px;
      line-height: 36px;
      font-size: 14px;
      color: #E09E51;
      text-align: center;
      font-weight: 500;
    }
  }
`
export default QueryPaymentWrapper
