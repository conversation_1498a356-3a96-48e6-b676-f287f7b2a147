import { hasAgentType } from '@/kit/common'
import { getCookies } from '@/kit/cookie'
import { isLogin } from '@/utils/userInfo'
import { getDevice } from '@/kit/device'
import $http from '@/kit/fetch'
import { RENEW_URL } from '@/constants/interfaces'
import { setCookies } from '@/kit/cookie'
export const initLogin = async (lang, ptid) => {
  if (isLogin()) {
    checkReNew(ptid)
    return
  }
  const globalLoginSdk = await import('@iqiyi-ibd/global-login-sdk')
  let loginSdk
  if (!loginSdk) {
    loginSdk = new globalLoginSdk.LoginSdk({
      agenttype: getDevice() === 'mobile' ? 479 : 426,
      ptid,
      lang,
      rpage: '', // TODO
    })
  }
  window.sdkPackManager = {
    globalLogin: loginSdk
  }
  loginSdk.init()
  loginSdk.on('login', () => {
    window.location.reload()
  })
  loginSdk.on('logout', () => {
  })
}

export const initLoginWindow = async (lang, ptid) => {
  const globalLoginSdk = await import('@iqiyi-ibd/global-login-sdk')
  let loginSdk
  if (!loginSdk) {
    loginSdk = new globalLoginSdk.LoginSdk({
      agenttype: getDevice() === 'mobile' ? 479 : 426,
      ptid,
      lang,
      rpage: '', // TODO
    })
  }
  window.sdkPackManager = {
    globalLogin: loginSdk
  }
  loginSdk.init()
  loginSdk.on('login', () => {
    window.location.reload()
  })
  loginSdk.on('logout', () => {
  })
}

export const checkReNew = async (ptid) => {
  const qdsf = require('@iqiyi/qdsf')
  const loginTimerCookie = getCookies('IQC163')
  if (!loginTimerCookie) {  
    const qc005 = getCookies('QC005')
    const tm = new Date().getTime()
    const agenttype = hasAgentType()
    const params = {
      agenttype,
      device_id: qc005,
      ptid
    }
    const paramStr = `agenttype=${agenttype}&device_id=${qc005}&ptid=${ptid}`
    const qdsfStr = qdsf.qdsf(0, paramStr, tm)
    params.qd_sf = qdsfStr
    const options = {
      params,
      withCredentials: true,
      timeout: 3000
    }
    try {
      const loginStatus = await $http(RENEW_URL, options)
      const loginIsExpired = loginStatus.code === 'A00001'
      setCookies('IQC163', '1', {
        time: 1000 * 60 * 60 * 24
      })
      if (loginIsExpired) {// 登录过期，执行退登操作 
        if (!(window.sdkPackManager && window.sdkPackManager.globalLogin)) {
          await initLogin()
        }
        window.sdkPackManager.globalLogin.logout()
      }
    } catch (e) {
      console.log(e)
    }
  }
}

