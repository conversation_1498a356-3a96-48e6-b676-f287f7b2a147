import styled from 'styled-components'
const BuyBtnWrapper = styled.div`
  position: absolute;
  bottom: 0px;
  width: 100%;
  box-sizing: border-box;
  padding: 0 40px 20px;

  background: #ffffff;
  border-top: 1px solid #f2f2f2;
  .agree-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-top: 16px;
  }
  .agree-tip-wrapper {
    position: absolute;
    left: -10px;
    bottom: calc(100% + 12px);
    max-width: 280px;
    padding: 12px;
    background: rgba(26, 28, 34, 1);
    border-radius: 4px;
    box-sizing: border-box;
    font-size: 14px;
    color: #ffffff;
    &:after {
      content: '';
      position: absolute;
      bottom: -15px;
      left: 11px;
      height: 1px;
      width: 1px;
      border: 7px solid transparent;
      border-top-color: rgba(26, 28, 34, 1);
    }
  }

  .checkbox-input {
    width: 16px;
    height: 16px;
    vertical-align: sub;
    overflow: visible;
    visibility: hidden;
    margin: 0;
    &:after {
      content: '';
      display: inline-block;
      width: 16px;
      height: 16px;
      border-radius: 2px;
      border: 1px solid rgba(153, 153, 153, 0.9);
      visibility: visible;
      line-height: 1;
      font-size: 16px;
      text-align: center;
      cursor: pointer;
      box-sizing: border-box;
    }
    &:hover:after {
      border-color: #000000;
    }
    &:checked::after {
      content: '';
      background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAIKADAAQAAAABAAAAIAAAAACshmLzAAACDklEQVRYCe1Xv2vCQBR+SUAo0sEfi9BF0Vlcuqr4J7iJgrPu3YTMXRyNuBcEx+7q5qR20EXHgluGbDpo70uNNHpHrMnVpQ9Ccu+9e993n3cnTyFm6XT66XA4vLKnwIYJ+CTaRlGUEXte1uv1pwLw/X7/wQCjEkF5pU1VVbNaJBIxWPSZlyHZ98DqJ9Sj7JKx+OWBrYIFP/wn3gQI3NX+CUhVQNM0yuVyhLfIpBEIhULU6/VoMBhQt9sljHmGe0DnBfz4AGYYBuXzebtMMpkky7JoNptdlA1cgXNwIG63Wy44YoESEIE3Gg2aTqfAu7DACPDAd7sdNZtNGo1GF8COQ0igUqnQZDKharXq5ArfInCsfDgcCuchwN2EpVKJ2u02hcNhKhaL9gaaz+fcQn7AUZCrQDwed4G1Wi2q1+suHwZ+wVGDq8BisaBoNErZbBY5tuFI4Sg5SgC80+lQoVA4ZhDhN79G9tME9sElgARsHBGJ5XIZCDhwlFQqdcCHyHRdp1qt5gqvVivKZDIn3y0rdyYLFXASeErEYjEnfJPsp8nsw5MAkkGCXdmuPQG/n5VjPuwqAkgcj8cuEkGAo67nHkDSTyuXy7YS/X6fcFr82q8J+AU8n8+9iM6TZI7/CUCBjUyJPWpvVDSKHknSwnaTevfm1DRNi12tb2yZaNHwP/wobcnfhdGev7POuIz2/AvFSt1GQ+UhGgAAAABJRU5ErkJggg==)
        no-repeat center;
      background-size: cover;
      background-color: #000000;
      border-color: #000000;
    }
  }
  .err-check-box {
    &:after {
      border-color: #ff0000;
    }
  }
  .agree-title {
    margin-left: 6px;
    font-size: 14px;
    color: #999999;
  }
  .agree-btn {
    margin-left: 4px;
    font-size: 14px;
    line-height: 20px;
    color: #999999;
    text-decoration: underline;
    cursor: pointer;
    &:hover {
      color: #e09e51;
      text-decoration: underline;
    }
    &:link {
      text-decoration: underline;
    }
  }
  /* 按钮部分 */
  .button-wrapper {
    display: flex;
    margin-top: 16px;
  }
  .final-price {
    /* display: ; */
    display: flex;
    /* flex-direction: column; */
    align-items: flex-start;
    align-content: center;
    justify-content: center;
    flex-wrap: wrap;
    padding: 6px 16px;
    height: 50px;
    min-width: 180px;
    background: #23252b;
    border-radius: 4px 0 0 4px;
    box-sizing: border-box;
  }
  .origin-price {
    width: 100%;
    font-size: 18px;
    color: #ffffff;
    line-height: 22px;
  }
  .price-off {
    width: 100%;
    font-size: 13px;
    color: #f2bf83;
    line-height: 16px;
    text-align: left;
  }
  .buy-btn {
    display: flex;
    flex: 1;
    justify-content: center;
    align-items: center;
    padding: 0 16px;
    background: #f2bf83;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
    box-sizing: border-box;
    &:hover {
      background: #f4cb9b;
      color: #404247;
    }
  }
  .buy-btn-text {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    font-size: 18px;
    color: #111319;
    /* line-height: 50px; */
    text-align: center;
  }
  .buy-loading {
    width: 20px;
    height: 20px;
    animation: Loading 1s linear infinite;
  }
  @keyframes Loading {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
`
export default BuyBtnWrapper
