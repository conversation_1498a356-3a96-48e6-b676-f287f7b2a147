import styled from 'styled-components'
const TabContentWapper = styled.div`
  /* min-height: 500px; */
  overflow: scroll;
  margin-bottom: 120px;
  overflow-x: hidden;
  -ms-overflow-style: -ms-autohiding-scrollbar;
  /* scrollbar-width: none;
  ::-webkit-scrollbar {
    width: 0;
  }
  ::-webkit-scrollbar-thumb {
    border-radius: 8px;
    background: #999;
    border: solid 4px #f0f0f0;
  }
  ::-webkit-scrollbar-track {
    padding-right: 4px;
    background: #f0f0f0;
  } */
  .tab-content-outter {
    /* position: relative; */
    padding: 40px 0 0px;
    background: #ffffff;
    /* min-height: 500px;
    max-height: 600px; */
    overflow-y: scroll;
    /* margin-bottom: 120px; */
  }

  .loading-img-wrapper {
    position: absolute;
    top: 55%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  .loading-img {
    width: 30px;
    height: 30px;
    animation: Loading 1s linear infinite;
  }
  @keyframes Loading {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
  .goods-list-wrapper {
    display: flex;
    justify-content: flex-start;
    /* overflow: auto; */
  }
  .tab-detail-wrapper {
    padding: 6px 0 0;
    background: #ffffff;

    /* margin-bottom: 30px; */
    /* overflow-y: scroll; */
  }
  .goods-item-wrapper {
    position: relative;
    width: calc((100% - 36px) / 4);
    flex-shrink: 0;
    padding: 16px 16px 14px;
    color: #222222;
    background: #f2f2f2;
    border: 2px solid #f2f2f2;
    border-radius: 4px;
    cursor: pointer;
    box-sizing: border-box;
    & + .goods-item-wrapper {
      margin-left: 12px;
    }
    &:hover {
      background: #ffffff;
      border-color: #f2bf83;
    }
    &.goods-item-focused {
      background: #fcf2e6;
      border-color: #f2bf83;
    }
    &.goods-item-ani {
      &:hover {
        background: #ffffff;
        border-color: #ff4f18;
      }
    }
    &.goods-item-ani-focus {
      border-color: #ff4f18;
      background: rgba(255, 79, 24, 0.08);
      &:hover {
        background: rgba(255, 79, 24, 0.08);
        border-color: #ff4f18;
      }
    }
  }
  .goods-promotion {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    position: absolute;
    top: -8px;
    right: -2px;
    padding: 2px 4px;
    font-size: 12px;
    line-height: 14px;
    color: #111319;
    background: #f2bf83;
    border-radius: 2px 2px 0 2px;
  }
  .goods-promition-ani {
    background: #ff4f18;
  }

  .goods-type {
    font-size: 14px;
    color: #222222;
    letter-spacing: 0;
    font-weight: bold;
    margin-bottom: 4px;
  }
  .goods-title-blank {
    min-height: 40px;
  }
  .goods-title {
    width: 100%;
    word-break: break-all;
    font-size: 14px;
  }
  .price-wrapper {
    position: absolute;
    bottom: 16px;
    left: 16px;
    right: 16px;
    height: 50px;
    margin-top: 10px;

    /* 这里是数字动效的字体设置 */
    .animated-container {
      text-align: right;
    }
    .number-item {
      font-size: 24px;
      font-weight: bold;
    }
  }
  .currency-icon {
    margin-right: 3px;
    font-size: 16px;
  }
  .goods-price {
    display: flex;
    align-items: baseline;
    font-size: 24px;
    font-weight: bold;
  }
  .goods-origin-price {
    font-size: 14px;
    color: #999999;
    text-decoration: line-through;
  }
  .goods-blank-div {
    height: 50px;
    margin-top: 10px;
  }
  .origin-ani-price {
    display: inline-block;
    padding: 0 6px;
    border: 1px solid rgba(255, 79, 24, 1);
    border-radius: 50px;
    font-size: 12px;
    color: #ff4f18;
    font-weight: bold;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    box-sizing: border-box;
  }
  .origin-ani-price-start {
    animation: Upto 0.2s ease-in-out forwards;
  }
  @keyframes Upto {
    0% {
      transform: translateY(14px);
      opacity: 0.5;
    }
    100% {
      transform: translateY(0);
      opacity: 1;
    }
  }
  /* 新加弹窗动效计时 */
  .goods-timecount-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: -8px;
    right: -2px;
    left: -2px;
    padding: 2px 4px;
    line-height: 14px;
    background: #ff4f18;
    text-align: center;
    border-radius: 4px 4px 0px 0px;
    box-sizing: border-box;
  }
  .goods-timecount-ani {
    animation: ToShow 0.2s ease-in forwards;
  }
  @keyframes ToShow {
    0% {
      opacity: 0.6;
      top: -100%;
    }
    100% {
      opacity: 1;
      top: -8px;
    }
  }
  .goods-timecount {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 12px;
    color: #ffffff;
  }
  .clock-icon {
    display: block;
    width: 16px;
    height: 16px;
    background: url(//www.iqiyipic.com/new-vip-pop/icon_time.png) no-repeat center;
    background-size: 100% 100%;
  }

  @media screen and (max-width: 1023px) {
    .goods-item-wrapper {
      width: calc((100% - 24px) / 3);
    }
    .loading-img-wrapper {
      /* position: absolute; */
      top: 58%;
      /* left: 50%; */
      transform: translate(-50%, -50%);
    }
  }
  @media screen and (max-width: 767px) {
    .goods-item-wrapper {
      width: calc((100% - 24px) / 3);
    }
  }
`
// import TabBgHover from '../img/tab-bg-hover.png'
const TabListWrapper = styled.div`
  background: #23252b;
  .tab-list {
    display: flex;
    justify-content: center;
  }
  .tab-item {
    position: relative;
    background-size: 100% 100%;
    flex: 1;
    padding: 12px 20px;
    z-index: 1;
    cursor: pointer;
    &:after {
      content: '';
      position: absolute;
      height: 106%;
      width: 101%;
      top: -2px;
      right: 0;
      bottom: 0;
      background-image: linear-gradient(45deg, #37383d 0%, #5d5e62 100%);
      border-radius: 4px 4px 0 0;
      transform: perspective(15px) scale(1, 1) rotateX(0.6deg);
      z-index: -1;
      transform-origin: bottom left;
    }
    & + .tab-item {
      margin-left: -3px;
    }
    &:first-child {
      &:after {
        left: 0;
        margin-right: -5px;
      }
      &.tab-active {
        z-index: 2;
        &:after {
          background: #f2bf83;
          transform-origin: bottom left;
        }
      }
    }
    &:last-child {
      margin-left: -8px;
      &:after {
        margin-left: -3px;
        right: -1px;
        transform-origin: bottom right;
      }
      &.tab-active {
        z-index: 2;
        &:after {
          background: #f2bf83;
          transform-origin: bottom right;
        }
      }
    }
    &:not(:first-child):not(:last-child) {
      &:after {
        transform-origin: bottom center;
      }
      &.tab-active {
        z-index: 2;
        &:after {
          background: #f2bf83;
        }
      }
    }
  }
  .tab-active {
    .tab-name {
      color: #111319;
    }
  }
  .tab-name {
    height: 22px;
    font-size: 16px;
    color: #ffffff;
    text-align: center;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
    white-space: break-spaces;
  }
`
export { TabContentWapper, TabListWrapper }
