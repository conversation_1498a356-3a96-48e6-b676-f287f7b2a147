function clipboardCopy(content) {
  if (navigator.clipboard) {
    return navigator.clipboard.writeText(content).then(
      () => {
        return Promise.resolve('Success')
      },
      () => {
        return Promise.reject(new Error('Copy faild'))
      }
    )
  } else {
    const span = document.createElement('span')
    const selection = window.getSelection()
    const range = window.document.createRange()
    let success = false
    span.textContent = content
    span.style.whiteSpace = 'pre'
    document.body.appendChild(span)
    selection.removeAllRanges()
    range.selectNode(span)
    selection.addRange(range)
    try {
      success = window.document.execCommand('copy')
    } catch (err) {
      console.log('error', err)
    }
    selection.removeAllRanges()
    window.document.body.removeChild(span)
    return success
      ? Promise.resolve('Success')
      : Promise.reject(new Error('Copy faild'))
  }
}

export default clipboardCopy
