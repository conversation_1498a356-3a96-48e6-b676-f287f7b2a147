import { getDevice, isServer } from '@/kit/device'
import { getCookies } from '@/kit/cookie'
import Browser from '@/kit/browser'
// import { publicRuntimeConfig } from '../../next.config'

export const mygetURLQuery = (url, name) => {
  const reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)')
  const hash = url.split('?')[1]
  const r = hash ? hash.match(reg) : null
  if (r != null) {
    return decodeURI(r[2])
  }
  return ''
}

export const locUrl = () => {
  let url = ''
  if (isServer) {
    url = global.pageReqUrl
  } else {
    url = window.location.href
  }
  return url
}

export const devHostName = () => {
  let host = ''
  if (isServer) {
    host = global.hostName
  } else {
    host = window.location.host
  }
  if (/\.qae/.test(host)) {
    // 判断是不是测试线qae机器过来的请求
    host = 'www.iq.com'
  }
  const pageUrl = locUrl()
  const userName = mygetURLQuery(pageUrl, 'user')
  if (userName) {
    host = 'www-test.iq.com'
  }

  return '//' + host + '/'
}

export const hostName = () => {
  let host
  if (getDevice() === 'pc') {
    host = '//www.iq.com/'
  } else {
    host = '//www.iq.com/'
  }
  if (isServer) {
    const isProd = process.env.BUILD_TAG === 'prod'
    if (!isProd) {
      host = devHostName()
    }
  } else {
    host = devHostName()
  }
  return host
}

// 此函数用不到了，mod从url.path中消失了
export const curlMod = () => {
  let mod = ''
  if (isServer) {
    mod = global.queryMod
  } else {
    mod = getCookies('intl_mod')
  }
  return mod
}

export const platformId = () => {
  if (getDevice() === 'pc') {
    return 3
  } else {
    // 移动端
    return 4
  }
}

export const commonParams = state => {
  let lang = 'en_us'
  let mod = 'intl'
  const params = {}
  params.platformId = platformId()
  if (state) {
    const modeLangObj = state.getIn(['language', 'modeLangObj'])
    lang = modeLangObj.get('lang')
    mod = modeLangObj.get('mod')
  }
  params.modeCode = mod
  params.langCode = lang
  // params.deviceId = getCookies('QC005')
  //  params.clientIp = '**************' // ***********
  return params
}

export const commonDeviceIdParams = state => {
  const params = commonParams(state)
  // 非iqiyi.com域名ajax第一次访问拿不到QC005
  params.deviceId = getCookies('QC005') || '1213'
  if (isServer) {
    params.deviceId = global.qc005Val || '1213'
  }

  return params
}

export const rebuildPlayUrl = id => {
  const host = hostName()
  // const mod = curlMod()
  // const url = host + mod + '/play?id=' + id
  let url = host + 'play/' + id
  const pageUrl = locUrl()
  if (pageUrl.indexOf('TESTINTLS') !== -1) {
    url = host + 'play/' + id + '/TESTINTLS'
  }
  const changeUrl = forceIntlUrl(url, pageUrl)
  return changeUrl
  // if (pageUrl.indexOf('_FORCE_INTL_SITE=INTL') !== -1) {
  //   url += '&_FORCE_INTL_SITE=INTL'
  // }
  // return url
  // return host + mod + '/play?id=404'
}

export const rebuildCommonUrl = path => {
  const host = hostName()
  // const mod = curlMod()
  const url = host + path
  const pageUrl = locUrl()
  // if (pageUrl.indexOf('_FORCE_INTL_SITE=INTL') !== -1) {
  //   url += '&_FORCE_INTL_SITE=INTL'
  // }
  const changeUrl = forceIntlUrl(url, pageUrl)
  return changeUrl
  // return host + mod + '/play?id=404'
}

export const forceIntlUrl = (url, pageUrl, tag) => {
  pageUrl = pageUrl || locUrl()
  const userName = mygetURLQuery(pageUrl, 'user')
  const zeusVal = mygetURLQuery(pageUrl, 'zeus')

  // 测试使用
  if (userName || zeusVal) {
    if (pageUrl.indexOf('?') === -1 || url.indexOf('?') === -1) {
      url += '?'
    } else {
      url += '&'
    }
    if (userName && zeusVal) url += `user=${userName}&zeus=${zeusVal}`
    else if (userName) url += `user=${userName}`
    else if (zeusVal) url += `zeus=${zeusVal}`
  }

  // pingback param
  if (tag === 'videochange') {
    const pbParams = {
      frmrp: mygetURLQuery(pageUrl, 'frmrp'),
      frmb: mygetURLQuery(pageUrl, 'frmb'),
      frmrs: mygetURLQuery(pageUrl, 'frmrs')
    }
    for (const name in pbParams) {
      const pbVal = pbParams[name] ? `${name}=${pbParams[name]}` : ''
      if (pbVal) {
        if (url.indexOf(name) === -1) {
          if (url.indexOf('?') === -1) {
            url += `?${pbVal}`
          } else {
            url += `&${pbVal}`
          }
        } else {
          url.replace(`/${name}=[^&]+/`, `${name}=${pbParams[name]}`)
        }
      }
    }
  }

  return url
}

// uc浏览器video标签层级最高，故打开汉堡菜单时video隐藏
export const hidVideoDom = showTag => {
  const browser = new Browser()
  if (browser.muc) {
    const videoTag = document.getElementsByTagName('video') || []
    const videoDom = videoTag[0]
    if (!videoDom) {
      return
    }
    if (showTag) {
      videoDom.style.display = 'none'
    } else {
      videoDom.style.display = ''
    }
  }
}

// 判断浏览器是否支持webp格式
export const canUseWebp = () => {
  let tag = 0
  if (isServer) {
    tag = global.useWebpTag
  } else {
    tag =
      !![].map &&
      document
        .createElement('canvas')
        .toDataURL('image/webp')
        .indexOf('data:image/webp') === 0
  }
  return tag
}
export const hasAgentType = () => (getDevice() === 'pc' ? 426 : 479)

// 处理lang: en_us -> en_US
export const handleLang = lang => {
  if (!lang) {
    return ''
  }
  return lang.replace(/_\w+$/, word => {
    return word.toUpperCase()
  })
}

// 判断是不是图片
export const isImage = src => {
  return /\.(gif|jpg|jpeg|png|GIF|JPG|PNG|webp)$/.test(src)
}

// 返回rebuildCommonUrl后且是https协议的完整url
export const rebuildHttpsUrl = path => {
  let rebuildUrl = rebuildCommonUrl(path)
  // 需要加上协议
  const isHttp = rebuildUrl.substr(0, 7).toLowerCase()
  const isHttps = rebuildUrl.substr(0, 8).toLowerCase()
  if (isHttp !== 'http://' && isHttps !== 'https://') {
    rebuildUrl = 'https:' + rebuildUrl
  }
  return rebuildUrl
}

export const toPercent = (point, decimal) => {
  let str = Number(point * 100).toFixed(decimal)
  str += '%'
  return str
}

export const formatNumber = num => {
  let result = num
  if (num < 1e3) {
    return num
  } else if (num < 1e6) {
    result = (num / 1e3).toFixed(1) + 'k'
  } else {
    result = (num / 1e6).toFixed(1) + 'm'
  }
  return result
}
