/*
 * NEW_VIP
 */
import { useContext } from 'react'

function _getLanguage(langPackage, name, lang) {
  let language = ''
  try {
    language = langPackage[name][lang]
  } catch (error) {
    console.error(`Can't get language with name - ${name}; lang - ${lang}`)
  }
  return language
}

/**
 * 获取语言方法，支持重载
 *
 * @param langPackage 当前语言包配置
 * @param name 可以是数组或者字符串
 * @param langOrPageInfoContext 语言或者页面Context
 */

function getLanguage(langPackage, names, langOrPageInfoContext) {
  const defaultLang = 'en_us'
  const { lang = defaultLang } =
    langOrPageInfoContext.length > 0
      ? { lang: langOrPageInfoContext }
      : useContext(langOrPageInfoContext)

  if (typeof names === 'string') {
    return _getLanguage(langPackage, names, lang)
  } else {
    return names.map(name => _getLanguage(langPackage, name, lang))
  }
}

export { getLanguage }
