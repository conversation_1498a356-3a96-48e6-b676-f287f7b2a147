import styled from 'styled-components'

const ModalWrapper = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  z-index: 1;
  .ds-modal-wrapper {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 400px;
    padding: 40px;
    background: #ffffff;
    border-radius: 8px;
    text-align: center;
    box-sizing: border-box;
  }
  .ds-modal-close-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: 12px;
    right: 12px;
    width: 24px;
    height: 24px;
    cursor: pointer;
    g {
      fill: #666666;
    }
    &:hover {
      g {
        fill: #222222;
      }
    }
  }

  .ds-modal-title {
    margin-bottom: 16px;
    font-size: 16px;
    font-weight: bold;
    color: #222222;
    text-align: center;
  }
  .ds-modal-subtitle {
    padding-bottom: 8px;
    font-size: 14px;
    font-weight: 400;
    color: #666666;
  }
  .ds-modal-btn {
    position: relative;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    width: 100%;
    height: 44px;
    padding: 0 24px;
    margin: 16px auto 0;
    font-size: 16px;
    line-height: 44px;
    background: #f2bf83;
    color: #111319;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
    font-weight: bold;
    border: none;
    outline: none;
    border-radius: 4px;
    &:hover {
      &:after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 4px;
        background: rgba(255, 255, 255, 0.2);
      }
    }
  }
  @media screen and (max-width: 767px) {
    & .ds-modal-mobile {
      width: 270px;
      padding: 16px;
      .ds-modal-close-wrapper {
        top: 6px;
        right: 6px;
      }
      .ds-modal-subtitle {
        font-size: 13px;
      }
      .ds-modal-btn {
        min-width: 88px;
        height: 36px;
        line-height: 36px;
        padding: 0 12px;
        margin-top: 16px;
        font-size: 14px;
        &:hover {
          &:after {
            display: none;
          }
        }
      }
    }
  }
`

export default ModalWrapper
