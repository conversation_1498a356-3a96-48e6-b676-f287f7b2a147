import React from 'react'
import { connect } from 'react-redux'
import qs from 'qs'
import { intlVipPayResultInterface } from '@/constants/interfaces'
import $http from '@/kit/fetch'
import { formatDate } from '@/utils/common'
import { getUid } from '@/utils/userInfo'
import { getCookies } from '@/kit/cookie'
import TemplateErrorPop from '../Error'
import PayResultWrapper from './style'

class PayResult extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      resultData: null
    }
  }

  async componentDidMount() {
    await this.fetchPayResult()
  }

  fetchPayResult = async () => {
    const { resultInfo, setErrorPop, setResultSuccess } = this.props
    const { platform, mod, lang, timeZone, vipOrder } = resultInfo
    try {
      const data =
        (await $http(intlVipPayResultInterface, {
          method: 'POST',
          credentials: true,
          params: qs.stringify({
            platform,
            app_lm: mod,
            lang,
            timeZone,
            version: '1.0',
            orderCode: vipOrder
          })
        })) || {}

      // 添加线上日志
      const addloggers = {
        popupPayResult: {
          time: new Date(),
          uid: getUid(),
          data
        }
      }
      const loggers = localStorage.getItem('QiyiPlayerLogger')
      window.localStorage.setItem(
        'QiyiPlayerLogger',
        loggers + JSON.stringify(addloggers)
      )
      if (data.code === 'A00000' || data.code === 'Q00301') {
        this.setState({
          resultData: data
        })
        const i18nOrderInfo = data.data.i18nOrderInfo || {}
        window.gtag &&
          window.gtag('event', 'in_web_purchase', {
            currency: i18nOrderInfo.currencyUnit,
            value: i18nOrderInfo.realFee / 100,
            mod: mod || 'intl',
            rpage: 'cashier_popup'
          })
        setResultSuccess(true)
      } else if (data.code === 'Q00332') {
        setErrorPop('timeout')
      } else {
        this.setState({
          resultData: {
            msg: '__sysErr'
          }
        })
      }
    } catch (error) {
      if (error.message.match('timeout')) {
        this.setState({ resultData: { code: 'Q00332', message: 'timeout' } })
        setErrorPop('timeout')
      }
    }
  }

  render() {
    const { params, vipLangPkg, setStep, setHide, setResultSuccess, userInfo } =
      this.props
    const { nickname } = userInfo
    const { resultData } = this.state
    const mod = getCookies('mod')
    let success = resultData && resultData.code === 'A00000'
    // 单独处理支付中的状态 http://pms.qiyi.domain/browse/GLOBALREQ-6504
    const pending = resultData && resultData.code === 'Q00301'
    let successInfo = []
    let i18nOrderInfo = {}
    if (success) {
      i18nOrderInfo = resultData.data.i18nOrderInfo
      success = success && i18nOrderInfo.status === 1
      if (i18nOrderInfo.i18nName) {
        successInfo.push({
          key: vipLangPkg.pcashier_result_plan,
          value: i18nOrderInfo.i18nName || ''
        })
      }
      if (params.cashierType === 2 && !params.isLiveTvod) {
        const deadline = formatDate(
          i18nOrderInfo.deadline,
          'yyyy-MM-dd HH:mm:ss'
        )
        successInfo.push({
          key: vipLangPkg.viewing_period,
          value: vipLangPkg.tvod_expiry.replace('%s', deadline)
        })
      }
      if (params.cashierType === 2 && params.isLiveTvod) {
        successInfo.push({ key: vipLangPkg.pcashier_result_name, value: nickname })
      }

      // successInfo = [

      // {
      //   key: vipLangPkg.pcashier_result_nextDate,
      //   value: formatDate(i18nOrderInfo.deadline, 'yyyy/MM/dd')
      // }
      // ]
    }
    return (
      <PayResultWrapper>
        {!resultData && (
          <div className="scroll-container">
            <div className="scroll-content"></div>
          </div>
        )}
        {resultData && success && (
          <>
            <div className="scroll-container">
              <div className="scroll-content">
                <div className="step-icon"></div>
                <div className="step-title">
                  {vipLangPkg.pcashier_result_success}
                </div>
                <div className="step-tip">
                  {params.isLiveTvod ? null : vipLangPkg.pcashier_result_welcome}
                </div>
                {successInfo.length ? (
                  <div className="success-info">
                    <div className="success-detail">
                      {vipLangPkg.pcashier_result_accountDetail}
                    </div>
                    <ul className="success-list">
                      {successInfo.map((item) => {
                        if (item.value !== '') {
                          return (
                            <li className="item-row">
                              <span className="item-key">{`${item.key}:`}</span>
                              <span className="item-value">{item.value}</span>
                            </li>
                          )
                        } else {
                          return null
                        }
                      })}
                    </ul>
                    <div className="total-price">
                      <span className="total">
                        {vipLangPkg.pcashier_result_total}
                      </span>
                      <span className="price">
                        {mod === 'vn'
                          ? Number(
                              (i18nOrderInfo.realFee / 100).toFixed(2)
                            ).toString() + i18nOrderInfo.currencySymbol
                          : i18nOrderInfo.currencySymbol +
                            Number(
                              (i18nOrderInfo.realFee / 100).toFixed(2)
                            ).toString()}
                      </span>
                    </div>
                  </div>
                ) : (
                  ''
                )}
              </div>
            </div>
            {/* <div className="success-container">
              <h3 className="success-title">
                {vipLangPkg.pcashier_result_welcome}
              </h3>
            </div> */}
            <button
              type="button"
              className="containue"
              onClick={() => {
                // window.location.reload()
                setResultSuccess(true)
                setHide()
              }}
            >
              {vipLangPkg.pcashier_result_finishButton}
            </button>
          </>
        )}
        {resultData && pending && (
          <TemplateErrorPop
            image="//www.iqiyipic.com/common/fix/global/pay_fail.png"
            title={vipLangPkg.pcashier_result_processing}
            btnText={vipLangPkg.pcashier_result_refresh}
            btnClick={() => {
              this.fetchPayResult()
            }}
          />
        )}
        {resultData && !success && !pending && (
          <TemplateErrorPop
            image="//www.iqiyipic.com/common/fix/global/pay_fail.png"
            title={vipLangPkg.pcashier_result_fail}
            btnText={vipLangPkg.pcashier_result_backPlan}
            btnClick={() => {
              setStep(
                params.cashierType === 2 ? 'vodOrderStep' : 'vipOrderStep'
              )
            }}
          />
        )}
      </PayResultWrapper>
    )
  }
}

const mapStateToProps = (state) => ({
  userInfo: state['user']['userInfo']
})

export default connect(mapStateToProps)(PayResult)
