import React from 'react'

const I<PERSON><PERSON>Logo = () => (
  <svg
    viewBox="0 0 106 85"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    fill="#0BBE06"
  >
    <path d="M105.1468,21.1958 C105.1468,20.7428 104.7828,20.3798 104.3258,20.3798 L96.1748,20.3798 C95.7278,20.3798 95.3638,20.7428 95.3638,21.1958 L95.3638,63.6108 C95.3638,64.0718 95.7278,64.4188 96.1748,64.4188 L104.3258,64.4188 C104.7828,64.4188 105.1468,64.0718 105.1468,63.6108 L105.1468,21.1958 Z" />
    <path d="M28.7298,60.772 C27.2008,60.772 25.9808,59.855 25.9808,58.721 L25.9808,26.502 C25.9808,25.376 27.2008,24.459 28.7298,24.459 C30.2458,24.459 31.4698,25.376 31.4698,26.502 L31.4698,58.721 C31.4698,59.855 30.2458,60.772 28.7298,60.772 M41.6908,64.479 L37.8488,64.479 C36.9448,64.479 36.4228,64.459 36.3248,64.211 C39.2418,62.868 41.1298,60.772 41.1298,58.4 L41.1298,26.502 C41.1298,22.439 35.5598,19.146 28.7058,19.146 C21.8538,19.146 16.2768,22.439 16.2768,26.502 L16.2768,58.4 C16.2768,62.474 21.8538,65.772 28.7058,65.772 C29.6888,65.772 30.6538,65.689 31.5778,65.569 C31.6708,66.672 31.9568,67.21 32.6398,67.934 C33.6488,68.803 34.8978,69.168 37.1748,69.199 L41.6908,69.199 C42.1578,69.199 42.5168,68.845 42.5168,68.403 L42.5168,65.312 C42.5168,64.395 41.6908,64.479 41.6908,64.479" />
    <path d="M8.972,20.3799 L0.821,20.3799 C0.364,20.3799 0,20.7429 0,21.1959 L0,29.3529 C0,29.8039 0.364,30.1779 0.821,30.1779 L8.972,30.1779 C9.434,30.1779 9.779,29.8039 9.779,29.3529 L9.779,21.1959 C9.779,20.7429 9.434,20.3799 8.972,20.3799" />
    <path d="M56.6536,20.4507 L48.5036,20.4507 C48.0556,20.4507 47.6966,20.8047 47.6966,21.2627 L47.6966,63.6607 C47.6966,64.1297 48.0556,64.4787 48.5036,64.4787 L56.6536,64.4787 C57.0916,64.4787 57.4456,64.1297 57.4456,63.6607 L57.4456,21.2627 C57.4456,20.8047 57.0916,20.4507 56.6536,20.4507" />
    <path d="M8.972,34.7549 L0.821,34.7549 C0.364,34.7549 0,35.1209 0,35.5649 L0,63.6109 C0,64.0719 0.364,64.4189 0.821,64.4189 L8.972,64.4189 C9.434,64.4189 9.779,64.0719 9.779,63.6109 L9.779,35.5649 C9.779,35.1209 9.434,34.7549 8.972,34.7549" />
    <path d="M91.6399,21.4829 C91.6689,21.3789 91.6949,21.3019 91.6949,21.1959 C91.6949,20.7759 91.2019,20.4329 90.7749,20.3949 L82.5759,20.3799 C81.9649,20.3799 81.7729,20.8969 81.7729,20.9229 L76.4269,34.6909 L71.0499,20.8639 C70.9169,20.5669 70.6419,20.3799 70.2979,20.3799 L61.6809,20.3949 C61.2569,20.4329 61.0949,20.7759 61.0949,21.1959 C61.0949,21.3019 61.1339,21.4829 61.1339,21.4829 L71.3549,47.8849 L71.3549,63.5879 L71.3549,63.6109 C71.3549,64.0719 71.7199,64.4189 72.1609,64.4189 L75.6439,64.4189 L76.8259,64.4189 L80.5869,64.4189 C81.0449,64.4189 81.4099,64.0719 81.4099,63.6109 L81.4099,63.5879 L81.4099,47.8849 L91.6399,21.4829 Z" />
    <path d="M105.1126,16.2329 C104.9546,14.0009 104.3496,11.0249 102.9096,8.9949 C101.4626,6.9249 100.0606,5.9249 98.0686,5.0189 C92.2396,2.3429 74.0756,-0.0001 52.5916,-0.0001 L52.5816,-0.0001 C31.0816,-0.0001 12.9056,2.3429 7.0876,5.0189 C5.0916,5.9249 3.6886,6.9249 2.2176,8.9949 C0.8066,11.0249 0.1866,14.0009 0.0296,16.2329 C0.0096,16.4969 0.1326,16.9979 0.6396,16.9979 L9.2476,16.9979 C9.7786,16.9979 9.8426,16.5829 9.9556,16.2539 C10.2996,15.2729 11.0816,13.7249 12.0206,12.8929 C13.6276,11.4649 14.7126,11.0499 16.9206,10.7369 C25.8916,9.4229 40.2106,8.7229 52.5816,8.7229 C64.9266,8.7229 79.2646,9.4229 88.2216,10.7369 C90.4296,11.0499 91.5226,11.4649 93.1156,12.8929 C94.0656,13.7249 94.8666,15.2729 95.1906,16.2539 C95.2996,16.5829 95.3636,16.9979 95.9096,16.9979 L104.5226,16.9979 C105.0336,16.9979 105.1416,16.4969 105.1126,16.2329" />
    <path d="M105.1126,68.7549 C104.9546,70.9649 104.3496,73.9479 102.9096,75.9869 C101.4626,78.0439 100.0606,79.0519 98.0686,79.9519 C92.2396,82.6129 74.0756,84.9569 52.5916,84.9569 L52.5816,84.9569 C31.0816,84.9569 12.9056,82.6129 7.0876,79.9519 C5.0916,79.0519 3.6886,78.0439 2.2176,75.9869 C0.8066,73.9479 0.1866,70.9649 0.0296,68.7549 C0.0096,68.4489 0.1326,67.9669 0.6396,67.9669 L9.2476,67.9899 C9.7786,67.9899 9.8426,68.4129 9.9556,68.6899 C10.2996,69.6989 11.0816,71.2519 12.0206,72.0899 C13.6276,73.5049 14.7126,73.9179 16.9206,74.2459 C25.8916,75.5479 40.2106,76.2539 52.5816,76.2539 C64.9266,76.2539 79.2646,75.5479 88.2216,74.2459 C90.4296,73.9179 91.5226,73.5049 93.1156,72.0899 C94.0656,71.2519 94.8666,69.6889 95.1906,68.6899 C95.2996,68.4129 95.3636,67.9899 95.9096,67.9899 L104.5226,67.9669 C105.0336,67.9669 105.1416,68.4489 105.1126,68.7549" />
  </svg>
)

const ArrowLeft = () => (
  <svg
    width="24px"
    height="24px"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g transform="translate(-657.000000, -592.000000)">
      <path
        d="M671.914331,604.29542 L663.271795,595.157594 C662.908483,594.773462 662.906982,594.172892 663.268369,593.786948 L663.704416,593.321271 C664.081904,592.918132 664.714728,592.897338 665.117867,593.274827 C665.132636,593.288656 665.146982,593.302931 665.160886,593.317631 L674.728205,603.433238 C674.953663,603.671616 675.039788,603.993345 674.986323,604.295408 C675.039791,604.597474 674.953667,604.919206 674.728207,605.157587 L665.160877,615.273201 C664.781379,615.674448 664.148459,615.692078 663.747212,615.31258 C663.732512,615.298676 663.718237,615.28433 663.704408,615.269561 L663.268368,614.803891 C662.906981,614.417947 662.908482,613.817377 663.271793,613.433245 L671.914331,604.29542 Z"
        transform="translate(669.000000, 604.295416) rotate(-180.000000) translate(-669.000000, -604.295416) "
      />
    </g>
  </svg>
)

const ArrowRight = () => (
  <svg
    width="24px"
    height="24px"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g transform="translate(-657.000000, -562.000000)">
      <path d="M672.914331,574.29542 L664.271795,565.157594 C663.908483,564.773462 663.906982,564.172892 664.268369,563.786948 L664.704416,563.321271 C665.081904,562.918132 665.714728,562.897338 666.117867,563.274827 C666.132636,563.288656 666.146982,563.302931 666.160886,563.317631 L675.728205,573.433238 C675.953663,573.671616 676.039788,573.993345 675.986323,574.295408 C676.039791,574.597474 675.953667,574.919206 675.728207,575.157587 L666.160877,585.273201 C665.781379,585.674448 665.148459,585.692078 664.747212,585.31258 C664.732512,585.298676 664.718237,585.28433 664.704408,585.269561 L664.268368,584.803891 C663.906981,584.417947 663.908482,583.817377 664.271793,583.433245 L672.914331,574.29542 Z" />
    </g>
  </svg>
)

const MenuIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 18">
    <path
      d="M18.229 14.843c.6 0 1 .4 1 1v.3c0 .6-.4 1-1 1h-16.7c-.6 0-1-.4-1-1v-.3c0-.6.4-1 1-1h16.7zm0-7c.6 0 1 .4 1 1v.3c0 .6-.4 1-1 1h-16.7c-.6 0-1-.4-1-1v-.3c0-.6.4-1 1-1h16.7zm0-7c.6 0 1 .4 1 1v.3c0 .6-.4 1-1 1h-16.7c-.6 0-1-.4-1-1v-.3c0-.6.4-1 1-1h16.7z"
      fillRule="evenodd"
      clipRule="evenodd"
      fill="#fff"
    />
  </svg>
)

const PlayIcon = () => (
  <svg
    width="22px"
    height="21px"
    viewBox="0 0 22 21"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <g transform="translate(-524.000000, -438.000000)" fill="#FFFFFF">
        <path
          d="M536.910331,442.082546 L544.593312,454.213569 C544.888813,454.68015 544.750126,455.297939 544.283545,455.59344 C544.123494,455.694805 543.937942,455.748621 543.748493,455.748621 L528.38253,455.748621 C527.830246,455.748621 527.38253,455.300906 527.38253,454.748621 C527.38253,454.559172 527.436346,454.373619 527.537711,454.213569 L535.220692,442.082546 C535.516194,441.615965 536.133983,441.477278 536.600564,441.772779 C536.725445,441.85187 536.831239,441.957664 536.910331,442.082546 Z"
          transform="translate(536.065576, 448.683045) rotate(-270.000000) translate(-536.065576, -448.683045) "
        />
      </g>
    </g>
  </svg>
)

const DetailArrowIcon = () => (
  <svg
    width="260px"
    height="10px"
    viewBox="0 0 260 10"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <g transform="translate(-301.000000, -370.000000)" fill="#0BBE06">
        <path d="M437.038996,374.142549 L431.721392,379.681794 C431.33892,380.080208 430.705887,380.093132 430.307473,379.710659 C430.297656,379.701235 430.288032,379.691612 430.278608,379.681794 L424.980676,374.163042 L424.961004,374.142549 L301,374.142549 L301,370 L561,370 L561,374.142549 L437.038996,374.142549 Z" />
      </g>
    </g>
  </svg>
)

const CloseIcon = () => (
  <svg
    width="21px"
    height="21px"
    viewBox="0 0 21 21"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g stroke="none" strokeWidth="1" fillRule="evenodd">
      <g transform="translate(-614.000000, -438.000000)">
        <path
          d="M626.11988,450.11988 L626.11988,460.927572 C626.11988,461.756 625.448307,462.427572 624.61988,462.427572 L624.542957,462.427572 C623.71453,462.427572 623.042957,461.756 623.042957,460.927572 L623.042957,450.11988 L612.235265,450.11988 C611.406838,450.11988 610.735265,449.448307 610.735265,448.61988 L610.735265,448.542957 C610.735265,447.71453 611.406838,447.042957 612.235265,447.042957 L623.042957,447.042957 L623.042957,436.235265 C623.042957,435.406838 623.71453,434.735265 624.542957,434.735265 L624.61988,434.735265 C625.448307,434.735265 626.11988,435.406838 626.11988,436.235265 L626.11988,447.042957 L636.927572,447.042957 C637.756,447.042957 638.427572,447.71453 638.427572,448.542957 L638.427572,448.61988 C638.427572,449.448307 637.756,450.11988 636.927572,450.11988 L626.11988,450.11988 Z"
          transform="translate(624.581419, 448.581419) rotate(-225.000000) translate(-624.581419, -448.581419) "
        />
      </g>
    </g>
  </svg>
)

const MultiDevicesIcon = () => (
  <svg
    width="268px"
    height="105px"
    viewBox="0 0 268 105"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M9.99963381,35 L5,35 L5,99 L37,99 L37,35 L32.0003662,35 C31.0881599,36.2144467 29.6358178,37 28,37 L14,37 C12.3641822,37 10.9118401,36.2144467 9.99963381,35 Z M5,30 L37,30 C39.7614237,30 42,32.2385763 42,35 L42,99 C42,101.761424 39.7614237,104 37,104 L5,104 C2.23857625,104 3.38176876e-16,101.761424 0,99 L0,35 C-3.38176876e-16,32.2385763 2.23857625,30 5,30 Z" />
    <path d="M101.964556,92 C101.987914,92.1633015 102,92.3302393 102,92.5 L102,97.5 C102,97.6697607 101.987914,97.8366985 101.964556,98 L111,98 C114.313708,98 117,100.686292 117,104 L75,104 C75,100.686292 77.6862915,98 81,98 L90.0354444,98 C90.012086,97.8366985 90,97.6697607 90,97.5 L90,92.5 C90,92.3302393 90.012086,92.1633015 90.0354444,92 L41.9436088,92 L41.3683192,87 L145,87 L145,92 L101.964556,92 Z M165,36 L160,36 L160,5 L96.9275362,5 L32,5 L32,34 L27,34 L27,0 L97,0 L165,0 L165,36 Z" />
    <path d="M257,98 L268,98 C266.180025,102.246608 262.004398,105 257.384227,105 L144.615773,105 C139.995602,105 135.819975,102.246608 134,98 L145,98 L145,36.5 C145,34.5670034 146.567003,33 148.5,33 L253.5,33 C255.432997,33 257,34.5670034 257,36.5 L257,98 Z M252,98 L252,38 L150,38 L150,98 L252,98 Z" />
  </svg>
)

const TriangleIcon = () => (
  <svg
    width="14px"
    height="14px"
    viewBox="0 0 14 14"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <g transform="translate(-403.000000, -237.000000)" fill="#DDDDDD">
        <path
          d="M411.232793,241.789162 L415.439581,246.319549 C415.815384,246.72426 415.791949,247.356991 415.387239,247.732793 C415.202273,247.904547 414.959199,248 414.706788,248 L406.293212,248 C405.740928,248 405.293212,247.552285 405.293212,247 C405.293212,246.747589 405.388665,246.504514 405.560419,246.319549 L409.767207,241.789162 C410.143009,241.384452 410.77574,241.361017 411.180451,241.73682 C411.198539,241.753615 411.215998,241.771075 411.232793,241.789162 Z"
          transform="translate(410.500000, 244.500000) rotate(-180.000000) translate(-410.500000, -244.500000) "
        />
      </g>
    </g>
  </svg>
)

const AppleIcon = () => (
  <svg
    width="120"
    height="42"
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 120 42"
  >
    <g fill="none" fillRule="evenodd">
      <path
        d="M117.05.5c1.357 0 2.458 1.12 2.458 2.5v36c0 1.38-1.1 2.5-2.459 2.5H2.951C1.593 41.5.49 40.38.49 39V3c0-1.38 1.102-2.5 2.46-2.5h114.098zm0 .5H2.95C1.865 1 .985 1.895.985 3v36c0 1.105.88 2 1.967 2h114.098c1.087 0 1.967-.895 1.967-2V3c0-1.105-.88-2-1.967-2z"
        fill="#979797"
        fillRule="nonzero"
      />
      <rect fill="#181A1F" x=".984" y="1" width="118.033" height="40" rx="2" />
      <g fill="#FFF">
        <path d="M25.57 20.89a5.164 5.164 0 012.41-4.318 5.16 5.16 0 00-4.081-2.244c-1.718-.183-3.383 1.045-4.258 1.045-.893 0-2.24-1.027-3.691-.997a5.426 5.426 0 00-4.576 2.837c-1.978 3.482-.502 8.6 1.393 11.414.948 1.378 2.056 2.918 3.506 2.863 1.418-.06 1.948-.92 3.66-.92 1.698 0 2.195.92 3.674.886 1.522-.026 2.482-1.385 3.396-2.776a11.478 11.478 0 001.553-3.216c-1.808-.777-2.983-2.579-2.985-4.575zM22.777 12.477a5.123 5.123 0 001.14-3.63 5.044 5.044 0 00-3.281 1.726 4.872 4.872 0 00-1.17 3.495 4.166 4.166 0 003.31-1.591z" />
        <path
          d="M42.592 27.14h-4.656l-1.118 3.356h-1.972l4.41-12.418h2.049l4.41 12.418h-2.006l-1.117-3.356zm-4.174-1.55h3.69l-1.818-5.446h-.051l-1.82 5.447zM55.239 25.97c0 2.813-1.482 4.62-3.717 4.62a3.009 3.009 0 01-2.802-1.583h-.042v4.484H46.85V21.442h1.77v1.506h.033a3.148 3.148 0 012.836-1.6c2.26 0 3.75 1.816 3.75 4.622zm-1.88 0c0-1.833-.931-3.038-2.353-3.038-1.396 0-2.336 1.23-2.336 3.038 0 1.824.94 3.046 2.336 3.046 1.422 0 2.354-1.197 2.354-3.046zM65.04 25.97c0 2.813-1.481 4.62-3.716 4.62a3.009 3.009 0 01-2.802-1.583h-.043v4.484h-1.828V21.442h1.77v1.506h.033a3.148 3.148 0 012.836-1.6c2.26 0 3.75 1.816 3.75 4.622zm-1.879 0c0-1.833-.932-3.038-2.353-3.038-1.397 0-2.336 1.23-2.336 3.038 0 1.824.94 3.046 2.336 3.046 1.421 0 2.353-1.197 2.353-3.046z"
          fillRule="nonzero"
        />
        <path d="M71.518 27.036c.135 1.232 1.312 2.04 2.92 2.04 1.54 0 2.649-.808 2.649-1.919 0-.964-.669-1.54-2.252-1.936l-1.583-.388c-2.243-.55-3.284-1.617-3.284-3.348 0-2.142 1.837-3.614 4.444-3.614 2.582 0 4.351 1.472 4.41 3.614h-1.845c-.11-1.239-1.118-1.987-2.59-1.987-1.473 0-2.48.757-2.48 1.858 0 .878.643 1.395 2.217 1.79l1.346.336c2.506.603 3.547 1.626 3.547 3.443 0 2.323-1.82 3.778-4.715 3.778-2.709 0-4.538-1.42-4.656-3.667h1.872zM82.963 19.3v2.142h1.694v1.472h-1.694v4.991c0 .776.34 1.137 1.084 1.137a5.62 5.62 0 00.601-.043v1.463a4.94 4.94 0 01-1.015.086c-1.803 0-2.506-.689-2.506-2.445v-5.189h-1.295v-1.472h1.295V19.3h1.836z" />
        <path
          d="M85.637 25.97c0-2.849 1.65-4.639 4.223-4.639 2.582 0 4.225 1.79 4.225 4.639 0 2.856-1.634 4.638-4.225 4.638-2.59 0-4.223-1.782-4.223-4.638zm6.585 0c0-1.954-.88-3.108-2.362-3.108-1.48 0-2.362 1.162-2.362 3.108 0 1.962.881 3.106 2.362 3.106s2.362-1.144 2.362-3.106z"
          fillRule="nonzero"
        />
        <path d="M95.592 21.442h1.743v1.541h.043a2.13 2.13 0 012.142-1.635c.21-.001.42.022.626.069v1.738a2.516 2.516 0 00-.821-.112 1.827 1.827 0 00-1.424.596c-.366.4-.542.944-.481 1.487v5.37h-1.828v-9.054z" />
        <path
          d="M108.574 27.837c-.246 1.643-1.82 2.771-3.835 2.771-2.59 0-4.198-1.764-4.198-4.595 0-2.84 1.616-4.682 4.121-4.682 2.464 0 4.014 1.72 4.014 4.466v.637h-6.29v.112c-.059.674.17 1.341.628 1.832a2.3 2.3 0 001.768.732 2.012 2.012 0 002.056-1.273h1.736zm-6.18-2.702h4.453a2.198 2.198 0 00-.599-1.634 2.124 2.124 0 00-1.586-.664 2.236 2.236 0 00-1.604.668 2.312 2.312 0 00-.663 1.63z"
          fillRule="nonzero"
        />
        <g>
          <path d="M40.278 11.278c0-.718-.187-1.269-.562-1.653-.374-.383-.92-.575-1.64-.575-.304 0-.565.021-.78.064v4.602c.119.018.338.028.655.028.742 0 1.316-.21 1.72-.63.404-.42.607-1.032.607-1.836m1.023-.028c0 1.109-.326 1.942-.979 2.503-.605.517-1.464.776-2.577.776a11.84 11.84 0 01-1.42-.073V8.4a10.317 10.317 0 011.672-.127c1.06 0 1.858.235 2.397.703.604.53.907 1.288.907 2.273M45.72 12.25c0-.408-.088-.76-.261-1.053-.204-.354-.494-.53-.87-.53-.39 0-.687.176-.89.53-.174.294-.26.65-.26 1.071 0 .41.086.76.26 1.054.21.354.502.531.88.531.371 0 .66-.18.871-.54.18-.3.27-.653.27-1.062m1.005-.033c0 .683-.191 1.242-.575 1.68-.4.451-.933.677-1.598.677-.641 0-1.15-.217-1.532-.649-.38-.432-.57-.977-.57-1.635 0-.687.196-1.25.588-1.689.393-.438.92-.657 1.585-.657.641 0 1.156.216 1.545.648.371.42.557.962.557 1.625M54 10.036l-1.366 4.438h-.888l-.566-1.927c-.144-.48-.26-.959-.351-1.433h-.017c-.085.487-.201.965-.351 1.433l-.602 1.927h-.898l-1.284-4.438h.997l.494 2.11c.119.498.218.973.296 1.424h.018c.072-.371.191-.843.36-1.415l.619-2.119h.79l.593 2.073c.144.505.26.992.35 1.461h.027c.066-.457.165-.943.297-1.46l.53-2.074H54zM59.03 14.474h-.97v-2.541c0-.783-.293-1.175-.88-1.175a.869.869 0 00-.701.323c-.179.215-.27.47-.27.76v2.633h-.97v-3.168c0-.39-.011-.813-.035-1.27h.853l.045.694h.027c.113-.215.282-.394.503-.535.263-.167.557-.25.88-.25.407 0 .746.134 1.015.402.335.329.503.819.503 1.47v2.657zM60.737 14.474h.969V8h-.97zM66.411 12.25c0-.408-.087-.76-.26-1.053-.203-.354-.494-.53-.87-.53-.39 0-.687.176-.89.53-.173.294-.26.65-.26 1.071 0 .41.087.76.26 1.054.21.354.503.531.881.531.37 0 .66-.18.87-.54.18-.3.27-.653.27-1.062m1.007-.033c0 .683-.192 1.242-.575 1.68-.402.451-.935.677-1.6.677-.641 0-1.15-.217-1.53-.649-.38-.432-.57-.977-.57-1.635 0-.687.195-1.25.587-1.689.392-.438.92-.657 1.584-.657.643 0 1.156.216 1.547.648.37.42.557.962.557 1.625M71.108 12.949v-.685c-1.07-.018-1.606.28-1.606.895 0 .231.061.405.186.52a.671.671 0 00.474.174.964.964 0 00.593-.206.84.84 0 00.353-.698m1.007 1.525h-.872l-.072-.511h-.027c-.298.408-.723.612-1.275.612-.412 0-.745-.134-.997-.403a1.273 1.273 0 01-.341-.903c0-.542.222-.956.669-1.242.447-.286 1.074-.426 1.883-.42v-.082c0-.585-.303-.877-.906-.877a2 2 0 00-1.138.329l-.197-.649c.405-.255.906-.383 1.497-.383 1.141 0 1.713.612 1.713 1.836v1.634c0 .444.021.797.063 1.059M76.623 12.602v-.74a1.41 1.41 0 00-.027-.31 1.125 1.125 0 00-.35-.598.946.946 0 00-.65-.242.99.99 0 00-.854.438c-.206.292-.31.667-.31 1.123 0 .438.1.795.298 1.07.21.29.493.437.848.437a.93.93 0 00.767-.365c.186-.225.278-.497.278-.813m1.006 1.872h-.86l-.047-.712h-.026c-.275.541-.744.813-1.402.813-.526 0-.964-.21-1.311-.63-.347-.42-.52-.965-.52-1.635 0-.719.187-1.3.565-1.744.366-.414.814-.62 1.348-.62.585 0 .996.2 1.23.602h.017V8h.972v5.278c0 .432.01.83.034 1.196M84.922 12.25c0-.408-.087-.76-.26-1.053-.204-.354-.494-.53-.872-.53-.389 0-.686.176-.89.53-.173.294-.26.65-.26 1.071 0 .41.087.76.26 1.054.21.354.503.531.881.531.37 0 .662-.18.872-.54.179-.3.27-.653.27-1.062m1.005-.033c0 .683-.192 1.242-.575 1.68-.402.451-.934.677-1.599.677-.64 0-1.15-.217-1.532-.649-.38-.432-.57-.977-.57-1.635 0-.687.196-1.25.588-1.689.393-.438.921-.657 1.586-.657.64 0 1.156.216 1.545.648.37.42.557.962.557 1.625M91.145 14.474h-.97v-2.541c0-.783-.292-1.175-.88-1.175a.866.866 0 00-.7.323c-.179.215-.27.47-.27.76v2.633h-.97v-3.168c0-.39-.012-.813-.035-1.27h.852l.045.694h.027a1.42 1.42 0 01.503-.535c.264-.167.557-.25.88-.25.407 0 .745.134 1.015.402.336.329.503.819.503 1.47v2.657zM97.675 10.776h-1.07v2.155c0 .548.191.822.567.822.174 0 .318-.015.432-.046l.026.748c-.192.074-.443.11-.754.11-.384 0-.682-.118-.898-.356-.216-.237-.323-.636-.323-1.196v-2.237h-.639v-.74h.639v-.812l.95-.293v1.105h1.07v.74zM102.813 14.474h-.972V11.95c0-.796-.293-1.193-.878-1.193-.45 0-.758.23-.927.692a1.26 1.26 0 00-.045.355v2.669h-.969V8h.97v2.675h.017c.306-.487.744-.73 1.312-.73.401 0 .734.134.998.402.33.334.494.83.494 1.488v2.639zM107.185 11.79a1.346 1.346 0 00-.188-.758c-.168-.274-.425-.411-.772-.411a.917.917 0 00-.772.402c-.161.213-.257.468-.288.767h2.02zm.926.255c0 .177-.013.326-.036.447h-2.91c.013.439.152.774.421 1.004.247.208.565.311.954.311.43 0 .822-.07 1.177-.21l.152.685c-.415.183-.9.274-1.465.274-.676 0-1.208-.202-1.593-.607-.387-.405-.579-.948-.579-1.63 0-.67.18-1.227.539-1.67.376-.476.884-.713 1.526-.713.628 0 1.105.237 1.427.712.259.378.387.843.387 1.397z" />
        </g>
      </g>
    </g>
  </svg>
)

const GoogleIcon = () => (
  <svg
    width="120"
    height="42"
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 120 42"
  >
    <defs>
      <linearGradient x1="60.866%" y1="4.839%" x2="27.094%" y2="71.968%" id="a">
        <stop stopColor="#00A0FF" offset="0%" />
        <stop stopColor="#00A1FF" offset=".657%" />
        <stop stopColor="#00BEFF" offset="26.01%" />
        <stop stopColor="#00D2FF" offset="51.22%" />
        <stop stopColor="#00DFFF" offset="76.04%" />
        <stop stopColor="#00E3FF" offset="100%" />
      </linearGradient>
      <linearGradient
        x1="107.728%"
        y1="49.428%"
        x2="-130.665%"
        y2="49.428%"
        id="b"
      >
        <stop stopColor="#FFE000" offset="0%" />
        <stop stopColor="#FFBD00" offset="40.87%" />
        <stop stopColor="orange" offset="77.54%" />
        <stop stopColor="#FF9C00" offset="100%" />
      </linearGradient>
      <linearGradient
        x1="86.389%"
        y1="30.185%"
        x2="-49.888%"
        y2="138.895%"
        id="c"
      >
        <stop stopColor="#FF3A44" offset="0%" />
        <stop stopColor="#C31162" offset="100%" />
      </linearGradient>
      <linearGradient
        x1="-18.579%"
        y1="-13.756%"
        x2="42.275%"
        y2="34.562%"
        id="d"
      >
        <stop stopColor="#32A071" offset="0%" />
        <stop stopColor="#2DA771" offset="6.85%" />
        <stop stopColor="#15CF74" offset="47.62%" />
        <stop stopColor="#06E775" offset="80.09%" />
        <stop stopColor="#00F076" offset="100%" />
      </linearGradient>
    </defs>
    <g fill="none" fillRule="evenodd">
      <path
        d="M117.05.5c1.357 0 2.458 1.12 2.458 2.5v36c0 1.38-1.1 2.5-2.459 2.5H2.951C1.593 41.5.49 40.38.49 39V3c0-1.38 1.102-2.5 2.46-2.5h114.098zm0 .5H2.95C1.865 1 .985 1.895.985 3v36c0 1.105.88 2 1.967 2h114.098c1.087 0 1.967-.895 1.967-2V3c0-1.105-.88-2-1.967-2z"
        fill="#979797"
        fillRule="nonzero"
      />
      <rect fill="#181A1F" x=".984" y="1" width="118.033" height="40" rx="2" />
      <g fill="#EEE">
        <path d="M42.843 12.91c0 .706-.174 1.324-.608 1.765-.52.529-1.128.793-1.908.793-.781 0-1.388-.264-1.909-.793-.52-.53-.78-1.147-.78-1.94 0-.795.26-1.412.78-1.941.52-.53 1.128-.794 1.909-.794.347 0 .694.088 1.04.265.348.176.608.352.782.617l-.434.441c-.347-.441-.781-.617-1.388-.617-.52 0-1.041.176-1.388.617-.434.353-.608.882-.608 1.5 0 .617.174 1.146.608 1.499.433.353.867.617 1.388.617.607 0 1.04-.176 1.475-.617.26-.265.433-.618.433-1.059h-1.908v-.617h2.516v.265zM46.833 10.706h-2.342v1.675h2.169V13h-2.17v1.676h2.343v.705h-3.036v-5.292h3.036zM49.696 15.38h-.694v-4.674h-1.475v-.618h3.644v.618h-1.475zM53.687 15.38v-5.292h.694v5.292zM57.33 15.38h-.694v-4.674h-1.474v-.618h3.557v.618h-1.475v4.674z" />
        <path
          d="M65.572 14.675c-.52.529-1.128.793-1.908.793-.781 0-1.389-.264-1.909-.793-.52-.53-.78-1.147-.78-1.94 0-.795.26-1.412.78-1.941.52-.53 1.128-.794 1.909-.794.78 0 1.388.265 1.908.794s.78 1.146.78 1.94-.26 1.411-.78 1.94zm-3.297-.441c.347.352.868.617 1.389.617.52 0 1.04-.176 1.388-.617.347-.353.607-.882.607-1.5 0-.617-.174-1.146-.607-1.5-.347-.352-.868-.617-1.388-.617-.521 0-1.042.177-1.389.618a2.146 2.146 0 00-.607 1.5c0 .617.174 1.146.607 1.499z"
          fillRule="nonzero"
        />
        <path d="M67.307 15.38v-5.292h.781l2.516 4.146v-4.146h.694v5.292h-.694l-2.69-4.322v4.322z" />
      </g>
      <path
        d="M60.8 23.142c-2.081 0-3.73 1.587-3.73 3.792 0 2.117 1.649 3.793 3.73 3.793 2.083 0 3.731-1.588 3.731-3.793 0-2.293-1.648-3.792-3.73-3.792zm0 5.997c-1.127 0-2.081-.97-2.081-2.293 0-1.323.954-2.293 2.082-2.293 1.127 0 2.082.882 2.082 2.293 0 1.323-.955 2.293-2.082 2.293zm-8.067-5.997c-2.083 0-3.73 1.587-3.73 3.792 0 2.117 1.647 3.793 3.73 3.793 2.082 0 3.73-1.588 3.73-3.793 0-2.293-1.648-3.792-3.73-3.792zm0 5.997c-1.128 0-2.083-.97-2.083-2.293 0-1.323.955-2.293 2.083-2.293 1.127 0 2.082.882 2.082 2.293 0 1.323-.955 2.293-2.082 2.293zm-9.63-4.85v1.587h3.73c-.087.882-.434 1.588-.867 2.029-.52.529-1.388 1.146-2.863 1.146-2.343 0-4.078-1.852-4.078-4.233 0-2.382 1.822-4.234 4.078-4.234 1.214 0 2.169.53 2.863 1.147l1.128-1.147c-.955-.882-2.17-1.588-3.904-1.588-3.124 0-5.813 2.646-5.813 5.822 0 3.175 2.69 5.82 5.813 5.82 1.735 0 2.95-.528 3.99-1.675 1.041-1.058 1.388-2.558 1.388-3.704 0-.353 0-.706-.086-.97h-5.38zm39.386 1.234c-.347-.882-1.214-2.381-3.123-2.381-1.909 0-3.47 1.5-3.47 3.792 0 2.117 1.561 3.793 3.644 3.793 1.648 0 2.689-1.058 3.036-1.676l-1.215-.882c-.433.618-.954 1.059-1.821 1.059-.868 0-1.389-.353-1.822-1.147l4.945-2.117-.174-.44zm-5.032 1.235c0-1.411 1.128-2.205 1.909-2.205.607 0 1.215.353 1.388.794l-3.297 1.411zm-4.077 3.616h1.648V19.35H73.38v11.025zm-2.603-6.438c-.433-.441-1.127-.882-1.995-.882-1.822 0-3.557 1.675-3.557 3.792s1.648 3.705 3.557 3.705c.868 0 1.562-.441 1.909-.882h.086v.529c0 1.411-.78 2.205-1.995 2.205-.954 0-1.648-.706-1.822-1.323l-1.388.617c.434.97 1.475 2.205 3.297 2.205 1.908 0 3.47-1.146 3.47-3.88v-6.704h-1.562v.618zm-1.908 5.203c-1.128 0-2.082-.97-2.082-2.293 0-1.323.954-2.293 2.082-2.293s1.995.97 1.995 2.293c0 1.323-.867 2.293-1.995 2.293zm21.168-9.79h-3.904v11.025h1.648V26.23h2.256c1.822 0 3.557-1.323 3.557-3.44s-1.735-3.44-3.557-3.44zm.087 5.292H87.78V20.85h2.343c1.214 0 1.908 1.058 1.908 1.852-.087.97-.78 1.94-1.908 1.94zm9.976-1.587c-1.214 0-2.429.529-2.863 1.675l1.475.618c.347-.618.868-.794 1.475-.794.868 0 1.648.53 1.735 1.411v.088c-.26-.176-.954-.44-1.648-.44-1.562 0-3.123.881-3.123 2.469 0 1.5 1.3 2.47 2.689 2.47 1.128 0 1.648-.53 2.082-1.059h.087v.882h1.561v-4.233c-.173-1.94-1.648-3.087-3.47-3.087zm-.173 6.085c-.52 0-1.302-.264-1.302-.97 0-.882.955-1.146 1.736-1.146.694 0 1.04.176 1.474.352-.173 1.059-1.04 1.764-1.908 1.764zm9.109-5.82l-1.822 4.762h-.087l-1.908-4.763h-1.735l2.863 6.703-1.649 3.705h1.649l4.424-10.408h-1.735zM94.46 30.373h1.649V19.35H94.46v11.025z"
        fill="#FFF"
        fillRule="nonzero"
      />
      <g>
        <path
          d="M.59.307C.318.584.136 1.045.136 1.599V22c0 .554.182 1.016.454 1.292l.091.093 11.258-11.446v-.185L.59.307z"
          fill="url(#a)"
          transform="translate(10.82 9)"
        />
        <path
          d="M15.662 15.815l-3.723-3.785v-.277l3.723-3.785.09.093 4.45 2.584c1.27.739 1.27 1.939 0 2.677l-4.54 2.493z"
          fill="url(#b)"
          transform="translate(10.82 9)"
        />
        <path
          d="M15.753 15.722l-3.814-3.877L.59 23.384c.454.461 1.09.461 1.907.092l13.256-7.754"
          fill="url(#c)"
          transform="translate(10.82 9)"
        />
        <path
          d="M15.753 7.968L2.497.307C1.68-.155 1.044-.063.59.399l11.35 11.446 3.813-3.877z"
          fill="url(#d)"
          transform="translate(10.82 9)"
        />
        <path
          d="M26.482 24.63L13.317 32.2c-.727.46-1.362.368-1.816 0l-.091.091.09.093c.455.369 1.09.461 1.817 0l13.165-7.754z"
          fill="#000"
          opacity=".2"
        />
        <path
          d="M11.41 32.2c-.273-.278-.363-.74-.363-1.293v.092c0 .554.181 1.016.454 1.292V32.2h-.091zM31.021 22.045l-4.54 2.585.092.092 4.448-2.584c.636-.37.908-.831.908-1.293 0 .462-.363.831-.908 1.2z"
          fill="#000"
          opacity=".12"
        />
        <path
          d="M13.317 9.4L31.02 19.644c.545.37.908.739.908 1.2 0-.461-.272-.923-.908-1.292L13.317 9.307c-1.272-.739-2.27-.092-2.27 1.384v.093c0-1.477.998-2.123 2.27-1.385z"
          fill="#FFF"
          opacity=".25"
        />
      </g>
    </g>
  </svg>
)

const ArrowTopIcon = () => (
  <svg
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    x="0px"
    y="0px"
    viewBox="0 0 24 24"
  >
    <g>
      <g>
        <path
          d="M11.3,6.5L11.3,6.5c0.4-0.4,1-0.4,1.4,0l8.5,8.5c0.4,0.4,0.4,1,0,1.4l0,0c-0.4,0.4-1,0.4-1.4,0l-8.5-8.5
			C10.9,7.5,10.9,6.9,11.3,6.5z"
        />
      </g>
      <g>
        <path
          d="M2.8,16.4L2.8,16.4c-0.4-0.4-0.4-1,0-1.4l8.5-8.5c0.4-0.4,1-0.4,1.4,0l0,0c0.4,0.4,0.4,1,0,1.4l-8.5,8.5
			C3.8,16.8,3.2,16.8,2.8,16.4z"
        />
      </g>
    </g>
  </svg>
)

const DropDownArrow = () => (
  <svg
    width="14px"
    height="14px"
    viewBox="0 0 30 30"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <rect
        id="Rectangle"
        fill="#FFFFFF"
        transform="translate(9.343146, 15.656854) rotate(-45.000000) translate(-9.343146, -15.656854) "
        x="7.34314575"
        y="5.65685425"
        width="4"
        height="20"
        rx="2"
      />
      <rect
        id="Rectangle-Copy-7"
        fill="#FFFFFF"
        transform="translate(20.656854, 15.656854) rotate(-315.000000) translate(-20.656854, -15.656854) "
        x="18.6568542"
        y="5.65685425"
        width="4"
        height="20"
        rx="2"
      />
    </g>
  </svg>
)

const UpArrow = () => (
  <svg
    width="14px"
    height="14px"
    viewBox="0 0 30 30"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <rect
        id="Rectangle-Copy-7"
        fill="#FFFFFF"
        transform="translate(9.343146, 15.656854) rotate(-315.000000) translate(-9.343146, -15.656854) "
        x="7.34314575"
        y="5.65685425"
        width="4"
        height="20"
        rx="2"
      />
      <rect
        id="Rectangle"
        fill="#FFFFFF"
        transform="translate(20.656854, 15.656854) rotate(-45.000000) translate(-20.656854, -15.656854) "
        x="18.6568542"
        y="5.65685425"
        width="4"
        height="20"
        rx="2"
      />
    </g>
  </svg>
)

const HeadDownArrow = () => (
  <svg
    width="14px"
    height="8px"
    viewBox="0 0 14 8"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
  >
    <defs>
      <path
        d="M1.70678699,0.29257357 C1.31608616,-0.0977741045 0.682921244,-0.0974878156 0.29257357,0.293213014 C-0.0977741045,0.683913843 -0.0974878156,1.31707876 0.293213014,1.70742643 L6.29864133,7.70742643 C6.68932821,8.09776017 7.32246559,8.09748988 7.71281906,7.70682271 L13.7080011,1.70682271 C14.0983685,1.31614159 14.0981142,0.682976664 13.7074331,0.292609261 C13.3167519,-0.0977581418 12.683587,-0.0975038257 12.2932196,0.293177292 L7.0048246,5.58582227 L1.70678699,0.29257357 Z"
        id="path-1"
      />
    </defs>
    <g
      id="Stylesheet"
      stroke="none"
      strokeWidth="1"
      fill="none"
      fillRule="evenodd"
    >
      <g
        id="iQIYI/UI-Core-Elements/Icongraphy"
        transform="translate(-522.000000, -233.000000)"
      >
        <g
          id="icon/outlined/chevron-down"
          transform="translate(517.000000, 225.000000)"
        >
          <g id="colour" transform="translate(5.000000, 8.000000)">
            <mask id="mask-2" fill="white" />
            <use
              id="Mask"
              fill="#222222"
              fillRule="nonzero"
              xlinkHref="#path-1"
            />
            <g id="Colours/Primary-Black" mask="url(#mask-2)" fill="#2C3038">
              <g transform="translate(-5.000000, -8.000000)" id="Color">
                <rect x="0" y="0" width="24" height="24" />
              </g>
            </g>
          </g>
        </g>
      </g>
    </g>
  </svg>
)

const HeaderCloseIcon = () => (
  <svg
    width="14px"
    height="14px"
    viewBox="0 0 21 21"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g stroke="none" strokeWidth="1" fillRule="evenodd">
      <g transform="translate(-614.000000, -438.000000)">
        <path
          d="M626.11988,450.11988 L626.11988,460.927572 C626.11988,461.756 625.448307,462.427572 624.61988,462.427572 L624.542957,462.427572 C623.71453,462.427572 623.042957,461.756 623.042957,460.927572 L623.042957,450.11988 L612.235265,450.11988 C611.406838,450.11988 610.735265,449.448307 610.735265,448.61988 L610.735265,448.542957 C610.735265,447.71453 611.406838,447.042957 612.235265,447.042957 L623.042957,447.042957 L623.042957,436.235265 C623.042957,435.406838 623.71453,434.735265 624.542957,434.735265 L624.61988,434.735265 C625.448307,434.735265 626.11988,435.406838 626.11988,436.235265 L626.11988,447.042957 L636.927572,447.042957 C637.756,447.042957 638.427572,447.71453 638.427572,448.542957 L638.427572,448.61988 C638.427572,449.448307 637.756,450.11988 636.927572,450.11988 L626.11988,450.11988 Z"
          transform="translate(624.581419, 448.581419) rotate(-225.000000) translate(-624.581419, -448.581419) "
        />
      </g>
    </g>
  </svg>
)

const UnLoginUser = () => (
  <svg
    width="42px"
    height="42px"
    viewBox="0 0 42 42"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g id="Page-1" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <g
        id="1920/Home/Watch-History-Sticky-Header"
        transform="translate(-1648.000000, -14.000000)"
      >
        <g id="Sticky-Header">
          <g id="Memeber-login" transform="translate(1648.000000, 14.000000)">
            <circle id="Oval" fill="#666666" cx="21" cy="21" r="21" />
            <g id="Memeber" transform="translate(11.000000, 9.000000)">
              <circle
                id="Oval"
                stroke="#FFFFFF"
                strokeWidth="2"
                cx="10"
                cy="8"
                r="7"
              />
              <path
                d="M10,14 C15.1790437,14 19.4441326,17.937396 19.9999469,22.9995166 L17.7462166,22.999036 C17.2038362,19.1831038 13.9423691,16.2497194 10,16.2497194 C6.0576309,16.2497194 2.79616379,19.1831038 2.25378344,22.999036 L5.30650914e-05,22.9995166 C0.*********,17.937396 4.82095629,14 10,14 Z"
                id="Combined-Shape"
                fill="#FFFFFF"
              />
            </g>
          </g>
        </g>
      </g>
    </g>
  </svg>
)

const HoverUnLoginUser = () => (
  <svg
    width="42px"
    height="42px"
    viewBox="0 0 42 42"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g id="Page-1" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <g
        id="1920/Home/Watch-History-Sticky-Header"
        transform="translate(-1648.000000, -14.000000)"
      >
        <g id="Sticky-Header">
          <g id="Memeber-login" transform="translate(1648.000000, 14.000000)">
            <circle id="Oval" fill="#666666" cx="21" cy="21" r="21" />
            <g id="Memeber" transform="translate(11.000000, 9.000000)">
              <circle
                id="Oval"
                stroke="#0BBE06"
                strokeWidth="2"
                cx="10"
                cy="8"
                r="7"
              />
              <path
                d="M10,14 C15.1790437,14 19.4441326,17.937396 19.9999469,22.9995166 L17.7462166,22.999036 C17.2038362,19.1831038 13.9423691,16.2497194 10,16.2497194 C6.0576309,16.2497194 2.79616379,19.1831038 2.25378344,22.999036 L5.30650914e-05,22.9995166 C0.*********,17.937396 4.82095629,14 10,14 Z"
                id="Combined-Shape"
                fill="#0BBE06"
              />
            </g>
          </g>
        </g>
      </g>
    </g>
  </svg>
)

const LogoutBtn = () => (
  <svg
    width="19px"
    height="18px"
    viewBox="0 0 19 18"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g id="Page-1" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <g
        id="1920/Home/Passport-Entry/Member-Logout-entry"
        transform="translate(-1473.000000, -164.000000)"
      >
        <g id="浮层" transform="translate(1454.000000, 70.000000)">
          <g id="icon/logout" transform="translate(20.000000, 95.000000)">
            <path
              d="M11,16 L0.625,16 C0.*********,16 0,15.6418278 0,15.2 L0,0.8 C0,0.3581722 0.*********,0 0.625,0 L11,0"
              id="Path"
              stroke="#CCCCCC"
              strokeWidth="2"
              strokeLinecap="round"
            />
            <rect
              id="Rectangle"
              fill="#CCCCCC"
              x="7"
              y="7"
              width="10"
              height="2"
            />
            <polyline
              id="Path"
              stroke="#CCCCCC"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              transform="translate(14.000000, 8.000000) rotate(-315.000000) translate(-14.000000, -8.000000) "
              points="12 6 16 6 16 10"
            />
          </g>
        </g>
      </g>
    </g>
  </svg>
)
const DateLeftArrow = () => (
  <svg
    width="8"
    height="14"
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
  >
    <defs>
      <path
        d="M7.707 1.707A1 1 0 006.293.293l-6 6a1 1 0 000 1.414l6 6a1 1 0 101.414-1.414L2.414 7l5.293-5.293z"
        id="a"
      />
    </defs>
    <g fill="none" fillRule="evenodd">
      <mask id="b" fill="#fff">
        <use xlinkHref="#a" />
      </mask>
      <use fill="#222" fillRule="nonzero" xlinkHref="#a" />
      <g mask="url(#b)" fill="#FFF">
        <path d="M-8-5h24v24H-8z" />
      </g>
    </g>
  </svg>
)
const DateRightArrow = () => (
  <svg
    className="date-right-arrow"
    width="8"
    height="14"
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
  >
    <defs>
      <path
        d="M.293 12.289c-.391.392-.39 1.027 0 1.418a1 1 0 001.417-.001l5.997-6.02a1.004 1.004 0 00-.002-1.42L1.707.292A1 1 0 00.291.296a1.004 1.004 0 00.004 1.418L5.582 6.98l-5.29 5.309z"
        id="a"
      />
    </defs>
    <g fill="none" fillRule="evenodd">
      <mask id="b" fill="#fff">
        <use xlinkHref="#a" />
      </mask>
      <use fill="#2C3038" fillRule="nonzero" xlinkHref="#a" />
      <g mask="url(#b)" fill="#FFF">
        <path d="M-8-5h24v24H-8z" />
      </g>
    </g>
  </svg>
)
const ColComIcon = () => (
  <svg
    width="72"
    height="72"
    viewBox="0 0 72 72"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g fill="#FFF" fillRule="evenodd">
      <circle
        strokeOpacity=".247"
        stroke="#FFF"
        strokeWidth="2"
        fillOpacity=".047"
        cx="36"
        cy="36"
        r="35"
      />
      <g transform="translate(16 16)">
        <rect x="23.333" y="11.667" width="12.5" height="2.5" rx=".833" />
        <rect x="28.333" y="6.667" width="2.5" height="12.5" rx=".833" />
        <path
          d="M20 5.833v.834c0 .46-.373.833-.833.833H10c-.41 0-.75.295-.82.684l-.013.15V33.66l8.613-5.74a2.5 2.5 0 012.558-.128l.215.128 8.614 5.742v-7.829c0-.46.373-.833.833-.833h.833c.46 0 .834.373.834.833v9.386a1.667 1.667 0 01-2.592 1.387L19.167 30l-9.91 6.606a1.667 1.667 0 01-2.59-1.387V8.333A3.333 3.333 0 0110 5h9.167c.46 0 .833.373.833.833z"
          fillRule="nonzero"
        />
      </g>
    </g>
  </svg>
)
const ColHoverIcon = () => (
  <svg
    width="72"
    height="72"
    viewBox="0 0 72 72"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g fill="#FFF" fillRule="evenodd">
      <circle
        strokeOpacity=".247"
        stroke="#FFF"
        strokeWidth="2"
        fillOpacity=".2"
        cx="36"
        cy="36"
        r="35"
      />
      <g transform="translate(16 16)">
        <rect x="23.333" y="11.667" width="12.5" height="2.5" rx=".833" />
        <rect x="28.333" y="6.667" width="2.5" height="12.5" rx=".833" />
        <path
          d="M20 5.833v.834c0 .46-.373.833-.833.833H10c-.41 0-.75.295-.82.684l-.013.15V33.66l8.613-5.74a2.5 2.5 0 012.558-.128l.215.128 8.614 5.742v-7.829c0-.46.373-.833.833-.833h.833c.46 0 .834.373.834.833v9.386a1.667 1.667 0 01-2.592 1.387L19.167 30l-9.91 6.606a1.667 1.667 0 01-2.59-1.387V8.333A3.333 3.333 0 0110 5h9.167c.46 0 .833.373.833.833z"
          fillRule="nonzero"
        />
      </g>
    </g>
  </svg>
)
const ColAddComIcon = () => (
  <svg
    width="72"
    height="72"
    viewBox="0 0 72 72"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g fill="none" fillRule="evenodd">
      <circle
        strokeOpacity=".247"
        stroke="#FFF"
        strokeWidth="2"
        fillOpacity=".05"
        fill="#FFF"
        cx="36"
        cy="36"
        r="35"
      />
      <path
        d="M49.433 24.031l.814.733a.84.84 0 01.062 1.187l-18.69 20.757a.84.84 0 01-1.186.062l-.503-.452a.847.847 0 01-.093-.08l-.04-.04-.178-.161a.843.843 0 01-.123-.137l-8.285-8.289a.84.84 0 010-1.188l.774-.775a.84.84 0 011.189 0l7.718 7.719 17.354-19.273a.84.84 0 011.187-.063z"
        fill="#E2E2E3"
        stroke="#E2E2E3"
        strokeWidth=".84"
      />
    </g>
  </svg>
)
const ColAddedHoverIcon = () => (
  <svg
    width="72"
    height="72"
    viewBox="0 0 72 72"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g fill="none" fillRule="evenodd">
      <circle
        strokeOpacity=".247"
        stroke="#FFF"
        strokeWidth="2"
        fillOpacity=".204"
        fill="#FFF"
        cx="36"
        cy="36"
        r="35"
      />
      <path
        d="M49.433 24.031l.814.733a.84.84 0 01.062 1.187l-18.69 20.757a.84.84 0 01-1.186.062l-.503-.452a.847.847 0 01-.093-.08l-.04-.04-.178-.161a.843.843 0 01-.123-.137l-8.285-8.289a.84.84 0 010-1.188l.774-.775a.84.84 0 011.189 0l7.718 7.719 17.354-19.273a.84.84 0 011.187-.063z"
        fill="#E2E2E3"
        stroke="#E2E2E3"
        strokeWidth=".84"
      />
    </g>
  </svg>
)
const CheckIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 26 26">
    <path
      d="M13 0c7.2 0 13 5.8 13 13s-5.8 13-13 13S0 20.2 0 13 5.8 0 13 0zm0 3C7.5 3 3 7.5 3 13s4.5 10 10 10 10-4.5 10-10S18.5 3 13 3z"
      fill="#fff"
      opacity=".5"
    />
  </svg>
)
const CheckedIcon = () => (
  <svg
    version="1.1"
    id="图层_1"
    xmlns="http://www.w3.org/2000/svg"
    x="0"
    y="0"
    viewBox="0 0 26 26"
    xmlSpace="preserve"
  >
    <g id="收藏功能-UI">
      <g id="watch-later4-选择" transform="translate(-1039 -273)">
        <g id="Group-7备份-2" transform="translate(1039 273)">
          <circle id="Oval" cx="13" cy="13" r="13" fill="#00cc36" />
          <g id="Group-8" transform="translate(6.33 8.45)">
            <path
              id="Rectangle"
              fill="#fff"
              d="M11.8.9l1 1c.******* 0 .7l-7 7.1c-.2.2-.5.2-.7 0l-1-1c-.3-.2-.3-.5-.1-.7L11.1.9c.2-.2.5-.2.7 0z"
            />
            <path
              id="Rectangle_1_"
              fill="#fff"
              d="M.7 4.7l1.1-1.1c.2-.2.5-.2.7 0L6 7.1c.******* 0 .7L5 8.9c-.2.2-.5.2-.7 0L.7 5.4c-.2-.2-.2-.5 0-.7z"
            />
          </g>
        </g>
      </g>
    </g>
  </svg>
)
const HotPlayOne = () => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 32">
    <path
      d="M0 0h30v30.5c0 .8-.7 1.5-1.5 1.5-.1 0-.3 0-.4-.1L15 28.1 1.9 31.9c-.8.2-1.6-.2-1.9-1V0z"
      fillRule="evenodd"
      clipRule="evenodd"
      fill="#f01111"
    />
    <path fill="#fff" d="M17.5 22.5v-16L11 7.8v2.1h3.3v12.6z" />
  </svg>
)
const HotPlayTwo = () => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 32">
    <path
      d="M0 0h30v30.5c0 .8-.7 1.5-1.5 1.5-.1 0-.3 0-.4-.1L15 28.1 1.9 31.9c-.8.2-1.6-.2-1.9-1V0z"
      fillRule="evenodd"
      clipRule="evenodd"
      fill="#ff8422"
    />
    <path
      d="M21 22.5V20h-6.8l2.9-3.2c1.3-1.3 2.1-2.4 2.7-3.2s.8-1.7.8-2.7c0-1.5-.5-2.6-1.4-3.4S17 6.2 15.3 6.2c-1.7 0-3 .5-4 1.5S9.8 10 9.8 11.5H13c0-.8.2-1.5.6-2s.9-.7 1.7-.7c.7 0 1.2.2 1.6.6s.5 1 .5 1.7c0 .5-.2 1.1-.5 1.6s-.9 1.3-1.6 2.2l-5.2 5.5v2.1H21z"
      fill="#fff"
    />
  </svg>
)
const HotPlayThr = () => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 32">
    <path
      d="M0 0h30v30.5c0 .8-.7 1.5-1.5 1.5-.1 0-.3 0-.4-.1L15 28.1 1.9 31.9c-.8.2-1.6-.2-1.9-1V0z"
      fillRule="evenodd"
      clipRule="evenodd"
      fill="#ffae00"
    />
    <path
      d="M15.1 22.7c1.7 0 3.1-.4 4.1-1.2s1.6-2 1.6-3.5c0-.9-.2-1.6-.7-2.3s-1.1-1.1-2-1.4c.8-.4 1.4-.8 1.8-1.5s.6-1.3.6-2c0-1.5-.5-2.6-1.5-3.4s-2.3-1.2-4-1.2c-1.5 0-2.8.4-3.8 1.2s-1.5 1.9-1.5 3.2h3.2c0-.6.2-1 .6-1.4s.9-.5 1.5-.5c.7 0 1.2.2 1.6.6s.5.9.5 1.6c0 .7-.2 1.3-.5 1.7s-.9.6-1.7.6h-1.8v2.4h1.8c.8 0 1.5.2 1.9.6s.6 1 .6 1.9c0 .7-.2 1.3-.6 1.7s-1 .6-1.8.6c-.7 0-1.2-.2-1.7-.6s-.6-.9-.6-1.5h-3c0 1.5.5 2.6 1.6 3.4s2.3 1 3.8 1z"
      fill="#fff"
    />
  </svg>
)
const hotPlayTotal = [HotPlayOne, HotPlayTwo, HotPlayThr]

const SearchShowMoreArrow = () => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 10 10">
    <path
      d="M9.2 3.2c.*******.1 1.3l-.1.2-3.5 3.5c-.4.4-1 .4-1.4.1l-.1-.1L.7 4.7c-.4-.4-.4-1 0-1.4.4-.4.9-.4 1.3-.1l.1.1 2.8 2.8 2.8-2.8c.4-.4 1-.4 1.5-.1z"
      fill="#999"
    />
  </svg>
)
const FBWithBg = () => (
  <svg
    width="32px"
    height="32px"
    viewBox="0 0 32 32"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
  >
    <title>Group 14</title>
    <g
      id="v1.7.0_2269_Unlock-advance"
      stroke="none"
      strokeWidth="1"
      fill="none"
      fillRule="evenodd"
    >
      <g
        id="2269-7_768_share"
        transform="translate(-258.000000, -430.000000)"
        fill="#4267B2"
      >
        <g id="Confirmation-Copy" transform="translate(184.000000, 300.000000)">
          <g
            id="VIP-Privilege-Model"
            transform="translate(40.000000, 106.000000)"
          >
            <g id="Group-14" transform="translate(34.000000, 24.000000)">
              <g id="ic_facebook">
                <path
                  d="M28,-2.73558953e-13 C30.209139,-2.73964766e-13 32,1.790861 32,4 L32,28 C32,30.209139 30.209139,32 28,32 L21.2936861,32 L21.2936861,19.8573153 L24.7725088,19.8573153 L25.1428571,15.4305681 L21.2936861,15.4305681 L21.2936861,12.9089912 C21.2936861,11.8652245 21.4921428,11.4519952 22.4444586,11.4519952 L25.142797,11.4519952 L25.142797,6.85714286 L21.6904793,6.85714286 C17.9801438,6.85714286 16.3073255,8.5871426 16.3073255,11.9003604 L16.3073255,15.4305681 L13.7142857,15.4305681 L13.7142857,19.9133291 L16.3073255,19.9133291 L16.3073255,32 L4,32 C1.790861,32 2.705415e-16,30.209139 0,28 L0,4 C-2.705415e-16,1.790861 1.790861,-2.73153141e-13 4,-2.73558953e-13 L28,-2.73558953e-13 Z"
                  id="Combined-Shape"
                />
              </g>
            </g>
          </g>
        </g>
      </g>
    </g>
  </svg>
)
const TwitterWithBg = () => (
  <svg
    width="32px"
    height="32px"
    viewBox="0 0 32 32"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
  >
    <title>ic_twitter</title>
    <g
      id="v1.7.0_2269_Unlock-advance"
      stroke="none"
      strokeWidth="1"
      fill="none"
      fillRule="evenodd"
    >
      <g
        id="2269-3_1280_share"
        transform="translate(-624.000000, -430.000000)"
        fillRule="nonzero"
      >
        <g id="Confirmation-Copy" transform="translate(440.000000, 300.000000)">
          <g
            id="VIP-Privilege-Model-Copy-5"
            transform="translate(150.000000, 106.000000)"
          >
            <g id="ic_twitter" transform="translate(34.000000, 24.000000)">
              <g>
                <g id="Dark_Blue" fill="#1DA1F2">
                  <path
                    d="M28,32 L4,32 C1.792,32 0,30.208 0,28 L0,4 C0,1.792 1.792,0 4,0 L28,0 C30.208,0 32,1.792 32,4 L32,28 C32,30.208 30.208,32 28,32 Z"
                    id="Path"
                  />
                </g>
                <g
                  id="Logo__x2014__FIXED"
                  transform="translate(6.000000, 7.840000)"
                  fill="#FFFFFF"
                >
                  <path
                    d="M6.288,16.288 C13.832,16.288 17.96,10.032 17.96,4.616 C17.96,4.44 17.96,4.264 17.952,4.088 C18.752,3.512 19.448,2.784 20,1.96 C19.264,2.288 18.472,2.504 17.64,2.608 C18.488,2.104 19.136,1.296 19.448,0.336 C18.656,0.808 17.776,1.144 16.84,1.328 C16.088,0.528 15.024,0.032 13.848,0.032 C11.584,0.032 9.744,1.872 9.744,4.136 C9.744,4.456 9.784,4.768 9.848,5.072 C6.44,4.904 3.416,3.264 1.392,0.784 C1.04,1.392 0.84,2.096 0.84,2.848 C0.84,4.272 1.568,5.528 2.664,6.264 C1.992,6.24 1.36,6.056 0.808,5.752 C0.808,5.768 0.808,5.784 0.808,5.808 C0.808,7.792 2.224,9.456 4.096,9.832 C3.752,9.928 3.392,9.976 3.016,9.976 C2.752,9.976 2.496,9.952 2.248,9.904 C2.768,11.536 4.288,12.72 6.08,12.752 C4.672,13.856 2.904,14.512 0.984,14.512 C0.656,14.512 0.328,14.496 0.008,14.456 C1.816,15.608 3.976,16.288 6.288,16.288"
                    id="Path"
                  />
                </g>
              </g>
            </g>
          </g>
        </g>
      </g>
    </g>
  </svg>
)
const CopyLinkWithBg = () => (
  <svg
    width="32px"
    height="32px"
    viewBox="0 0 32 32"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
  >
    <title>ic_copylink</title>
    <g
      id="v1.7.0_2269_Unlock-advance"
      stroke="none"
      strokeWidth="1"
      fill="none"
      fillRule="evenodd"
    >
      <g id="2269-3_1280_share" transform="translate(-734.000000, -430.000000)">
        <g id="Confirmation-Copy" transform="translate(440.000000, 300.000000)">
          <g
            id="VIP-Privilege-Model-Copy-6"
            transform="translate(260.000000, 106.000000)"
          >
            <g id="ic_copylink" transform="translate(34.000000, 24.000000)">
              <g>
                <rect
                  id="Rectangle"
                  fill="#A3ADC0"
                  x="0"
                  y="0"
                  width="32"
                  height="32"
                  rx="4"
                />
                <g
                  id="编组-3"
                  transform="translate(16.449977, 16.064282) rotate(-45.000000) translate(-16.449977, -16.064282) translate(7.449977, 12.064282)"
                  stroke="#FFFFFF"
                  strokeLinecap="round"
                  strokeWidth="1.45454545"
                >
                  <path
                    d="M12.1009219,0 L12.3636364,0 L13.8181818,0 C15.82649,0 17.4545455,1.62805546 17.4545455,3.63636364 C17.4545455,5.64467182 15.82649,7.27272727 13.8181818,7.27272727 L10.9090909,7.27272727 C8.90078273,7.27272727 7.27272727,5.64467182 7.27272727,3.63636364 L7.27272727,3.04804171"
                    id="路径"
                  />
                  <path
                    d="M4.82819466,0 L5.09090909,0 L6.54545455,0 C8.55376273,0 10.1818182,1.62805546 10.1818182,3.63636364 C10.1818182,5.64467182 8.55376273,7.27272727 6.54545455,7.27272727 L3.63636364,7.27272727 C1.62805546,7.27272727 0,5.64467182 0,3.63636364 L0,3.04804171"
                    id="路径备份"
                    transform="translate(5.090909, 3.636364) scale(-1, -1) translate(-5.090909, -3.636364) "
                  />
                </g>
              </g>
            </g>
          </g>
        </g>
      </g>
    </g>
  </svg>
)
const QrcodeIcon = () => (
  <svg
    width="30"
    height="30"
    viewBox="0 0 30 30"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g fill="none" fillRule="evenodd">
      <rect
        className="stroke"
        stroke="#FFF"
        strokeWidth="2"
        x="8"
        y="3"
        width="14"
        height="24"
        rx="4"
      />
      <rect
        className="fill"
        fill="#FFF"
        x="2"
        y="10"
        width="26"
        height="10"
        rx="1"
      />
      <path
        d="M6.71 19l.537-1.815H9.69L10.217 19h1.327l-2.492-7.82H7.918L5.378 19H6.71zm2.658-2.917H7.574l.908-3.066.886 3.066zM13.885 19v-2.997h1.327c.39-.004.751-.06 1.082-.172.332-.11.617-.27.857-.478.24-.208.427-.458.561-.752a2.35 2.35 0 00.202-.988c0-.376-.067-.714-.202-1.013a2.19 2.19 0 00-.56-.762c-.24-.21-.526-.371-.858-.484a3.437 3.437 0 00-1.082-.174h0-2.616V19h1.29zm1.327-4.04h-1.327v-2.738h1.327c.219.003.414.04.586.107.171.068.318.164.44.287.122.124.215.272.28.444.064.171.096.36.096.564 0 .193-.032.372-.097.537a1.185 1.185 0 01-.279.424 1.29 1.29 0 01-.44.277c-.172.066-.367.099-.586.099h0zM20.487 19v-2.997h1.326c.39-.004.751-.06 1.083-.172.33-.11.616-.27.856-.478.24-.208.427-.458.561-.752a2.35 2.35 0 00.202-.988c0-.376-.067-.714-.202-1.013a2.19 2.19 0 00-.56-.762c-.24-.21-.526-.371-.857-.484a3.437 3.437 0 00-1.083-.174h0-2.615V19h1.289zm1.326-4.04h-1.326v-2.738h1.326c.219.003.414.04.586.107.172.068.318.164.44.287.122.124.215.272.28.444.064.171.096.36.096.564 0 .193-.032.372-.097.537a1.185 1.185 0 01-.279.424 1.29 1.29 0 01-.44.277c-.172.066-.367.099-.586.099h0z"
        stroke="#000"
        strokeWidth=".2"
        fill="#000"
        fillRule="nonzero"
      />
      <rect
        className="fill"
        fill="#FFF"
        x="13"
        y="23"
        width="4"
        height="1.5"
        rx=".75"
      />
    </g>
  </svg>
)
const TransRightIcon = () => (
  <svg
    width="16"
    height="16"
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
  >
    <defs>
      <path
        d="M5.528 11.526a.67.67 0 00.001.945c.261.261.684.26.944 0l3.999-4.014a.67.67 0 00-.002-.947L6.47 3.528a.667.667 0 00-.944.003.67.67 0 00.003.945l3.524 3.51-3.526 3.54z"
        id="a"
      />
    </defs>
    <g fill="none" fillRule="evenodd">
      <mask id="b" fill="#fff">
        <use xlinkHref="#a" />
      </mask>
      <use fill="#999999" fillRule="nonzero" xlinkHref="#a" />
      <g mask="url(#b)" fill="none" id="green">
        <path d="M0 0h16v16H0z" />
      </g>
    </g>
  </svg>
)

const TransTooltipIcon = () => (
  <svg
    width="18"
    height="18"
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
  >
    <defs>
      <path
        d="M10 1.667a8.333 8.333 0 110 16.666 8.333 8.333 0 010-16.666zm0 1.666a6.667 6.667 0 100 13.334 6.667 6.667 0 000-13.334zm.417 5c.23 0 .416.187.416.417v5c0 .23-.186.417-.416.417h-.834a.417.417 0 01-.416-.417v-5c0-.23.186-.417.416-.417h.834zm0-2.5c.23 0 .416.187.416.417v.833c0 .23-.186.417-.416.417h-.834a.417.417 0 01-.416-.417V6.25c0-.23.186-.417.416-.417h.834z"
        id="TransTooltipIcon"
      />
    </defs>
    <use
      fill="#999"
      fillRule="nonzero"
      xlinkHref="#TransTooltipIcon"
      transform="translate(-1 -1)"
    />
  </svg>
)
const DownloadIcon = () => (
  <svg
    width="32px"
    height="32px"
    viewBox="0 0 32 32"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
  >
    <defs>
      <path
        d="M23,15.5 C23.2761424,15.5 23.5,15.7238576 23.5,16 L23.499,24.877 L26.142892,22.6476389 C26.3164739,22.4733675 26.5866427,22.453285 26.7824782,22.5878812 L26.8520983,22.6456062 C27.0485026,22.8405389 27.0494142,23.1574974 26.8541346,23.3535527 L26.8541346,23.3535527 L23.3671913,26.3523611 C23.1716515,26.5486776 22.8536128,26.5492901 22.6573156,26.3537282 L22.6573156,26.3537282 L19.1472323,23.3549198 C18.9511963,23.1596181 18.9508828,22.8426585 19.1465321,22.6469708 C19.3421814,22.451283 19.659705,22.4509701 19.855741,22.6462718 L19.855741,22.6462718 L22.499,24.862 L22.5,16 C22.5,15.7238576 22.7238576,15.5 23,15.5 Z"
        id="path-1"
      />
    </defs>
    <g
      id="ic/header/download"
      stroke="none"
      strokeWidth="1"
      fill="none"
      fillRule="evenodd"
    >
      <path
        d="M17.2290052,26 L11.2142857,26 C9.43908473,26 8,24.5225397 8,22.7 L8,7.3 C8,5.47746033 9.43908473,4 11.2142857,4 L19.7857143,4 C21.5609153,4 23,5.47746033 23,7.3 L23,11.4210526"
        id="Path"
        stroke="#FFFFFF"
        strokeWidth="1.5"
        strokeLinecap="round"
      />
      <rect id="Rectangle" x="14" y="21" width="4" height="2" rx="1" />
      <mask id="mask-2" fill="white">
        <use xlinkHref="#path-1" />
      </mask>
      <use
        id="Combined-Shape"
        stroke="#FFFFFF"
        strokeWidth="0.5"
        fill="#FFFFFF"
        xlinkHref="#path-1"
      />
    </g>
  </svg>
)

const PopCloseIcon = () => (
  <svg
    width="14px"
    height="14px"
    viewBox="0 0 21 21"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g stroke="none" strokeWidth="1" fillRule="evenodd">
      <g transform="translate(-614.000000, -438.000000)">
        <path
          d="M626.11988,450.11988 L626.11988,460.927572 C626.11988,461.756 625.448307,462.427572 624.61988,462.427572 L624.542957,462.427572 C623.71453,462.427572 623.042957,461.756 623.042957,460.927572 L623.042957,450.11988 L612.235265,450.11988 C611.406838,450.11988 610.735265,449.448307 610.735265,448.61988 L610.735265,448.542957 C610.735265,447.71453 611.406838,447.042957 612.235265,447.042957 L623.042957,447.042957 L623.042957,436.235265 C623.042957,435.406838 623.71453,434.735265 624.542957,434.735265 L624.61988,434.735265 C625.448307,434.735265 626.11988,435.406838 626.11988,436.235265 L626.11988,447.042957 L636.927572,447.042957 C637.756,447.042957 638.427572,447.71453 638.427572,448.542957 L638.427572,448.61988 C638.427572,449.448307 637.756,450.11988 636.927572,450.11988 L626.11988,450.11988 Z"
          transform="translate(624.581419, 448.581419) rotate(-225.000000) translate(-624.581419, -448.581419) "
          fill="#999999"
        />
      </g>
    </g>
  </svg>
)

const PlayerDownloadAppIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 22 12"
    className="app-icon"
  >
    <path
      d="M2 0h18c1.1 0 2 .9 2 2v8c0 1.1-.9 2-2 2H2c-1.1 0-2-.9-2-2V2C0 .9.9 0 2 0z"
      fillRule="evenodd"
      clipRule="evenodd"
      fill="#00cc36"
    />
    <text
      transform="translate(2.033 9)"
      fontSize="9"
      fontFamily="SFProText-Medium"
      fill="#fff"
    >
      APP
    </text>
  </svg>
)

const EpisodesPageTabDownArrow = () => (
  <svg
    width="24px"
    height="24px"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
  >
    <title>chevron down</title>
    <defs>
      <path
        d="M6.70678699,8.29257357 C6.31608616,7.9022259 5.68292124,7.90251218 5.29257357,8.29321301 C4.9022259,8.68391384 4.90251218,9.31707876 5.29321301,9.70742643 L11.2986413,15.7074264 C11.6893282,16.0977602 12.3224656,16.0974899 12.7128191,15.7068227 L18.7080011,9.70682271 C19.0983685,9.31614159 19.0981142,8.68297666 18.7074331,8.29260926 C18.3167519,7.90224186 17.683587,7.90249617 17.2932196,8.29317729 L12.0048246,13.5858223 L6.70678699,8.29257357 Z"
        id="path-1"
      />
    </defs>
    <g
      id="chevron-down"
      stroke="none"
      strokeWidth="1"
      fill="none"
      fillRule="evenodd"
    >
      <mask id="mask-2" fill="white">
        <use xlinkHref="#path-1" />
      </mask>
      <use id="Mask" fill="#222222" fillRule="nonzero" xlinkHref="#path-1" />
      <g id="Group" mask="url(#mask-2)" fill="#2C3038">
        <g id="colour">
          <rect id="Color" x="0" y="0" width="24" height="24" />
        </g>
      </g>
    </g>
  </svg>
)

const EpisodesPageListMode = () => (
  <svg
    width="24px"
    height="24px"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
  >
    <title>listmode</title>
    <g
      id="listmode"
      stroke="none"
      strokeWidth="1"
      fill="none"
      fillRule="evenodd"
    >
      <g id="btn_listmode">
        <rect id="Rectangle" x="0" y="0" width="24" height="24" />
        <path
          d="M6,18 L6,21 L2,21 L2,18 L6,18 Z M22,18 L22,21 L8,21 L8,18 L22,18 Z M6,11 L6,14 L2,14 L2,11 L6,11 Z M22,11 L22,14 L8,14 L8,11 L22,11 Z M6,4 L6,7 L2,7 L2,4 L6,4 Z M22,4 L22,7 L8,7 L8,4 L22,4 Z"
          id="Combined-Shape"
          fill="#D8D8D8"
        />
      </g>
    </g>
  </svg>
)

const EpisodesPageImgMode = () => (
  <svg
    width="24px"
    height="24px"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
  >
    <title>btnmode</title>
    <g
      id="btnmode"
      stroke="none"
      strokeWidth="1"
      fill="none"
      fillRule="evenodd"
    >
      <g id="btn_btnmode" fill="#D8D8D8">
        <path
          d="M7,17 L7,22 L2,22 L2,17 L7,17 Z M14,17 L14,22 L9,22 L9,17 L14,17 Z M21,17 L21,22 L16,22 L16,17 L21,17 Z M7,10 L7,15 L2,15 L2,10 L7,10 Z M14,10 L14,15 L9,15 L9,10 L14,10 Z M21,10 L21,15 L16,15 L16,10 L21,10 Z M7,3 L7,8 L2,8 L2,3 L7,3 Z M14,3 L14,8 L9,8 L9,3 L14,3 Z M21,3 L21,8 L16,8 L16,3 L21,3 Z"
          id="Combined-Shape"
        />
      </g>
    </g>
  </svg>
)

const PlayCollectBtn = () => (
  <svg
    width="72px"
    height="72px"
    viewBox="0 0 72 72"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
  >
    <title>btn/add list</title>
    <g
      id="btn/add-list"
      stroke="none"
      strokeWidth="1"
      fill="none"
      fillRule="evenodd"
    >
      <g id="btn/play" fill="#FFFFFF">
        <circle
          id="bg"
          strokeOpacity="0.246749344"
          stroke="#FFFFFF"
          strokeWidth="2"
          fillOpacity="0.0474213287"
          cx="36"
          cy="36"
          r="35"
        />
        <g id="ic/add-list" transform="translate(16.000000, 16.000000)">
          <rect
            id="Rectangle"
            x="23.3333333"
            y="11.6666667"
            width="12.5"
            height="2.5"
            rx="0.833333333"
          />
          <rect
            id="Rectangle"
            x="28.3333333"
            y="6.66666667"
            width="2.5"
            height="12.5"
            rx="0.833333333"
          />
          <path
            d="M20,5.83333333 L20,6.66666667 C20,7.12690396 19.626904,7.5 19.1666667,7.5 L10,7.5 L10,7.5 C9.59090019,7.5 9.25065272,7.79479193 9.18009278,8.18354061 L9.16666667,8.33333333 L9.16666667,33.66 L17.7799162,27.9198743 C18.5496857,27.4066946 19.5331003,27.3639296 20.3384773,27.7915794 L20.5534172,27.9198743 L29.1666667,33.6616667 L29.1666667,25.8333333 C29.1666667,25.373096 29.5397627,25 30,25 L30.8333333,25 C31.2935706,25 31.6666667,25.373096 31.6666667,25.8333333 L31.6666667,35.2191382 L31.6666667,35.2191382 C31.6666667,36.1396128 30.9204746,36.885982 30,36.885982 C29.6709584,36.885982 29.3492789,36.7884081 29.0754997,36.6058887 L19.1666667,30 L9.25783366,36.6058887 C8.49195251,37.1164761 7.45717027,36.9095197 6.94658284,36.1436385 C6.76406338,35.8698593 6.66666667,35.5481798 6.66666667,35.2191382 L6.66666667,8.33333333 C6.66666667,6.49238417 8.15905083,5 10,5 L19.1666667,5 C19.626904,5 20,5.37309604 20,5.83333333 Z"
            id="路径"
            fillRule="nonzero"
          />
        </g>
      </g>
    </g>
  </svg>
)

const PlayCollectHoverBtn = () => (
  <svg
    width="72px"
    height="72px"
    viewBox="0 0 72 72"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
  >
    <title>btn/add list_hover</title>
    <g
      id="V1.11.0_UI_3916_Home-page-details"
      stroke="none"
      strokeWidth="1"
      fill="none"
      fillRule="evenodd"
    >
      <g
        id="3916-1_Buttons-status"
        transform="translate(-498.000000, -336.000000)"
        fill="#FFFFFF"
      >
        <g id="btn/play" transform="translate(498.000000, 336.000000)">
          <circle
            id="bg"
            strokeOpacity="0.246749344"
            stroke="#FFFFFF"
            strokeWidth="2"
            fillOpacity="0.200120192"
            cx="36"
            cy="36"
            r="35"
          />
          <g id="ic/add-list" transform="translate(16.000000, 16.000000)">
            <rect
              id="Rectangle"
              x="23.3333333"
              y="11.6666667"
              width="12.5"
              height="2.5"
              rx="0.833333333"
            />
            <rect
              id="Rectangle"
              x="28.3333333"
              y="6.66666667"
              width="2.5"
              height="12.5"
              rx="0.833333333"
            />
            <path
              d="M20,5.83333333 L20,6.66666667 C20,7.12690396 19.626904,7.5 19.1666667,7.5 L10,7.5 L10,7.5 C9.59090019,7.5 9.25065272,7.79479193 9.18009278,8.18354061 L9.16666667,8.33333333 L9.16666667,33.66 L17.7799162,27.9198743 C18.5496857,27.4066946 19.5331003,27.3639296 20.3384773,27.7915794 L20.5534172,27.9198743 L29.1666667,33.6616667 L29.1666667,25.8333333 C29.1666667,25.373096 29.5397627,25 30,25 L30.8333333,25 C31.2935706,25 31.6666667,25.373096 31.6666667,25.8333333 L31.6666667,35.2191382 L31.6666667,35.2191382 C31.6666667,36.1396128 30.9204746,36.885982 30,36.885982 C29.6709584,36.885982 29.3492789,36.7884081 29.0754997,36.6058887 L19.1666667,30 L9.25783366,36.6058887 C8.49195251,37.1164761 7.45717027,36.9095197 6.94658284,36.1436385 C6.76406338,35.8698593 6.66666667,35.5481798 6.66666667,35.2191382 L6.66666667,8.33333333 C6.66666667,6.49238417 8.15905083,5 10,5 L19.1666667,5 C19.626904,5 20,5.37309604 20,5.83333333 Z"
              id="路径"
              fillRule="nonzero"
            />
          </g>
        </g>
      </g>
    </g>
  </svg>
)

const PlayCollectedBtn = () => (
  <svg
    width="72px"
    height="72px"
    viewBox="0 0 72 72"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
  >
    <title>btn/add list_selected</title>
    <g
      id="btn/add-list_selected"
      stroke="none"
      strokeWidth="1"
      fill="none"
      fillRule="evenodd"
    >
      <g id="btn/play">
        <circle
          id="bg"
          strokeOpacity="0.246749344"
          stroke="#FFFFFF"
          strokeWidth="2"
          fillOpacity="0.0500710227"
          fill="#FFFFFF"
          cx="36"
          cy="36"
          r="35"
        />
        <g
          id="Group"
          transform="translate(16.000000, 16.000000)"
          fill="#E2E2E3"
          stroke="#E2E2E3"
          strokeWidth="0.840277778"
        >
          <g id="icon/play/big-like2">
            <path
              d="M33.4325049,8.03144727 L34.2466067,8.76446783 C34.5914798,9.07499301 34.6193244,9.60629834 34.3087992,9.95117149 L15.6196387,30.707587 C15.3091135,31.0524602 14.7778081,31.0803047 14.432935,30.7697795 L13.9301479,30.3180646 C13.8977632,30.2939301 13.8667221,30.2671609 13.8373181,30.2377568 L13.7968872,30.1977474 L13.6188332,30.036759 C13.5722114,29.9947806 13.5313834,29.9487674 13.4964018,29.8997255 L5.21087531,21.611314 C4.88272642,21.2831651 4.88272642,20.7511307 5.21087531,20.4229818 L5.98549809,19.648359 C6.31364697,19.3202101 6.84568143,19.3202101 7.17383032,19.648359 L14.8918872,27.3667474 L32.2458012,8.09363977 C32.5563264,7.74876662 33.0876317,7.72092208 33.4325049,8.03144727 Z"
              id="Combined-Shape"
            />
          </g>
        </g>
      </g>
    </g>
  </svg>
)

const PlayCollectedHoverBtn = () => (
  <svg
    width="72px"
    height="72px"
    viewBox="0 0 72 72"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
  >
    <title>btn/add list_selected_hover</title>
    <g
      id="V1.11.0_UI_3916_Home-page-details"
      stroke="none"
      strokeWidth="1"
      fill="none"
      fillRule="evenodd"
    >
      <g
        id="3916-1_Buttons-status"
        transform="translate(-690.000000, -336.000000)"
      >
        <g id="btn/play" transform="translate(690.000000, 336.000000)">
          <circle
            id="bg"
            strokeOpacity="0.246749344"
            stroke="#FFFFFF"
            strokeWidth="2"
            fillOpacity="0.203971809"
            fill="#FFFFFF"
            cx="36"
            cy="36"
            r="35"
          />
          <g
            id="Group"
            transform="translate(16.000000, 16.000000)"
            fill="#E2E2E3"
            stroke="#E2E2E3"
            strokeWidth="0.840277778"
          >
            <g id="icon/play/big-like2">
              <path
                d="M33.4325049,8.03144727 L34.2466067,8.76446783 C34.5914798,9.07499301 34.6193244,9.60629834 34.3087992,9.95117149 L15.6196387,30.707587 C15.3091135,31.0524602 14.7778081,31.0803047 14.432935,30.7697795 L13.9301479,30.3180646 C13.8977632,30.2939301 13.8667221,30.2671609 13.8373181,30.2377568 L13.7968872,30.1977474 L13.6188332,30.036759 C13.5722114,29.9947806 13.5313834,29.9487674 13.4964018,29.8997255 L5.21087531,21.611314 C4.88272642,21.2831651 4.88272642,20.7511307 5.21087531,20.4229818 L5.98549809,19.648359 C6.31364697,19.3202101 6.84568143,19.3202101 7.17383032,19.648359 L14.8918872,27.3667474 L32.2458012,8.09363977 C32.5563264,7.74876662 33.0876317,7.72092208 33.4325049,8.03144727 Z"
                id="Combined-Shape"
              />
            </g>
          </g>
        </g>
      </g>
    </g>
  </svg>
)

const PlayShareBtn = () => (
  <svg
    width="72px"
    height="72px"
    viewBox="0 0 72 72"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
  >
    <title>btn/share</title>
    <g
      id="V1.11.0_UI_3916_Home-page-details"
      stroke="none"
      strokeWidth="1"
      fill="none"
      fillRule="evenodd"
    >
      <g
        id="3916-1_Buttons-status"
        transform="translate(-1458.000000, -204.000000)"
      >
        <g id="btn/play" transform="translate(1458.000000, 204.000000)">
          <circle
            id="bg"
            strokeOpacity="0.246749344"
            stroke="#FFFFFF"
            strokeWidth="2"
            fillOpacity="0.0474213287"
            fill="#FFFFFF"
            cx="36"
            cy="36"
            r="35"
          />
          <g
            id="Group"
            transform="translate(16.000000, 16.000000)"
            fill="#E2E2E3"
            fillRule="nonzero"
          >
            <g id="icon/play/big-share2">
              <path
                d="M18.3333333,7.5 L18.3333333,8.33333333 C18.3333333,8.79357062 17.9602373,9.16666667 17.5,9.16666667 L8.33333333,9.16666667 L8.33333333,9.16666667 C7.92423352,9.16666667 7.58398605,9.4614586 7.51342612,9.85020728 L7.5,10 L7.5,31.6666667 C7.5,32.0757665 7.79479193,32.416014 8.18354061,32.4865739 L8.33333333,32.5 L30,32.5 C30.4090998,32.5 30.7493473,32.2052081 30.8199072,31.8164594 L30.8333333,31.6666667 L30.8333333,22.5 C30.8333333,22.0397627 31.2064294,21.6666667 31.6666667,21.6666667 L32.5,21.6666667 C32.9602373,21.6666667 33.3333333,22.0397627 33.3333333,22.5 L33.3333333,31.6666667 L33.3333333,31.6666667 C33.3333333,33.5076158 31.8409492,35 30,35 L8.33333333,35 C6.49238417,35 5,33.5076158 5,31.6666667 L5,10 C5,8.15905083 6.49238417,6.66666667 8.33333333,6.66666667 L17.5,6.66666667 C17.9602373,6.66666667 18.3333333,7.03976271 18.3333333,7.5 Z"
                id="路径"
              />
              <path
                d="M31.6666667,6.66666667 C32.5871412,6.66666667 33.3333333,7.41285875 33.3333333,8.33333333 L33.3333333,15.8333333 C33.3333333,16.2935706 32.9602373,16.6666667 32.5,16.6666667 L31.6666667,16.6666667 C31.2064294,16.6666667 30.8333333,16.2935706 30.8333333,15.8333333 L30.8333333,9.16666667 L30.8333333,9.16666667 L24.1666667,9.16666667 C23.7064294,9.16666667 23.3333333,8.79357062 23.3333333,8.33333333 L23.3333333,7.5 C23.3333333,7.03976271 23.7064294,6.66666667 24.1666667,6.66666667 L31.6666667,6.66666667 L31.6666667,6.66666667 Z"
                id="路径"
              />
              <path
                d="M32.2558736,7.1536958 L32.8451536,7.7429271 C33.1705998,8.06835241 33.1706229,8.59598756 32.8452053,8.92144143 L20.5893013,21.1784839 C20.2638826,21.503939 19.7362451,21.5039635 19.410793,21.1785417 C19.410792,21.1785407 19.410791,21.1785397 19.410793,21.1785356 L18.821513,20.5893043 C18.4960669,20.263879 18.4960437,19.7362439 18.8214613,19.41079 L31.0773653,7.15374752 C31.4027841,6.82829247 31.9304216,6.82826797 32.2558736,7.15368976 C32.2558746,7.15369077 32.2558756,7.15369177 32.2558736,7.1536958 Z"
                id="路径-16"
              />
            </g>
          </g>
        </g>
      </g>
    </g>
  </svg>
)

const PlayShareHoverBtn = () => (
  <svg
    width="72px"
    height="72px"
    viewBox="0 0 72 72"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
  >
    <title>btn/share_hover</title>
    <g
      id="V1.11.0_UI_3916_Home-page-details"
      stroke="none"
      strokeWidth="1"
      fill="none"
      fillRule="evenodd"
    >
      <g
        id="3916-1_Buttons-status"
        transform="translate(-1458.000000, -336.000000)"
      >
        <g id="btn/play" transform="translate(1458.000000, 336.000000)">
          <circle
            id="bg"
            strokeOpacity="0.246749344"
            stroke="#FFFFFF"
            strokeWidth="2"
            fillOpacity="0.200120192"
            fill="#FFFFFF"
            cx="36"
            cy="36"
            r="35"
          />
          <g
            id="Group"
            transform="translate(16.000000, 16.000000)"
            fill="#E2E2E3"
            fillRule="nonzero"
          >
            <g id="icon/play/big-share2">
              <path
                d="M18.3333333,7.5 L18.3333333,8.33333333 C18.3333333,8.79357062 17.9602373,9.16666667 17.5,9.16666667 L8.33333333,9.16666667 L8.33333333,9.16666667 C7.92423352,9.16666667 7.58398605,9.4614586 7.51342612,9.85020728 L7.5,10 L7.5,31.6666667 C7.5,32.0757665 7.79479193,32.416014 8.18354061,32.4865739 L8.33333333,32.5 L30,32.5 C30.4090998,32.5 30.7493473,32.2052081 30.8199072,31.8164594 L30.8333333,31.6666667 L30.8333333,22.5 C30.8333333,22.0397627 31.2064294,21.6666667 31.6666667,21.6666667 L32.5,21.6666667 C32.9602373,21.6666667 33.3333333,22.0397627 33.3333333,22.5 L33.3333333,31.6666667 L33.3333333,31.6666667 C33.3333333,33.5076158 31.8409492,35 30,35 L8.33333333,35 C6.49238417,35 5,33.5076158 5,31.6666667 L5,10 C5,8.15905083 6.49238417,6.66666667 8.33333333,6.66666667 L17.5,6.66666667 C17.9602373,6.66666667 18.3333333,7.03976271 18.3333333,7.5 Z"
                id="路径"
              />
              <path
                d="M31.6666667,6.66666667 C32.5871412,6.66666667 33.3333333,7.41285875 33.3333333,8.33333333 L33.3333333,15.8333333 C33.3333333,16.2935706 32.9602373,16.6666667 32.5,16.6666667 L31.6666667,16.6666667 C31.2064294,16.6666667 30.8333333,16.2935706 30.8333333,15.8333333 L30.8333333,9.16666667 L30.8333333,9.16666667 L24.1666667,9.16666667 C23.7064294,9.16666667 23.3333333,8.79357062 23.3333333,8.33333333 L23.3333333,7.5 C23.3333333,7.03976271 23.7064294,6.66666667 24.1666667,6.66666667 L31.6666667,6.66666667 L31.6666667,6.66666667 Z"
                id="路径"
              />
              <path
                d="M32.2558736,7.1536958 L32.8451536,7.7429271 C33.1705998,8.06835241 33.1706229,8.59598756 32.8452053,8.92144143 L20.5893013,21.1784839 C20.2638826,21.503939 19.7362451,21.5039635 19.410793,21.1785417 C19.410792,21.1785407 19.410791,21.1785397 19.410793,21.1785356 L18.821513,20.5893043 C18.4960669,20.263879 18.4960437,19.7362439 18.8214613,19.41079 L31.0773653,7.15374752 C31.4027841,6.82829247 31.9304216,6.82826797 32.2558736,7.15368976 C32.2558746,7.15369077 32.2558756,7.15369177 32.2558736,7.1536958 Z"
                id="路径-16"
              />
            </g>
          </g>
        </g>
      </g>
    </g>
  </svg>
)

const PlayDownAppBtn = () => (
  <svg
    width="72px"
    height="72px"
    viewBox="0 0 72 72"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
  >
    <title>btn/download</title>
    <g
      id="V1.11.0_UI_3916_Home-page-details"
      stroke="none"
      strokeWidth="1"
      fill="none"
      fillRule="evenodd"
    >
      <g
        id="3916-1_Buttons-status"
        transform="translate(-1266.000000, -204.000000)"
      >
        <g id="btn/play" transform="translate(1266.000000, 204.000000)">
          <circle
            id="bg"
            strokeOpacity="0.246749344"
            stroke="#FFFFFF"
            strokeWidth="2"
            fillOpacity="0.0474213287"
            fill="#FFFFFF"
            cx="36"
            cy="36"
            r="35"
          />
          <g
            id="Group"
            transform="translate(16.000000, 16.000000)"
            fill="#E2E2E3"
            fillRule="nonzero"
          >
            <g id="icon/play/big-download2">
              <path
                d="M35,32.5 C35.4602373,32.5 35.8333333,32.873096 35.8333333,33.3333333 L35.8333333,34.1666667 C35.8333333,34.626904 35.4602373,35 35,35 L5,35 C4.53976271,35 4.16666667,34.626904 4.16666667,34.1666667 L4.16666667,33.3333333 C4.16666667,32.873096 4.53976271,32.5 5,32.5 L35,32.5 Z M20.4166667,3.33333333 C20.876904,3.33333333 21.25,3.70642938 21.25,4.16666667 L21.2496667,23.6193333 L27.981453,16.9107443 C28.3079741,16.5853074 28.8373693,16.5853074 29.1638904,16.9107443 L29.7551092,17.5 C30.0816303,17.8254369 30.0816303,18.3530744 29.7551092,18.6785113 L20.8868281,27.5173461 C20.4378616,27.9648218 19.7331891,28.0021115 19.2416049,27.629215 L19.1131719,27.5173461 L10.2448908,18.6785113 C9.91836973,18.3530744 9.91836973,17.8254369 10.2448908,17.5 L10.8361096,16.9107443 C11.1626638,16.5853828 11.6920172,16.5853585 12.0186014,16.9106902 L18.7496667,23.6183333 L18.75,4.16666667 C18.75,3.70642938 19.123096,3.33333333 19.5833333,3.33333333 L20.4166667,3.33333333 Z"
                id="Combined-Shape"
              />
            </g>
          </g>
        </g>
      </g>
    </g>
  </svg>
)

const PlayButtonIcon = () => (
  <svg
    viewBox="0 0 72 72"
    xmlns="http://www.w3.org/2000/svg"
    className="play-button"
  >
    <g fill="none" fillRule="evenodd">
      <circle
        strokeOpacity=".247"
        stroke="#00CC36"
        strokeWidth="2"
        fill="#00CC36"
        cx="36"
        cy="36"
        r="35"
      />
      <path
        d="M50.634 37.467l-22.32 13.48a1.714 1.714 0 01-2.6-1.467V22.52a1.714 1.714 0 012.6-1.468l22.32 13.48a1.714 1.714 0 010 2.935z"
        fill="#FFF"
      />
    </g>
  </svg>
)
const RightIcon = () => (
  <svg
    width="16px"
    height="12px"
    viewBox="0 0 16 12"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g id="控件" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <g id="一列/浅色1920" transform="translate(-1121.000000, -34.000000)">
        <g id="形状结合" transform="translate(1117.000000, 28.000000)">
          <path d="M4.14644661,12.5104076 C3.95118446,12.3151455 3.95118446,11.998563 4.14644661,11.8033009 L4.85355339,11.0961941 C5.04881554,10.9009319 5.36539803,10.9009319 5.56066017,11.0961941 L9.449,14.985 L18.2885822,6.14644661 C18.4838444,5.95118446 18.8004269,5.95118446 18.995689,6.14644661 L19.7027958,6.85355339 C19.8980579,7.04881554 19.8980579,7.36539803 19.7027958,7.56066017 L9.80330086,17.4601551 C9.60803871,17.6554173 9.29145622,17.6554173 9.09619408,17.4601551 L4.14644661,12.5104076 Z" />
        </g>
      </g>
    </g>
  </svg>
)

const EditIcon = () => (
  <svg
    className="translate-play"
    width="50px"
    height="51px"
    viewBox="0 0 50 51"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <g
        id="2275-1_1920_Subtitle-translation"
        transform="translate(-930.000000, -860.000000)"
      >
        <g transform="translate(930.000000, 860.000000)">
          <circle fill="#000000" opacity="0.600000024" cx="25" cy="25" r="25" />
          <g transform="translate(12.500000, 12.500000)" fill="#1CC749">
            <rect
              x="4.31034483"
              y="18.9655172"
              width="16.3793103"
              height="1.72413793"
              rx="0.413793103"
            />
            <path
              d="M6.54248864,8.81540528 L19.1947156,8.81540528 C19.4232472,8.81540528 19.6085087,9.00066676 19.6085087,9.22919838 L19.6085087,11.711957 C19.6085087,11.9404886 19.4232472,12.1257501 19.1947156,12.1257501 L6.52043933,12.1257501 C6.43125733,12.1257501 6.34359036,12.1026914 6.26594962,12.0588127 L3.60591137,10.5554894 C3.45669377,10.471159 3.40409237,10.2818309 3.48842284,10.1326133 C3.52213053,10.0729696 3.57462668,10.0261558 3.63772594,9.99947126 L6.34102322,8.85625362 C6.40476906,8.82929566 6.47327691,8.81540528 6.54248864,8.81540528 Z"
              transform="translate(11.332647, 10.470578) rotate(-45.000000) translate(-11.332647, -10.470578) "
            />
          </g>
        </g>
      </g>
    </g>
  </svg>
)

const PlayBtnGreenTriangle = () => (
  <svg
    className="translate-play"
    width="50px"
    height="51px"
    viewBox="0 0 50 51"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g
      stroke="none"
      strokeWidth="1"
      fill="none"
      fillRule="evenodd"
      opacity="0.803803943"
    >
      <g
        id="2275-1_1920_Subtitle-translation"
        transform="translate(-1240.000000, -860.000000)"
      >
        <g transform="translate(1240.000000, 860.000000)">
          <rect
            id="Rectangle"
            fill="#000000"
            opacity="0.600000024"
            x="0"
            y="0"
            width="50"
            height="50"
            rx="25"
          />
          <path
            d="M31.7310367,25.7245814 L21.3085273,32.6678077 C20.9097907,32.9330155 20.3735288,32.8234542 20.1097225,32.4225992 C20.0154442,32.2808652 19.9652778,32.1130452 19.9652778,31.9426166 L19.9652778,18.0570337 C19.9652778,17.5770511 20.3527702,17.1875 20.8302163,17.1875 C21.0006092,17.1875 21.1666774,17.237933 21.3085273,17.3318426 L31.7310367,24.2750688 C32.1289084,24.5402766 32.2378907,25.0793875 31.9740844,25.4802425 C31.9100789,25.5767607 31.8279098,25.660236 31.7310367,25.7245814"
            fill="#1CC749"
          />
        </g>
      </g>
    </g>
  </svg>
)

const EpisodeIcon = () => (
  <svg
    className="episode-icon"
    width="20px"
    height="21px"
    viewBox="0 0 20 21"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <g
        id="2275-1_1920_Subtitle-translation"
        transform="translate(-1384.000000, -977.000000)"
        fill="#828387"
        fillRule="nonzero"
      >
        <g
          id="Card/Subtitle/1920"
          transform="translate(502.000000, 448.000000)"
        >
          <g transform="translate(0.000000, 306.000000)">
            <g id="Card-3" transform="translate(620.000000, 50.000000)">
              <g transform="translate(262.000000, 173.000000)">
                <path d="M14.5,2 C15.3284271,2 16,2.67157288 16,3.5 L16,5.999 L18.5,6 C19.3284271,6 20,6.67157288 20,7.5 L20,16.5 C20,17.3284271 19.3284271,18 18.5,18 L5.5,18 C4.67157288,18 4,17.3284271 4,16.5 L3.999,14 L1.5,14 C0.671572875,14 5.68434189e-14,13.3284271 5.68434189e-14,12.5 L5.68434189e-14,3.5 C5.68434189e-14,2.67157288 0.671572875,2 1.5,2 L14.5,2 Z M16,12.5 C16,13.3284271 15.3284271,14 14.5,14 L5.499,14 L5.5,16.5 L18.5,16.5 L18.5,7.5 L16,7.499 L16,12.5 Z M14.5,3.5 L1.5,3.5 L1.5,12.5 L14.5,12.5 L14.5,3.5 Z" />
              </g>
            </g>
          </g>
        </g>
      </g>
    </g>
  </svg>
)

const GrayStar = () => (
  <svg
    width="20px"
    height="20px"
    viewBox="0 0 28 27"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g
      id="V1.12.0_UI_4391_Watch-Page-Add-Rating"
      stroke="none"
      strokeWidth="1"
      fill="none"
      fillRule="evenodd"
    >
      <g
        id="4391-6_1920_info"
        transform="translate(-568.000000, -906.000000)"
        fill="#999999"
        fillRule="nonzero"
      >
        <g id="Group-10-Copy-11" transform="translate(526.000000, 880.000000)">
          <g
            id="ic/star_grey_hover"
            transform="translate(40.000000, 24.000000)"
          >
            <path
              d="M16.7983826,2.56356746 L19.7968803,11.2875241 L29.1657516,11.3941138 C29.9719564,11.4033379 30.3057022,12.4128653 29.6590696,12.8853446 L22.1424877,18.3829131 L24.9344802,27.1724634 C25.17436,27.9288402 24.3014061,28.55198 23.643301,28.0938493 L16.0005215,22.7674392 L8.35669898,28.0928244 C7.69963687,28.5509551 6.82563997,27.9267904 7.06551979,27.1714385 L9.85751226,18.3818882 L2.34093036,12.8843197 C1.69429781,12.4118404 2.02804364,11.402313 2.83424842,11.3930889 L12.2031197,11.2864992 L15.2016174,2.56254256 C15.4602704,1.81231509 16.5407725,1.81231509 16.7983826,2.56356746 Z"
              id="Star"
            />
          </g>
        </g>
      </g>
    </g>
  </svg>
)

const GreenStar = () => (
  <svg
    width="20px"
    height="20px"
    viewBox="0 0 28 27"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g
      id="V1.12.0_UI_4391_Watch-Page-Add-Rating"
      stroke="none"
      strokeWidth="1"
      fill="none"
      fillRule="evenodd"
    >
      <g
        id="4391-6_1920_info"
        transform="translate(-948.000000, -906.000000)"
        fill="#1CC749"
        fillRule="nonzero"
      >
        <g id="Group-10-Copy-10" transform="translate(906.000000, 880.000000)">
          <g id="ic/star_green" transform="translate(40.000000, 24.000000)">
            <path
              d="M16.7983826,2.56356746 L19.7968803,11.2875241 L29.1657516,11.3941138 C29.9719564,11.4033379 30.3057022,12.4128653 29.6590696,12.8853446 L22.1424877,18.3829131 L24.9344802,27.1724634 C25.17436,27.9288402 24.3014061,28.55198 23.643301,28.0938493 L16.0005215,22.7674392 L8.35669898,28.0928244 C7.69963687,28.5509551 6.82563997,27.9267904 7.06551979,27.1714385 L9.85751226,18.3818882 L2.34093036,12.8843197 C1.69429781,12.4118404 2.02804364,11.402313 2.83424842,11.3930889 L12.2031197,11.2864992 L15.2016174,2.56254256 C15.4602704,1.81231509 16.5407725,1.81231509 16.7983826,2.56356746 Z"
              id="Star"
            />
          </g>
        </g>
      </g>
    </g>
  </svg>
)

const Checkbox = () => (
  <svg
    width="16px"
    height="16px"
    viewBox="0 0 16 16"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g
      id="V1.8.0_UI_2504_Purchase-popup"
      stroke="none"
      strokeWidth="1"
      fill="none"
      fillRule="evenodd"
    >
      <g
        id="2504-14_step2"
        transform="translate(-180.000000, -622.000000)"
        fill="#FFFFFF"
        stroke="#DBDBDB"
        strokeWidth="1.2"
      >
        <g id="Group-20" transform="translate(180.000000, 138.000000)">
          <g id="Group-31" transform="translate(0.000000, 482.000000)">
            <rect
              id="checkbox"
              x="0.6"
              y="2.6"
              width="14.8"
              height="14.8"
              rx="1.6"
            />
          </g>
        </g>
      </g>
    </g>
  </svg>
)

const CheckboxError = () => (
  <svg
    width="16px"
    height="16px"
    viewBox="0 0 16 16"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g
      id="V1.8.0_UI_2504_Purchase-popup"
      stroke="none"
      strokeWidth="1"
      fill="none"
      fillRule="evenodd"
    >
      <g
        id="2504-15_step2"
        transform="translate(-180.000000, -674.000000)"
        fill="#FFFFFF"
        stroke="#E60000"
        strokeWidth="1.2"
      >
        <g id="Group-20" transform="translate(180.000000, 548.000000)">
          <g id="Group-31" transform="translate(0.000000, 124.000000)">
            <rect
              id="checkbox_error"
              x="0.6"
              y="2.6"
              width="14.8"
              height="14.8"
              rx="1.6"
            />
          </g>
        </g>
      </g>
    </g>
  </svg>
)
const CheckboxSelected = () => (
  <svg
    width="16px"
    height="16px"
    viewBox="0 0 16 16"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
  >
    <title>checkbox_checked</title>
    <g
      id="checkbox_checked"
      stroke="none"
      strokeWidth="1"
      fill="none"
      fillRule="evenodd"
    >
      <g id="checkbox-checked">
        <rect
          id="checkbox"
          stroke="#BB8B51"
          strokeWidth="1.33333333"
          fill="#BB8B51"
          x="0.666666667"
          y="0.666666667"
          width="14.6666667"
          height="14.6666667"
          rx="1.33333333"
        />
        <path
          d="M4.47339341,7.44469696 C4.15506199,7.09441493 3.61765025,7.07276925 3.27305068,7.39634997 C2.92845112,7.71993069 2.90715658,8.26620438 3.225488,8.6164864 L5.94716894,11.6113398 C6.28182187,11.9795815 6.85361277,11.9816871 7.19088173,11.6159198 L12.7703208,5.56503248 C13.0911406,5.21710416 13.0737405,4.67068798 12.7314564,4.34457777 C12.3891724,4.01846757 11.8516205,4.03615465 11.5308006,4.38408298 L6.57563907,9.75794295 L4.47339341,7.44469696 Z"
          id="Path"
          fill="#FFFFFF"
          fillRule="nonzero"
        />
      </g>
    </g>
  </svg>
)
const PopUpWindowCloseIcon = () => (
  <svg
    width="26px"
    height="26px"
    viewBox="0 0 26 26"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
  >
    <title>Icon/24/Clear</title>
    <g
      id="9260_开屏弹窗"
      stroke="none"
      strokeWidth="1"
      fill="none"
      fillRule="evenodd"
    >
      <g
        id="PCW-1920-内容运营示例"
        transform="translate(-1264.000000, -301.000000)"
      >
        <g id="编组-3" transform="translate(660.000000, 301.000000)">
          <g id="Icon/24/Clear" transform="translate(604.000000, 0.000000)">
            <circle
              id="Oval-Copy"
              fillOpacity="0.5"
              fill="#000000"
              cx="13"
              cy="13"
              r="13"
            />
            <path
              d="M13,0 C20.1797017,0 26,5.82029825 26,13 C26,20.1797017 20.1797017,26 13,26 C5.82029825,26 0,20.1797017 0,13 C0,5.82029825 5.82029825,0 13,0 Z M13,1.5 C6.64872538,1.5 1.5,6.64872538 1.5,13 C1.5,19.3512746 6.64872538,24.5 13,24.5 C19.3512746,24.5 24.5,19.3512746 24.5,13 C24.5,6.64872538 19.3512746,1.5 13,1.5 Z"
              id="Oval-Copy"
              fillOpacity="0.701841128"
              fill="#FFFFFF"
              fillRule="nonzero"
            />
            <g
              id="编组-2"
              transform="translate(13.000000, 13.000000) rotate(45.000000) translate(-13.000000, -13.000000) translate(6.000000, 6.000000)"
              fill="#ECECEC"
            >
              <rect id="矩形" x="0" y="6" width="14" height="2" rx="0.5" />
              <rect id="矩形" x="6" y="0" width="2" height="14" rx="0.5" />
            </g>
          </g>
        </g>
      </g>
    </g>
  </svg>
)

 const LoginCloseIcon = () => (
  <svg
    width="16px"
    height="16px"
    viewBox="0 0 16 16"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
  >
    <title>icon_close@2x</title>
    <g id="控件" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <g id="icon" fill="#999999">
        <path
          d="M12.2426407,3.75735931 C12.4379028,3.95262146 12.4379028,4.26920395 12.2426407,4.46446609 L8.70639967,7.99929289 L12.2426407,11.5355339 C12.4379028,11.7307961 12.4379028,12.0473785 12.2426407,12.2426407 C12.0473785,12.4379028 11.7307961,12.4379028 11.5355339,12.2426407 L7.99929289,8.70639967 L4.46446609,12.2426407 C4.26920395,12.4379028 3.95262146,12.4379028 3.75735931,12.2426407 C3.56209717,12.0473785 3.56209717,11.7307961 3.75735931,11.5355339 L7.29218611,7.99929289 L3.75735931,4.46446609 C3.56209717,4.26920395 3.56209717,3.95262146 3.75735931,3.75735931 C3.95262146,3.56209717 4.26920395,3.56209717 4.46446609,3.75735931 L7.99929289,7.29218611 L11.5355339,3.75735931 C11.7307961,3.56209717 12.0473785,3.56209717 12.2426407,3.75735931 Z"
          id="路径"
        ></path>
      </g>
    </g>
  </svg>
)

export {
  IqiyiLogo,
  MenuIcon,
  ArrowLeft,
  ArrowRight,
  ArrowTopIcon,
  PlayIcon,
  DetailArrowIcon,
  CloseIcon,
  MultiDevicesIcon,
  TriangleIcon,
  AppleIcon,
  GoogleIcon,
  DropDownArrow,
  UpArrow,
  HeadDownArrow,
  HeaderCloseIcon,
  UnLoginUser,
  HoverUnLoginUser,
  LogoutBtn,
  DateLeftArrow,
  DateRightArrow,
  ColComIcon,
  ColHoverIcon,
  ColAddComIcon,
  ColAddedHoverIcon,
  CheckIcon,
  CheckedIcon,
  SearchShowMoreArrow,
  FBWithBg,
  TwitterWithBg,
  CopyLinkWithBg,
  hotPlayTotal,
  QrcodeIcon,
  TransRightIcon,
  TransTooltipIcon,
  DownloadIcon,
  PopCloseIcon,
  PlayerDownloadAppIcon,
  EpisodesPageTabDownArrow,
  EpisodesPageListMode,
  EpisodesPageImgMode,
  PlayCollectBtn,
  PlayCollectHoverBtn,
  PlayCollectedBtn,
  PlayCollectedHoverBtn,
  PlayShareBtn,
  PlayShareHoverBtn,
  PlayDownAppBtn,
  PlayButtonIcon,
  RightIcon,
  GreenStar,
  GrayStar,
  EditIcon,
  PlayBtnGreenTriangle,
  EpisodeIcon,
  Checkbox,
  CheckboxError,
  CheckboxSelected,
  PopUpWindowCloseIcon,
  LoginCloseIcon
}
