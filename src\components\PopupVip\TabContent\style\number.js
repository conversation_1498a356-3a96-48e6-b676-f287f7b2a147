import styled from 'styled-components'
const NumberAniWrapper = styled.div`
  display: flex;
  .number-animation-wrap {
    position: relative;
    height: 33px;
    display: inline-block;
    overflow-y: hidden;

    .number-animation {
      height: auto;
      transform-origin: 0 0;
      transition: transform 0.2s ease-in-out; //过渡属性的名称 过渡动画所需的时间
      transition-delay: 0.1s;
    }

    .number,
    .number-animation-wrap-hidden {
      line-height: 33px;
      font-size: 24px;
      font-weight: bold;
      text-align: center;
    }

    .number-animation-wrap-hidden {
      visibility: hidden;
    }
  }
`

export default NumberAniWrapper
