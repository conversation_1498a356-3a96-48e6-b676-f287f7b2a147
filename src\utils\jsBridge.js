/* eslint-disable */
export default function() {
  function setupWebViewJavascriptBridge(callback) {
    if (window.WebViewJavascriptBridge) {
      return callback(WebViewJavascriptBridge)
    }
    if (window.WVJBCallbacks) {
      return window.WVJBCallbacks.push(callback)
    }
    window.WVJBCallbacks = [callback]
    const WVJBIframe = document.createElement('iframe')
    WVJBIframe.style.display = 'none'
    WVJBIframe.src = 'https://__bridge_loaded__'
    document.documentElement.appendChild(WVJBIframe)
    setTimeout(function() {
      document.documentElement.removeChild(WVJBIframe)
    }, 0)
  }
  setupWebViewJavascriptBridge(function(bridge) {
    bridge.registerHandler('testJavascriptHandler', function(
      data,
      responseCallback
    ) {
      alert(data)
    })
    window.jsWebViewBridge = bridge
  })
}
