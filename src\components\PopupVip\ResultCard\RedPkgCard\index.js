import React, { useEffect } from 'react'
import { getCookies } from '@/kit/cookie'
import { sendBlockPb } from '@/utils/pingBack'
import ResultCardWrapper from './style'
const mod = getCookies('mod') || 'intl'
const RedPkgCard = ({ cardData }) => {
  useEffect(() => {
    sendBlockPb('package', { rpage: 'redirection' })
  }, [])
  const {
    buttonTips,
    currencySymbol,
    desc,
    fee = 0,
    subTitle,
    title,
    link
  } = cardData
  return (
    <ResultCardWrapper>
      <div className="redpkg-card-wrapper">
        <div className="redpkg-card-header">
          <section className="red-header-content">
            <p className="red-value-title">{title}</p>
            {mod === 'vn' ? (
              <p className="red-value-wrapper">
                <span className="red-value-number">
                  {Number((fee / 100).toFixed(2))}
                </span>
                <span className="red-value-symbol">{currencySymbol}</span>
              </p>
            ) : (
              <p className="red-value-wrapper">
                <span className="red-value-symbol">{currencySymbol}</span>
                <span className="red-value-number">
                  {Number((fee / 100).toFixed(2))}
                </span>
              </p>
            )}

            <p className="redpkg-name">{subTitle}</p>
          </section>
        </div>
        <div className="red-pkg-detail">
          <p className="red-pkg-title">{desc}</p>
          <a
            href={link}
            target="_blank"
            rseat="share"
            data-pb="rpage=redirection&block=package"
          >
            <div className="red-pkg-share-btn">{buttonTips}</div>
          </a>
        </div>
      </div>
    </ResultCardWrapper>
  )
}
export default RedPkgCard
