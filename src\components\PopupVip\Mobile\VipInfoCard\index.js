import React from 'react'
import StyleWrapper from './style'

const Card = props => {
  const { selectedPkg, mod } = props
  const price = Number(
    (parseInt(selectedPkg.price, 10) / 100).toFixed(2)
  ).toString()
  const originalPrice = Number(
    (parseInt(selectedPkg.originalPrice, 10) / 100).toFixed(2)
  ).toString()
  return (
    <StyleWrapper>
      <div className="text">
        <div className="name">
          {`${selectedPkg.text3} ${selectedPkg.vipTypeName}`}
        </div>
        <div className="desc">
          {`${selectedPkg.detail} ${selectedPkg.autorenewTip}`}
        </div>
      </div>
      <div>
        <div className="vip-price">
          {mod === 'vn' ? (
            <>
              <span className="price">{price}</span>
              <span className="symbol">{selectedPkg.currencySymbol}</span>
            </>
          ) : (
            <>
              <span className="symbol">{selectedPkg.currencySymbol}</span>
              <span className="price">{price}</span>
            </>
          )}
        </div>
        {price < originalPrice && (
          <div className="vip-original-price">
            {mod === 'vn'
              ? originalPrice + selectedPkg.currencySymbol
              : selectedPkg.currencySymbol + originalPrice}
          </div>
        )}
      </div>
    </StyleWrapper>
  )
}

export default Card
