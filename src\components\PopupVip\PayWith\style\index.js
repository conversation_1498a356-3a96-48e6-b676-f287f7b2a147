import styled from 'styled-components'

const PayWithWrapper = styled.div`
  padding: 0;
  .pay-with-title {
    padding: 0 40px;
    font-size: 16px;
    color: #222222;
    font-weight: bold;
  }
  .pay-error-outer {
    padding: 0 40px;
  }
  .pay-error-wrapper {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 8px;
    margin-top: 12px;
    background: rgba(254, 59, 48, 0.08);
    border-radius: 4px;
  }
  .error-icon {
    width: 14px;
    height: 14px;
    margin-right: 4px;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAIKADAAQAAAABAAAAIAAAAACshmLzAAADc0lEQVRYCeVXO28TQRCeuTsrQAFCVJCKBqRQmYRHJBweghZHStLkX1DRUASJhop/gUAkUkILghAjhYcjVyBCQ5VQAaIBorNvmBnf7u2d7+FIoBQZybp9zHzz3dzs7BhgvwvuJgA0NVGHXm8agBqAMMq2J2L7bSDYAsAW+P4yrrU7w+IORYAa9VmI6B4BnB4GmEE3wcM72OosVumXEqCp8ychCh8S0cUqoLx9RHwDXm0e1959yduXtUIC/NaXIYIlAjpmjBHhB4d6BTx/Bcj/CMGhbd3r/uJP0T0DFN1kxCYRHLU2gN/AgxmOxiuz5j5zCcTOn7Hzmijzm/xh4Acw4t3H5xs/XYDsmK6PH4Gd6DYTvcWRO6D2gCGTuJFHYoCAhr0XvjdvzgpbUMNpXO20s87K5nSlPgEhLXPeSLIyf46EXzuX/RzeAIh88zjs6nzk4IXdOleHQlhs5QVYFJOxs/5SEZBsp4ieKICEPYBGkXPWHQOiOQWsBY/wZXszCy5zjUQXWvZzeDjnno50BPioWRD+5kXOVYedc7ItyA+63RlrlxkohuSPEdcHr1kC1Bg/a865ZjsnnLHJfSKyeizkpSJplu1TkldOEIv40IIWb1oCEEVNa8BHrSrb+YgmBIBKCSiWHF8jWk37k4SAlFcjcs53I1RcTywMek/t2PGVEOjX9r6OFJkqQTcCVcqyH3ywWo6vhEBysbBuXOGsRd7AyQFviAikMc0lliRhnovSNYySHKDyHFCcw2GSJ1wUDHZgBvyUun5K51rb4bOzNzgMgiUMu1qquVZr7RhUcla+h8ftDOGrGScE9D6PCWBvjBVKCcSF564Bqn7yZWWk70tnCQFpJoCu6mrUkyO5bPTznqlKGPiPcXXjU56eXZOb0or46kuShNzJmEVOqabeanYhdzCbVEIqrIRiqViMaVEcX5aAtFGcJVrP9T6XK7VMiEuRESTfDHOfjKWYvCk+3JbNElBDbqMsgNzncqUWSeAtcnldkB+rFLZeisFYFsb1wYtMKC10qb5uWjDe3NIr9cW6XqlpzeoZXZschZ3fb/nM9XsCbtHwdWfStUxHQHakh5PmgUUNBaAsEqKYI2rjOtfWrDafVR2IgCjsaUtmGMYkhm9KpXbI8f0XTaklsZdtuSEhT47G3vwxcUkokf/w1yzrY//N/wIHNoMQJWFG8wAAAABJRU5ErkJggg==)
      no-repeat center;
    background-size: 100% 100%;
  }
  .pay-error-tip {
    font-size: 12px;
    color: #fe3b30;
  }
  .pay-with-slider-wrapper {
    .right,
    .left {
      top: calc(50% + 8px);
    }
    .slider {
      padding-top: 0;
    }
  }
  .pay-with-list {
    display: flex;
    justify-content: flex-start;
    align-items: stretch;
    margin-top: 12px;
  }
  .pay-list-item {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: calc((100% - 24px) / 3);
    padding: 12px;
    background: #f2f2f2;
    border-radius: 4px;
    /* background: #fcf2e6; */
    border: 2px solid #f2f2f2;
    flex-shrink: 0;
    box-sizing: border-box;
    cursor: pointer;
    & + .pay-list-item {
      margin-left: 12px;
    }
    &:hover {
      background: #ffffff;
      border: 2px solid #f2bf83;
    }
    &.pay-list-foucs {
      background: #fcf2e6;
      border-color: #f2bf83;
    }
  }

  .pay-item-img {
    width: 36px;
    height: 36px;
    margin-right: 12px;
    border-radius: 2px;
    border: 0.5px solid #e6e6e6;
  }
  .pay-with-detail {
    flex-grow: 1;
    overflow: hidden;
  }
  .bank-detail {
    overflow: unset;
  }

  .gopay-icon {
    position: absolute;
    right: 12px;
    top: 12px;
    width: 64px;
    height: 20px;
  }
  .dss-icon {
    position: absolute;
    right: 12px;
    top: 12px;
    width: 46px;
    height: 20px;
  }

  .pay-item-title {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    margin-bottom: 2px;
    font-size: 14px;
    color: #222222;
  }
  .pay-item-discount {
    /* width: 100%; */
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 12px;
    color: #fa6400;
  }
  .pay-item-desc {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    line-height: 12px;
    font-size: 12px;
    color: #999999;
  }

  .pay-with-content {
    position: relative;
    /* height: 272px; */
    padding: 20px 0;
    margin: 12px 40px 0;
    background: #f2f2f2;
    border-radius: 4px;
  }
  .pay-with-detail {
    width: 420px;
    margin: 0 auto;
  }
  /* 已绑定银行卡样式开始 */
  .bank-login-wrapper {
    display: flex;
    align-items: center;
    margin: 12px 40px 0;
  }
  .bank-login-tip {
    font-size: 14px;
    color: #999999;
    letter-spacing: 0;
    font-weight: 400;
  }
  .bank-login-btn {
    cursor: pointer;
    &:hover {
      color: #222222;
    }
  }
  .bank-login-close {
    width: 16px;
    height: 16px;
    margin-left: 12px;
    cursor: pointer;
    &:hover {
      svg {
        g {
          fill: #222222;
        }
      }
    }
  }
  .bound-card-wrapper {
  }
  .bound-card-item {
    display: flex;
    align-items: center;
    padding: 16px 0;
    cursor: pointer;
    &:hover {
      div:before {
        border: 1px solid #222;
        
      }
    }
    & + .bound-card-item {
      border-top: 1px solid #e6e6e6;
    }
    .card-item-radio {
      position: absolute;
      margin-right: 12px;
      cursor: pointer;
      opacity: 0;
      /* visibility: hidden; */
      &:checked + div {
        &:before {
          border: 0.5px solid #222;
          box-shadow: inset 0 0 0 4px #222;
        }
      }
    }
    .card-item-icon {
      width: 30px;
      height: 20px;
      /* margin-left: 12px; */
      margin-right: 8px;
    }
    .item-name {
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #222222;
      &:before {
        display: flex;
        flex-shrink: 0;
        content: '';
        background-color: #fff;
        border: 0.5px solid #ccc;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        margin-right: 12px;
      }
    }
  }

  /* 已绑定银行样式结束 */
  .bankpay-title {
    margin-bottom: 10px;
    font-size: 14px;
    color: #222222;
  }
  .banknumber-input-wrapper {
    position: relative;
    display: block;
    margin-bottom: 12px;
    flex: 1;
    font-size: 16px;
    font-weight: 400;
    .banknumber-input-label {
      position: relative;
      box-sizing: border-box;
      border: 1px solid #e6e6e6;
      border-radius: 4px;
      transform-origin: bottom center;
      transition: border 0.3s;

      /* border: 1px solid #ff0000; */
      // overflow: hidden;
      display: flex;
      outline: none;
      &:focus-within {
        border: 1px solid #f2bf83;
      }
    }
    .banknumber-input-input {
      display: block;
      box-sizing: border-box;
      margin: 0;
      border: none;
      outline: none;
      // border-radius: 4px;
      padding: 0 12px 12px;
      width: 100%;
      height: 60px;
      border-top: solid 27px transparent;
      // color: rgba(var(--pure-material-onsurface-rgb, 0, 0, 0), 0.87);
      color: #222222;
      box-shadow: none;
      border-radius: 4px;
      font-size: 16px;
      transition:
        border-bottom 0.2s,
        background-color 0.2s;
      -webkit-appearance: none;
      &[type='password'] {
        font-size: 20px;
      }

      // &:focus{
      //   +span{
      //     font-size: 12px;
      //     line-height: 36px;
      //     // &::after{
      //     //     border-color: #00CC36;
      //     // }
      //   }
      // }
      &:not(:focus):placeholder-shown + span {
        font-size: 14px;
        line-height: 60px;
      }
    }
    .banknumber-input-desc {
      // font-size: 14px;
      // line-height: 60px;
      font-size: 12px;
      line-height: 36px;
      color: #999999;
      position: absolute;
      width: 90%;
      height: 100%;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: block;
      box-sizing: border-box;
      padding: 0 12px;
      pointer-events: none;
      /* border: 1px solid #FFFF00; */
      transition:
        color 0.2s,
        font-size 0.2s,
        line-height 0.2s;
    }
  }
  .other-err,
  .bankcard-err {
    display: none;
    width: 100%;
    margin-top: 8px;
    font-size: 12px;
    color: #fe3b30;
    letter-spacing: 0;
    font-weight: 400;
  }
  .mobile-error {
    color: #fe3d33;
    margin-top: 8px;
    font-size: 12px;
  }
  .has-error {
    .other-err,
    .bankcard-err {
      display: block;
    }
    .banknumber-input-label {
      border-color: #fe3b30;
    }
  }

  .bank-icon-wrapper {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    justify-content: center;
  }
  .bank-icon {
    width: 30px;
    height: 20px;
    & + .bank-icon {
      margin-left: 6px;
    }
  }
  .cvv-tip-wrapper {
    position: absolute;
    right: 12px;
    top: 20px;
    &:hover {
      .cvv-toast-wrapper {
        display: block;
      }
    }
  }
  .cvv-input {
    /* margin-bottom: 4px; */
  }
  .cvv-tip-img {
    width: 20px;
    height: 20px;
    cursor: pointer;
  }
  .cvv-toast-wrapper {
    display: none;
    position: absolute;
    bottom: calc(100% + 8px);
    width: 180px;
    left: -80px;
    padding: 12px;
    background: rgba(26, 28, 34, 1);
    /* border: 1px solid rgba(255, 255, 255, 0.12); */
    box-sizing: border-box;
    border-radius: 4px;
    &:after {
      content: '';
      width: 0;
      height: 0;
      border: 8px solid transparent;
      border-top: 8px solid rgba(26, 28, 34, 1);
      position: absolute;
      bottom: -16px;
      left: 50%;
      transform: translateX(-50%);
    }
  }
  .cvv-toast-tip {
    font-size: 14px;
    color: #ffffff;
    letter-spacing: 0;
    font-weight: 400;
  }
  .cvv-toast-img {
    width: 75px;
    height: 50px;
    margin-top: 8px;
  }

  .security-text-wrapper {
    text-align: center;
  }
  .security-text {
    padding-bottom: 10px;
    font-size: 12px;
    color: #999999;
    letter-spacing: 0;
    text-align: center;
    line-height: 14px;
    font-weight: 400;
  }
  .dssicon {
    width: 80px;
  }
  .gopay-title-wrapper {
    display: flex;
    align-items: center;
  }
  .gopay-back {
    margin-left: 12px;
    margin-bottom: 12px;
    font-size: 14px;
    color: #e09e51;
    cursor: pointer;
  }
  .areacode {
    position: absolute;
    top: 50%;
    left: 12px;
    transform: translateY(-50%);
    font-size: 16px;
    color: #222222;
  }
  .phonenumber-input-input {
    display: block;
    box-sizing: border-box;
    margin: 0;
    border: none;
    outline: none;
    // border-radius: 4px;
    padding: 0 12px 12px 60px;
    width: 100%;
    height: 60px;
    border-top: solid 27px transparent;
    // color: rgba(var(--pure-material-onsurface-rgb, 0, 0, 0), 0.87);
    color: #222222;
    box-shadow: none;
    border-radius: 4px;
    font-size: 16px;
    transition:
      border-bottom 0.2s,
      background-color 0.2s;
    -webkit-appearance: none;
    &[type='password'] {
      font-size: 20px;
    }

    // &:focus{
    //   +span{
    //     font-size: 12px;
    //     line-height: 36px;
    //     // &::after{
    //     //     border-color: #00CC36;
    //     // }
    //   }
    // }
    &:not(:focus):placeholder-shown + span {
      font-size: 14px;
      line-height: 60px;
    }
  }
  .phonenumber-input-desc {
    // font-size: 14px;
    // line-height: 60px;
    font-size: 12px;
    line-height: 36px;
    color: #999999;
    position: absolute;
    width: 90%;
    height: 100%;
    top: 0;
    left: 46px;
    right: 0;
    bottom: 0;
    display: block;
    box-sizing: border-box;
    padding: 0 12px;
    pointer-events: none;
    /* border: 1px solid #FFFF00; */
    transition:
      color 0.2s,
      font-size 0.2s,
      line-height 0.2s;
  }
  .phonenumber-wrapper {
    display: flex;
    align-items: center;
  }
  .phonenumber {
    font-size: 16px;
    color: #222222;
  }
  .change-phone {
    margin-left: 8px;
    font-size: 14px;
    color: #e09e51;
    cursor: pointer;
  }
  .gopay-desc {
    margin-top: -8px;
    font-size: 12px;
    color: #999999;
  }
  .third-pay {
    width: 420px;
    margin: 0 auto;
    font-size: 14px;
    color: #222222;
    text-align: center;
  }
  @media screen and (max-width: 1023px) {
    .pay-list-item {
      width: calc((100% - 24px) / 3);
    }
  }
  @media screen and (max-width: 767px) {
    .pay-list-item {
      width: calc((100% - 24px) / 3);
    }
    .pay-item-img {
      display: none;
    }
  }
`

export default PayWithWrapper
