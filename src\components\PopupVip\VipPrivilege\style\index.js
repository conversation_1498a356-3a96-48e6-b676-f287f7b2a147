import styled from 'styled-components'
const VipPrivilegeWrapper = styled.div`
  margin-top: 12px;
  padding: 0 40px;
  .privilege-title-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  }
  .privilege-title {
    line-height: 16px;
    font-size: 14px;
    color: #999999;
  }
  .privilege-btn-title {
    font-size: 14px;
  }
  .privilege-btn-wrapper {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    width: 180px;
    margin-left: 24px;
    color: #999999;
    cursor: pointer;

    .privilege-btn-icon {
      width: 12px;
      height: 12px;
      margin-left: 4px;
      background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAGKADAAQAAAABAAAAGAAAAADiNXWtAAABC0lEQVRIDWNgGAWjITAaAozoQTBr1qxOoFjB////2dDlCPB/MzIyTkxLSytFVseEzAGxgQZHkGE4SCsrEAeDGMgAwwImJqZCoIJ/yIqIZP8D+qAEXS2GBampqeuAivLRFRLBz4fqRVGKYQFINj09fQrQNaC4IAqA1IL0YFOMEckwRcB4YARG+CIgPwYmhoNeAozYOKAl/7HJY/UBSCFUQxKQ3oNNI1QNSA6kBqvhYDW4NMPE586dy/v3799DQB8ZwMTAGhkZLzAzM9slJyd/RhZHZ+P0AUwhyACg4V5A/gOYGIgNEiNkOEg9zjhAMgzMBPpE/c+fP8tAelhYWCKBht9EVzPKHw0B2oQAACcsSbnBlzQ5AAAAAElFTkSuQmCC)
        no-repeat center;
      background-size: 100% 100%;
      transition: transform 0.1s linear;
    }

    &:hover {
      color: #222222;
      .privilege-btn-icon {
        background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAGKADAAQAAAABAAAAGAAAAADiNXWtAAABGElEQVRIDWNgGAWjITAaAozoQaCsrNwJFCv4//8/G7ocPj4jI+NvoPzEu3fvliKrY0LmgNhAgyNINRyqjxWoLxjdPAwLgAoKgfgfukIi+CA9JejqmNEF3r9/f11ISOgdUNwLXQ4fHxhEeffu3VuIrgbDApACoCWnhIWFOYFetkHXgI3PxMTUCQz7NmxyGJEMUwQ0nBEY4YuA/BiYGA56CdDwOKAP/mOTxxYHYHUgDYKCgklAzh5sGqFie0BqcBkOUoPTBzBD1dXVeX///n0IyDeAiUHpC6ysrHY3b978jCaOwsXpA5gqkAHs7OygCH8AEwOxQWKEDAepJ+gDmKFAn6j/+fNnGUgPCwtLJNDwmzC5UXo0BGgbAgDgoVQ6s2CX2QAAAABJRU5ErkJggg==)
          no-repeat center;
        background-size: 100% 100%;
      }
    }
  }
  .icon-down {
    transform: rotate(180deg);
  }
  @keyframes rollDown {
    0% {
      transform: rotate(0);
    }
    100% {
      transform: rotate(180deg);
    }
  }

  .privilege-list {
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    height: auto;
    padding-top: 16px;
    overflow: hidden;
    transition: max-height 0.15s linear;
  }
  .default-privilege-list {
    transition: none 0.15s linear;
  }
  .privilege-item {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: calc((100% - 36px) / 4);
    height: 44px;
    margin-right: 12px;
    &:nth-child(4n) {
      margin-right: 0;
    }
    &:last-child {
      margin-bottom: 20px;
    }
  }
  .item-icon {
    width: 32px;
    height: 32px;
    margin-right: 8px;
  }
  .item-detail {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    font-size: 14px;
    color: #222222;
  }
  @media screen and (max-width: 767px) {
    .privilege-item {
      width: calc((100% - 24px) / 3);
      &:nth-child(3n) {
        margin-right: 0;
      }
      &:nth-child(4n) {
        margin-right: 12px;
      }
    }
  }
`

export default VipPrivilegeWrapper
