/**
 * @desc 新版本收银台优惠券
 * <AUTHOR>
 * @time 2022-08-20
 * @feature  GLOBALREQ-9714
 */

import React, { useState, useEffect, useRef, useLayoutEffect } from 'react'
import { getCookies } from '@/kit/cookie'
import { sendBlockPb } from '@/utils/pingBack'
import DiscountPopWrapper from './style'
const mod = getCookies('mod')
const fontSize = {
  1: 64,
  2: 56,
  3: 36,
  4: 36,
  5: 32,
  6: 30
}
let index = 0
const DiscountPop = React.memo(
  ({
    cashierLangPkg,
    selectProd,
    prodPosition,
    hidePop,
    setProdTime,
    pbInfo
  }) => {
    const { payItem = {}, pkgItem = {} } = selectProd
    const {
      promotionMark,
      trailPromotionProductDesc,
      promotionWindowSubTitle,
      countDown,
      ensureButtonDesc,
      discountMode,
      discountValue = '',
      discountPercent = ''
    } = payItem
    const { currencySymbol } = pkgItem
    const [startAni, setStartAni] = useState(false)
    const [hideAni, setHideAni] = useState(false)
    const [hideX, setHideX] = useState(0)
    const [hideY, setHideY] = useState(0)
    // const [start, setStart] = useState(false)
    const [countDay, setCountDay] = useState(0)
    const [countH, setCountH] = useState(0)
    const [countM, setCountM] = useState(0)
    const [countS, setCountS] = useState(0)
    const [countTime, setCountTime] = useState(countDown)
    const wrapperRef = useRef()
    const intervalRef = useRef(null)

    //  增加打点
    useEffect(() => {
      const { pkgItem } = selectProd
      if (pkgItem.productSetCode) {
       
        sendBlockPb('promotional_popup', {
          bstp: 56,
          rpage: 'cashier_popup',
          tjPb: {
            cashier_type: 'popup',
            v_pid: pbInfo.v_pid,
            v_prod: pbInfo.v_prod
          }
        })
      }
    }, [selectProd])
    useEffect(() => {
      setTimeout(() => {
        setStartAni(true)
      }, 300)
    }, [])

    useEffect(() => {
      if (countTime / 1000 - index > 0) {
        intervalRef.current = setInterval(() => {
          index = index + 1
          // countTimer(countTime - 1)
          let distance = countTime - index * 1000
          if (distance > -1) {
            let _days = Math.floor(distance / (1000 * 60 * 60 * 24))
            let _hours = Math.floor(
              (distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
            )
            let _minutes = Math.floor(
              (distance % (1000 * 60 * 60)) / (1000 * 60)
            )

            var _seconds = Math.floor((distance % (1000 * 60)) / 1000)
            let days = _days > 9 ? _days : '0' + _days
            let hours = _hours > 9 ? _hours : '0' + _hours
            let minutes = _minutes > 9 ? _minutes : '0' + _minutes
            let seconds = _seconds > 9 ? _seconds : '0' + _seconds
            setCountDay(() => days)
            setCountH(() => hours)
            setCountM(() => minutes)
            setCountS(() => seconds)
            setCountTime(() => countTime - 1000)
            // console.log('完成计算了', countDay, countM, countS)
          }
        }, 1000)
      } else {
        clearInterval(intervalRef.current)
      }
      return () => {
        clearInterval(intervalRef.current)
        index = 0
      }
    }, [])
    useLayoutEffect(() => {
      let popElement = wrapperRef.current
      let popPosition = popElement.getBoundingClientRect()
      setHideX(popPosition.left - 30)
      setHideY(popPosition.top - 30)
    }, [wrapperRef])

    // 关闭弹窗按钮
    const hidePopClk = () => {
      setProdTime(countDown - (index + 1) * 1000)
      hidePop()
      setTimeout(() => {
        setHideAni(true)
      }, 100)
    }

    return (
      <DiscountPopWrapper>
        {/* <Enhanced
        includeComma={false}
        animateToNumber={1234.56}
        configs={[{ mass: 1, tension: 280, friction: 60 }]}
      ></Enhanced> */}

        <section
          className={`discount-content-wrapper ${startAni ? 'shake' : ''} ${
            hideAni ? 'hide-pop' : ''
          }`}
          style={{
            transformOrigin: `${
              hideAni
                ? prodPosition.left -
                  hideX +
                  'px ' +
                  (prodPosition?.top - hideY) +
                  'px'
                : 'center'
            }`
          }}
          ref={wrapperRef}
        >
          <p
            className="pop-close"
            rseat="close"
            data-pb={`rpage=cashier_popup&block=promotional_popup&v_pid=${pbInfo.v_pid}&v_prod=${pbInfo.v_prod}&cashier_type=popup`}
            onClick={hidePopClk}
          ></p>
          <div className="pop-mark-wrapper">
            <i className="mark-left"></i>
            <div className="mark-title"><p className='mark-title-inner'>{promotionMark || ''}</p></div>
            <i className="mark-right"></i>
          </div>
          <div className="discount-wrapper">
            <div className="price-outter">
              {discountMode === 1 ? (
                <>
                  {mod === 'vn' ? (
                    <>
                      <p
                        className="price-number"
                        style={{
                          fontSize: `${
                            fontSize[
                              Number(
                                (discountValue / 100).toFixed(2)
                              ).toString().length
                            ] || '30'
                          }px`,
                          lineHeight: `${
                            fontSize[
                              Number(
                                (discountValue / 100).toFixed(2)
                              ).toString().length
                            ] || '30'
                          }px`
                        }}
                      >
                        {Number((discountValue / 100).toFixed(2)).toString()}
                      </p>
                      <p className="money-symbol">
                        &nbsp;&nbsp;{currencySymbol}
                      </p>
                    </>
                  ) : (
                    <>
                      <p className="money-symbol">
                        &nbsp;&nbsp;{currencySymbol}
                      </p>
                      <p
                        className="price-number"
                        style={{
                          fontSize: `${
                            fontSize[
                              Number(
                                (discountValue / 100).toFixed(2)
                              ).toString().length
                            ] || '30'
                          }px`,
                          lineHeight: `${
                            fontSize[
                              Number(
                                (discountValue / 100).toFixed(2)
                              ).toString().length
                            ] || '30'
                          }px`
                        }}
                      >
                        {Number((discountValue / 100).toFixed(2)).toString()}
                      </p>
                    </>
                  )}
                </>
              ) : (
                <>
                  <p
                    className="price-number"
                    style={{
                      fontSize: `${
                        fontSize[discountPercent.toString().length] || '30'
                      }px`,
                      lineHeight: `${
                        fontSize[discountPercent.toString().length] || '30'
                      }px`
                    }}
                  >
                    {discountPercent}%
                  </p>
                </>
              )}

              <p className="discount-text">
                {cashierLangPkg.PCW_CASHIER_1658477469276_901}
              </p>
            </div>
            <div className="discount-title">{trailPromotionProductDesc}</div>
          </div>
          {countDown > 0 ? (
            <section className="time-counter-section">
              <div className="time-counter">
                {+countDay > 0 ? (
                  <>
                    <p className="count-item count-day">{countDay}</p>
                    <p className="days-insert">
                      {cashierLangPkg.PCW_CASHIER_1658476805935_412}
                    </p>
                  </>
                ) : (
                  ''
                )}

                <p className="count-item count-hour">{countH}</p>
                <p className="separate-icon">:</p>
                <p className="count-item count-min">{countM}</p>
                <p className="separate-icon">:</p>
                <p className="count-item count-sec">{countS}</p>
              </div>
              <p className="time-counter-title">{promotionWindowSubTitle}</p>
            </section>
          ) : (
            <section className="time-counter-section">
              <p className="time-counter-title">{promotionWindowSubTitle}</p>
            </section>
          )}

          <div
            className="submit-btn"
            rseat="click"
            data-pb={`rpage=cashier_popup&block=promotional_popup&v_pid=${pbInfo.v_pid}&v_prod=${pbInfo.v_prod}&cashier_type=popup`}
            onClick={hidePopClk}
          >
            {ensureButtonDesc}
          </div>
        </section>
      </DiscountPopWrapper>
    )
  }
)
export default DiscountPop
