import React from 'react'
import qs from 'qs'
import $http from '@/kit/fetch'
import { removeProtocol } from '@/kit/url'
import { intlVipPayResultInterface } from '@/constants/interfaces'
import { sendBlockPb } from '@/utils/pingBack'
import { getUid } from '@/utils/userInfo'
import QueryPaymentWrapper from './style'
import Toast from '../Toast'

class QueryPayment extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      notFinishToast: false
    }
    this.timer = null
  }

  componentDidMount() {
    this.queryResult()
    const { params, pbInfo } = this.props
    const block = params.cashierType === 2 ? 'web_tvod_casher_redirection' : 'redirection'

    sendBlockPb(block, {
      bstp: 56,
      tjPb: pbInfo
    })
  }

  // clickReopen = () => {
  //   const { setStep } = this.props
  //   // 3ds跳会上一步银行卡, 以后会有其他支付方式弹窗
  //   setStep('bankCardStep')
  // }

  queryResult = async (onlyOnce) => {
    const { resultInfo, setStep, setErrorPop, feedbackUrl } = this.props
    const { platform, mod, lang, timeZone, vipOrder } = resultInfo
    if (!onlyOnce && this.timer) {
      clearTimeout(this.timer)
    }
    try {
      const resultData = (await $http(intlVipPayResultInterface, {
        method: 'POST',
        credentials: true,
        // timeout: 5,
        params: qs.stringify({
          platform,
          app_lm: mod,
          lang,
          timeZone,
          version: '1.0',
          orderCode: vipOrder
        })
      })) || {}
      const { i18nOrderInfo } = resultData.data || {}

      // 加线上日志
      const addloggers = {
        popupQueryPayment: {
          time: new Date(),
          uid: getUid(),
          resultData
        }
      }
      const loggers = localStorage.getItem('QiyiPlayerLogger')
      window.localStorage.setItem(
        'QiyiPlayerLogger',
        loggers + JSON.stringify(addloggers)
      )
      if (
        resultData.code === 'Q00301' ||
        (resultData.code === 'A00000' && i18nOrderInfo.status === 7)
      ) {
        onlyOnce && this.setState({ notFinishToast: true}, ()=> {
          setTimeout(() => {
            this.setState({ notFinishToast: false})}, 2000)
        })
        !onlyOnce && (this.timer = setTimeout(this.queryResult, 5000))
      } else if(resultData.code === 'Q00332') {
        setErrorPop('timeout')
      } else {
        setStep('payResultStep')
      }
    } catch (error) {
      if (error.message.match('timeout')) {
        setErrorPop('timeout')
      }
    }
  }

  componentWillUnmount() {
    if (this.timer) {
      clearTimeout(this.timer)
    }
  }

  render() {
    const { vipLangPkg, setStep, feedbackUrl, payIcon, pbInfo, payName, params } = this.props
    const { notFinishToast } = this.state

    const rpage = params.cashierType === 2 ? 'web_tvod_casher_redirection' : 'redirection'
    const pbInfoStr = qs.stringify(pbInfo) + '&bstp=56'
    return (
      <QueryPaymentWrapper>
        {/* <div className="step-3">{vipLangPkg.pcashier_query_stepTitle}</div> */}
        <div className="scroll-container">
          <div className="scroll-content">
            <div className="step-title">{vipLangPkg.pcashier_query_title}</div>
            <div className="step-tip">{vipLangPkg.pcashier_query_redirect}</div>
            <div className="img-text">
              {payIcon && (
                <img
                  alt=""
                  src={removeProtocol(payIcon)}
                />
              )}
              {payName && (
                <span className="text">{payName}</span>
              )}
              <a
                role="button"
                className="link"
                data-pb={`rpage=${rpage}&block=redirection&${pbInfoStr}`}
                rseat="reselect_paytype"
                onClick={() => {
                  setStep(params.cashierType === 2 ? 'vodOrderStep' : 'payTypeStep')
                }}
              >
                {vipLangPkg.pcashier_mpage_payment_reselect}
              </a>
            </div>
            <p className="finish-text">{vipLangPkg.pcashier_query_finish}</p>
            <p className="query-note-767">{vipLangPkg.pcashier_query_note}</p>
          </div>
        </div>
        <button
          type="button"
          className="containue"
          onClick={() => {
            // setStep('payResultStep')
            const onlyOnce = true
            this.queryResult(onlyOnce)
          }}
          data-pb={`rpage=${rpage}&block=redirection&${pbInfoStr}`}
          rseat="finish"
        >
          {vipLangPkg.pcashier_mpage_payment_result}
        </button>
        <p className="query-note">
          <span>{vipLangPkg.pcashier_query_note + ' '}</span>
          <a
            className="got-problem"
            target="_blank"
            rel="noopener noreferrer"
            href={feedbackUrl}
          >
            {vipLangPkg.pcashier_query_gotProblem}
          </a>
        </p>
        <a
          className="got-problem-767"
          target="_blank"
          rel="noopener noreferrer"
          href={feedbackUrl}
        >
          {vipLangPkg.pcashier_query_gotProblem}
        </a>
        {notFinishToast && <Toast msg={vipLangPkg.unpay_toast}/>}
      </QueryPaymentWrapper>
    )
  }
}

export default QueryPayment
