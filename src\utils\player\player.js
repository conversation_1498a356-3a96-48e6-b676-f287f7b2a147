import sendGtag from '@/utils/gtag'
import jsCallPlayer from './jsCallPlayer'

let playerManager
let constantType
class I71UnifiedPlayer {
  constructor(opt) {
    this.playerId = opt.playerId || 'flashbox'
    this._bindEvent()
  }

  _bindEvent() {
    const _this = this
    if (!window.QiyiPlayerLoader) {
      return
    }
    window.QiyiPlayerLoader.ready(qiyiPlayerManager => {
      playerManager = qiyiPlayerManager
      constantType = playerManager.constantType
      _this.player = playerManager.getPlayerById(_this.playerId)
      if (!_this.player) {
        return
      }
      window.playerEventsRecorder = {}
      _this._fire()
      const { eventsRecorder } = _this.player
      for (const key in eventsRecorder) {
        let eRecorder = {}
        const len = eventsRecorder[key].length
        for (let i = 0; i < len; i += 1) {
          eRecorder = eventsRecorder[key][i]
          _this.player.fire({ type: eRecorder.type, data: eRecorder.data })
          window.playerEventsRecorder[eRecorder.type] = eRecorder.data // 监听事件的业务js,后加载需主动触发
        }
      }
    })
  }

  _fire() {
    const _this = this
    const _player = _this.player
    // 视频切换
    _player.on(constantType.QYPLAYER_VIDEO_CHANGE, info => {
      const data = info.data.vi
      _this._videoid = data.vid
      _this._tvid = data.videoQipuId ? data.videoQipuId : data.tvid
      data.aid = data.albumQipuId ? data.albumQipuId : data.aid
      data.tvid = data.videoQipuId ? data.videoQipuId : data.tvid
      // data.ve = Q.getVideoEventID() || '';//某些pingback用到
      _this.fire({
        type: 'videoChanged',
        data
      })
    })
    _player.on(constantType.QYPLAYER_STATE_CHANGE, data => {
      const _type = data.data.state || ''

      if (_type === 'startplay') {
        sendGtag(null, true)
      }
      _this.fire({ type: _type.toLocaleLowerCase(), data: data.data })
    })
    _player.on(constantType.QYPLAYER_RECHARGE, info => {
      const data = info.data || {}
      _this.fire({ type: 'recharge', data })
    })
    _player.on(constantType.QYPLAYER_DOSOMETHING, data => {
      _this.fire({ type: 'setJsDoSomething', data: data.data })
    })
    _player.on(constantType.QYPLAYER_SHOW_LOGIN_PANEL, data => {
      _this.fire({ type: 'showLoginPanel', data: data.data })
    })
    _player.on(constantType.QYPLAYER_SHOW_APP_DOWNLOAD_PANEL, data => {
      _this.fire({ type: 'showAppDownloadPanel', data: data.data })
    })
    _player.on(constantType.QYPLAYER_SUBTITLE_FEEDBACK, data => {
      _this.fire({ type: 'showSubtitleFeedback', data: data.data })
    })
  }

  // 添加联播列表
  addVideoList(param) {
    jsCallPlayer({ id: this.playerId, name: 'addVideoList', param })
  }

  // JS要求切换片子,{vid:"",tvid:""}--应该与联播列表中字段相同
  switchVideo(param) {
    jsCallPlayer({ id: this.playerId, name: 'switchVideo', param })
  }

  // 播放器暂停播放
  pause() {
    jsCallPlayer({ id: this.playerId, name: 'pause' })
  }

  // 播放器恢复播放
  resume() {
    jsCallPlayer({ id: this.playerId, name: 'resume' })
  }

  // 广告播放器暂停播放
  pauseAds() {
    jsCallPlayer({ id: this.playerId, name: 'pauseAds' })
  }

  // 广告播放器恢复播放
  resumeAds() {
    jsCallPlayer({ id: this.playerId, name: 'resumeAds' })
  }

  // 清空播放列表
  removeVideoList(param) {
    jsCallPlayer({
      id: this.playerid || 'flashbox',
      name: 'removeVideoList',
      param
    })
  }

  on(_type, listener) {
    this.epCreateList()
    const type = _type.toLowerCase()
    this._ep_lists[type] = this._ep_lists[type] || []
    this._ep_lists[type].push({
      type,
      listener
    })
    return this
  }

  un(_type, listener) {
    this.epCreateList()
    if (_type) {
      const type = _type.toLowerCase()
      const listeners = this._ep_lists[type]
      if (listeners) {
        // const len = listeners.length;
        const isRemoveAll = !listener
        if (listeners && listeners.length > 0) {
          if (isRemoveAll) {
            this._ep_lists[type] = []
          } else {
            listeners.forEach((obj, index) => {
              if (obj && obj.listener === listener) {
                listeners[index] = null
              }
            })
          }
        }
      }
    } else {
      this.epClearList()
    }
    return this
  }

  fire(ev) {
    this.epCreateList()
    const type = ev.type.toLowerCase()
    const data = ev.data
    const listeners = this._ep_lists[type]
    if (listeners && listeners.length > 0) {
      listeners.forEach(obj => {
        try {
          if (obj && obj.listener) {
            obj.listener({ type, data })
          }
        } catch (e) {
          console.log(e)
        }
      })
    }
    return this
  }

  epCreateList() {
    if (!this._ep_lists) {
      this._ep_lists = {}
    }
  }

  epClearList() {
    this._ep_lists = null
  }
}

class I71Player {
  constructor(opt) {
    this.player = new I71UnifiedPlayer(opt)
    return this.player
  }

  static getPlayer(id) {
    if (this.player[id]) {
      return this.player[id]
    } else {
      this.player[id] = new I71Player({ playerid: id })
      return this.player[id]
    }
  }

  static destroy(id) {
    const player = this.player
    if (id) {
      player[id].un()
      player[id] = null
      delete player[id]
    } else {
      for (const i in player) {
        player[i].un()
      }
      this.player = {}
    }
  }
}
I71Player.player = {}

export default I71Player
