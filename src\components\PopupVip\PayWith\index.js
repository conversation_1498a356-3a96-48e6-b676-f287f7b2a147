/**
 * @desc 新版本收银台支付方式
 * <AUTHOR>
 * @time 2022-02-10
 * @feature  GLOBALREQ-6796
 */
import React, {
  useState,
  useEffect,
  useRef,
  forwardRef,
  useImperativeHandle
} from 'react'
import qs from 'qs'
import { LoginCloseIcon } from '@/components/svgs'
import { removeProtocol } from '@/kit/url'
import { bankLangPkg } from '../config'
import { sendBlockPb } from '@/utils/pingBack'
import { getCookies } from '@/kit/cookie'
import Slider from '../TabContent/slider'
import PayWithWrapper from './style/index'
import { isLogin } from '@/utils/userInfo'
const lang = getCookies('lang') || 'en_us'
const i18n = bankLangPkg[lang] || bankLangPkg['en_us']
const iconObj = {
  MASTERCARD: '//www.iqiyipic.com/new-vip/icon-master.png',
  JCB: '//www.iqiyipic.com/new-vip/icon-jcb.png',
  VISA: '//www.iqiyipic.com/new-vip/icon-visa.png'
}
const iconName = {
  MASTERCARD: 'Mastercard',
  JCB: 'JCB',
  VISA: 'VISA'
}
const PayWith = (props, ref) => {
  const {
    userCards,
    selectProd,
    vipLangPkg,
    cashierLangPkg,
    getPayWith,
    startLogin,
    hasCoupon = true,
    dopayError = '',
    tabClick,
    setNotabClick,
    tabChanged,
    afterLogin,
    defaultPayWith,
    changePW,
    pbInfo
  } = props
  const [selectPayWith, setSelectPayWith] = useState(0)
  const [payOptions, setPayOptions] = useState([])
  const [selectPayType, setSelectPayType] = useState('')
  const [validRes, setValidRes] = useState({
    cardNum: true,
    expiryMonth: true,
    expiryYear: true,
    securityCode: true,
    expireTime: true
  })
  const [cardtype, setCardType] = useState('')
  const [bankNumber, setBankNumber] = useState('')
  const [expireTime, setExpireTime] = useState('')
  const [cvvInput, setCvvInput] = useState('')
  const [monthErr, setMonthErr] = useState(false)
  const [mobile, setMobile] = useState('')
  const [mobileErr, setMobileErr] = useState('')
  // 绑定银行卡
  const [boundCard, setBoundCard] = useState(false)
  const [selectCardIndex, setCardIndex] = useState(0)
  const [choseBoundItem, setChoseBoundItem] = useState('')
  const [showLogin, setShowLogin] = useState(!isLogin())

  const [selectPayTypeIndex, setSelectPayTypeIndex] = useState(0)
  const cursorPosition = useRef()
  const cursorExpirPosition = useRef()
  const cardNumInput = React.createRef()
  const expireNumInput = React.createRef()

  const { pkgItem = {}, payType, payItem, canuseCoupon } = selectProd
  useEffect(() => {
    if (!selectProd.pkgItem) {
      setPayOptions([])
    }
  }, [selectProd])
  useEffect(() => {
    if (userCards) {
      setBoundCard(userCards && userCards.length > 0)
      setCardIndex(0)
      setChoseBoundItem(userCards[0])
    } else {
      setCardIndex(-1)
    }
    if (!isLogin()) {
      sendBlockPb('bank_login', {
        bstp: 56,
        rpage: 'cashier_popup',
        tjPb: {
          cashier_type: 'popup',
          abtest: pbInfo.abtest,
          fc: pbInfo.fc,
          fv: pbInfo.fv,
          v_pid: pbInfo.v_pid,
          v_prod: pbInfo.v_prod
        }
      })
    }
  }, [userCards])
  useEffect(() => {
    if (pkgItem.payTypeOptions) {
      let _index = -1
      _index = pkgItem.payTypeOptions.findIndex((item) => item.recommend === 1)

      if (_index === -1) {
        _index = pkgItem.payTypeOptions.findIndex((item) => {
          return item.payType === payType
        })
      }
      _index = _index === -1 ? 0 : _index
      setSelectPayTypeIndex(_index)
      setSelectPayType(pkgItem.payTypeOptions[_index].payType)
      getPayWith(pkgItem.payTypeOptions[_index])
      setPayOptions(pkgItem.payTypeOptions)
    }
  }, [pkgItem])
  useEffect(() => {
    if (
      bankNumber[bankNumber.length - 2] !== ' ' ||
      cursorPosition.current !== cardNumInput.current.selectionEnd - 1
    ) {
      if (cardNumInput.current) {
        cardNumInput.current.selectionStart = cursorPosition.current
        cardNumInput.current.selectionEnd = cursorPosition.current
      }
    }
  }, [bankNumber])
  useEffect(() => {
    if (
      expireTime[expireTime.length - 2] !== '/' ||
      cursorExpirPosition.current !== expireNumInput.current.selectionEnd - 1
    ) {
      if (expireNumInput.current) {
        expireNumInput.current.selectionStart = cursorExpirPosition.current
        expireNumInput.current.selectionEnd = cursorExpirPosition.current
      }
    }
  }, [expireTime])
  // 切换时候初始化页面
  useEffect(() => {
    initPage()
  }, [tabChanged])
  useEffect(() => {
    payOptions.forEach((item, i) => {
      sendBlockPb('pay_type_select', {
        bstp: 56,
        rpage: 'cashier_popup',
        tjPb: {
          position: i,
          cashier_type: 'popup',
          abtest: pbInfo.abtest,
          fc: pbInfo.fc,
          fv: pbInfo.fv,
          v_pid: pbInfo.v_pid,
          v_prod: pbInfo.v_prod,
          pay_type: item.payType
        }
      })
    })
  }, [payOptions])
  const initPage = () => {
    setCardType('')
    setBankNumber('')
    setExpireTime('')
    setCvvInput('')
    setMobile('')
    setMobileErr('')
    setValidRes({
      cardNum: true,
      expiryMonth: true,
      expiryYear: true,
      securityCode: true,
      expireTime: true
    })
  }
  const clickPayType = (item) => {
    setSelectPayType(item.payType)
    // setSelectPayWith(index)
    changePW(item.payType)
    getPayWith(item)
  }

  function getCardIssuer(cardNum) {
    // JCB	3528-3589	16
    // 万事达卡 MASTERCARD	51-55	16
    // Visa	4	16
    if (cardNum.length <= 16) {
      if (Number(cardNum[0]) === 4) {
        setCardType('VISA')
        return 'VISA'
      }
      if (cardNum.match(/^5[1-5]/)) {
        setCardType('MASTERCARD')
        return 'MASTERCARD'
      }
      const font = Number(cardNum.slice(0, 4))
      if (font >= 3528 && font <= 3589) {
        setCardType('JCB')
        return 'JCB'
      }
    }
    setCardType('')
    return ''
  }
  const validFunc = () => {
    return {
      cardNum: (num) => {
        return (
          num.replace(/\s/g, '').length >= 12 &&
          !!getCardIssuer(num.replace(/\s/g, ''))
        )
      },
      // eslint-disable-next-line
      expiryMonth: (expiryMonth) =>
        Number(expiryMonth) && Number(expiryMonth) <= 12,
      expiryYear: (expiryYear) => {
        if (!expiryYear || expiryYear.length > 2) return false
        return Number('20' + expiryYear) >= new Date().getFullYear()
      },
      securityCode: (securityCode) => securityCode.length >= 3
    }
  }
  const checkInputValid = (validKey, validValue) => {
    let _validRes = { ...validRes }
    _validRes[validKey] = validValue
    setValidRes((prevalidRes) => {
      return {
        ...prevalidRes,
        [validKey]: validValue
      }
    })
  }
  // 银行卡输入验证
  const bankNumberInputChange = (e) => {
    const value = e.target.value
    const inputPosition = e.target.selectionEnd
    cursorPosition.current = inputPosition
    if (value.match(/^[\d\s]*$/) && value.replace(/\s/g, '').length <= 19) {
      let num = value
      // 处理在空格处删除
      if (
        value.length === bankNumber.length - 1 &&
        bankNumber[inputPosition] === ' '
      ) {
        num =
          value.slice(0, inputPosition - 1) +
          value.slice(inputPosition, value.length)
        cursorPosition.current = inputPosition - 1
      }
      // 处理在空格处添加
      if (
        value.length === bankNumber.length + 1 &&
        (inputPosition - 1) % 4 === 0
      ) {
        cursorPosition.current = inputPosition + 1
      }
      num = num.replace(/\s/g, '')
      if (!validRes.cardNum) {
        checkInputValid('cardNum', validFunc().cardNum(value))
      }
      setBankNumber(num.replace(/(\d{4})/g, '$1 ').trim())
    }

    // setBankNumber(codeStr)
    getCardIssuer(value.replace(/\s/g, ''))
  }

  // 有效期输入
  const expireInputChange = (e) => {
    let value = e.target.value
    const inputPosition = e.target.selectionEnd
    cursorExpirPosition.current = inputPosition
    value = value.replace(/[^0-9]/g, '').replace(/([0-9]{2})(?=[^$])/g, '$1/')
    if (value.length <= 5) {
      let num = value
      // 处理在/处删除
      if (
        value.length === expireTime.length &&
        expireTime[inputPosition] === '/'
      ) {
        num =
          value.slice(0, inputPosition - 1) +
          value.slice(inputPosition, value.length)
        cursorExpirPosition.current = inputPosition - 1
      }
      // 处理在/处添加
      if (
        value.length === expireTime.length + 1 &&
        (inputPosition - 1) % 2 === 0
      ) {
        cursorExpirPosition.current = inputPosition + 1
      }
      num = num.replace(/\//g, '')
      let timeArr = value.split('/')
      if (!validRes.expireTime) {
        checkInputValid(
          'expiryTime',
          validFunc().expiryMonth(timeArr[0]) &
            validFunc().expiryYear(timeArr[1])
        )
      }
      setMonthErr(!validFunc().expiryMonth(timeArr[0]))
      setExpireTime(
        num
          .replace(/[^0-9]/g, '')
          .replace(/([0-9]{2})(?=[^$])/g, '$1/')
          .trim()
      )
    }
  }
  const expireInputBlur = (e) => {
    let value = e.target.value
    if (value.length <= 5) {
      value = value.replace(/[^0-9]/g, '').replace(/([0-9]{2})(?=[^$])/g, '$1/')
      let timeArr = value.split('/')

      checkInputValid(
        'expireTime',
        validFunc().expiryMonth(timeArr[0]) & validFunc().expiryYear(timeArr[1])
      )
      setMonthErr(!validFunc().expiryMonth(timeArr[0]))
    }
  }
  // cvv输入
  const cvvInputChange = (e) => {
    const value = e.target.value
    if (value.match(/^[\d\s]*$/) && value.replace(/\s/g, '').length <= 3) {
      if (!validRes.securityCode) {
        checkInputValid('securityCode', validFunc().securityCode(value))
      }
      setCvvInput(value.replace(/\s/g, ''))
    }
  }

  const checkMobile = () => {
    const number = mobile.replace(/\s/g, '')
    if (number[0] !== '8' && number.slice(0, 2) !== '08') {
      setMobileErr(true)
      return true
    }
    if (number[0] === '8' && number.length < 9) {
      setMobileErr(true)
      return true
    }
    if (number.slice(0, 2) === '08' && number.length < 10) {
      setMobileErr(true)
      return true
    }
  }
  useImperativeHandle(ref, () => ({
    getInputInfo: (type) => {
      let timeArr = expireTime.split('/')
      if (type === 10021) {
        checkMobile()
      }
      if (type === 10010 || type === 10009) {
        if (selectCardIndex < 0) {
          checkInputValid('cardNum', validFunc().cardNum(bankNumber))
          // let timeArr = expireTime.split('/')
          checkInputValid(
            'expireTime',
            !!(
              validFunc().expiryMonth(timeArr[0]) &
              validFunc().expiryYear(timeArr[1])
            )
          )
          checkInputValid('securityCode', validFunc().securityCode(cvvInput))
        }
      }

      // 收集用户输入的信息
      return {
        cardInfo: {
          bankNumber,
          expireTime,
          cvvInput,
          cardType: cardtype
        },
        boundCardInfo: choseBoundItem,
        hasCard: !!choseBoundItem,
        cardNoErr:
          validFunc().expiryMonth(timeArr[0]) &
          validFunc().expiryYear(timeArr[1]) &
          validFunc().securityCode(cvvInput) &
          validFunc().cardNum(bankNumber),
        phoneNumber: mobile,
        phoneNoErr: !checkMobile()
      }
    }
  }))
  // 选中银行卡
  const boundCardClk = (cardItem, cardIndex) => {
    if (cardItem) {
      setBoundCard(true)
      setCardIndex(cardIndex)
      setChoseBoundItem(cardItem)
    } else {
      setCardIndex(-1)
      setBoundCard(false)
      setChoseBoundItem('')
    }
  }

  // 点击登录
  const loginBtnClk = () => {
    startLogin && startLogin()
  }
  // 关闭登录提示
  const closeLoginTip = () => {
    setShowLogin(false)
  }
  return (
    <PayWithWrapper>
      <p className="pay-with-title">{vipLangPkg.PCW_VIP_1645428190068_151}</p>
      {dopayError ? (
        <div className="pay-error-outer">
          <div className="pay-error-wrapper">
            <i className="error-icon"></i>
            <p className="pay-error-tip">{dopayError}</p>
          </div>
        </div>
      ) : (
        ''
      )}
      <div className="pay-with-slider-wrapper">
        <Slider
          pageNum={payOptions.length / 3}
          itemCount={payOptions.length}
          slideToPage={selectPayTypeIndex}
          setNotabClick={setNotabClick}
          tabClick={tabClick}
          slideType="payWith"
        >
          <ul className="pay-with-list">
            {payOptions.map((item, index) => {
              return (
                <li
                  className={`pay-list-item ${
                    item.payType === selectPayType ? 'pay-list-foucs' : ''
                  }`}
                  key={item.name + index}
                  onClick={() => clickPayType(item, index)}
                  rseat={`${selectPayTypeIndex}:${index}`}
                  data-pb={`rpage=cashier_popup&block=pay_type_select&${qs.stringify(
                    { ...pbInfo, position: index, pay_type: item.payType }
                  )}`}
                >
                  <img
                    src={removeProtocol(item.iconUrl)}
                    alt=""
                    className="pay-item-img"
                  />
                  <div className="pay-with-detail">
                    {item.subPromotion ? (
                      <p
                        className="pay-item-discount"
                        title={item.subPromotion}
                      >
                        {item.subPromotion}
                      </p>
                    ) : (
                      ''
                    )}
                    <p className="pay-item-title">{item.name}</p>
                    {item.promotion ? (
                      <p className="pay-item-desc">{item.promotion}</p>
                    ) : (
                      ''
                    )}
                  </div>
                </li>
              )
            })}
          </ul>
        </Slider>
      </div>
      {!isLogin() && showLogin ? (
        <div className="bank-login-wrapper">
          <p className="bank-login-tip">
            {cashierLangPkg.p_mastercard_login_notice}
            <span
              className="bank-login-btn"
              onClick={loginBtnClk}
              rseat="click"
              data-pb={`rpage=cashier_popup&block=bank_login&cashier_type=&${qs.stringify(
                { ...pbInfo }
              )}`}
            >
              {' '}
              {cashierLangPkg.p_mastercard_login_button}
              {' >'}
            </span>
          </p>
          <div className="bank-login-close" onClick={closeLoginTip}>
            <LoginCloseIcon />
          </div>
        </div>
      ) : (
        ''
      )}

      <section className="pay-with-content">
        {selectPayType === 10010 || selectPayType === 10009 ? (
          <>
            <div className="pay-with-detail bank-detail">
              <>
                {userCards && userCards.length > 0 ? (
                  <div className="bound-card-wrapper">
                    <section className="bound-card-list">
                      {userCards &&
                        userCards.map((cardItem, cardIndex) => {
                          return (
                            <label
                              className="bound-card-item"
                              onClick={() => boundCardClk(cardItem, cardIndex)}
                              for={`card${cardIndex}`}
                              key={cardIndex}
                            >
                              <input
                                type="radio"
                                name="radio"
                                id={`card${cardIndex}`}
                                className="card-item-radio"
                                checked={selectCardIndex === cardIndex}
                              />
                              <div className="item-name">
                                <img
                                  src={iconObj[cardItem.cardIssuer]}
                                  alt=""
                                  className="card-item-icon"
                                />
                                <p className="card-item-name">
                                  {iconName[cardItem.cardIssuer]} •••• •••• ••••{' '}
                                  {cardItem.accountPanSuffix}
                                </p>
                              </div>
                            </label>
                          )
                        })}

                      <label
                        className="bound-card-item"
                        for="radio-next"
                        onClick={() => boundCardClk()}
                      >
                        <input
                          type="radio"
                          name="radio"
                          id="radio-next"
                          className="card-item-radio"
                          checked={selectCardIndex === -1}
                        />
                        <div className="item-name">
                          <p className="card-item-name">
                            {cashierLangPkg.p_master_card_add}
                          </p>
                        </div>
                      </label>
                    </section>
                  </div>
                ) : (
                  ''
                )}
                {boundCard ? (
                  ''
                ) : (
                  <>
                    {!userCards || userCards.length < 1 ? (
                      <p className="bankpay-title">
                        {vipLangPkg.pcashier_bank_title}
                      </p>
                    ) : (
                      ''
                    )}
                    <div
                      className={`banknumber-input-wrapper ${
                        validRes.cardNum ? '' : 'has-error'
                      }`}
                    >
                      <div className="banknumber-input-label">
                        <input
                          type="type"
                          className="banknumber-input-input"
                          autoComplete="on"
                          placeholder=" "
                          value={bankNumber || ''}
                          onChange={(e) => {
                            bankNumberInputChange(e)
                          }}
                          onBlur={(e) => {
                            checkInputValid(
                              'cardNum',
                              validFunc().cardNum(e.target.value)
                            )
                          }}
                          ref={cardNumInput}
                        />
                        <span className="banknumber-input-desc">
                          {vipLangPkg.PCW_VIP_1721901800476_511 ||
                            i18n.card_tip}
                        </span>
                        <div className="bank-icon-wrapper">
                          {!cardtype ? (
                            <>
                              <img
                                src={iconObj.MASTERCARD}
                                alt=""
                                className="bank-icon"
                              />
                              <img
                                src={iconObj.VISA}
                                alt=""
                                className="bank-icon"
                              />
                              <img
                                src={iconObj.JCB}
                                alt=""
                                className="bank-icon"
                              />
                            </>
                          ) : (
                            <img
                              src={iconObj[cardtype]}
                              alt=""
                              className="bank-icon"
                            />
                          )}
                        </div>
                      </div>
                      <div className="bankcard-err">
                        {!(bankNumber.replace(/\s/g, '').length >= 12)
                          ? i18n.card_error
                          : i18n.card_issuer_error}
                      </div>
                    </div>
                    {/* 有效期 */}
                    <div
                      className={`banknumber-input-wrapper ${
                        validRes.expireTime ? '' : 'has-error'
                      }`}
                    >
                      <div className="banknumber-input-label">
                        <input type="text" style={{ display: 'none' }} />

                        <input
                          type="password"
                          autoComplete="new-password"
                          style={{ display: 'none' }}
                        />
                        <input
                          type="type"
                          className="banknumber-input-input"
                          autoComplete="on"
                          placeholder=" "
                          value={expireTime || ''}
                          onChange={(e) => {
                            expireInputChange(e)
                          }}
                          onBlur={(e) => {
                            expireInputBlur(e)
                          }}
                          ref={expireNumInput}
                        />
                        <span className="banknumber-input-desc">
                          {vipLangPkg.PCW_VIP_1648807191771_675}
                        </span>
                      </div>
                      <div className="other-err">
                        {monthErr
                          ? i18n.valid_date_month_error
                          : i18n.valid_date_year_error}
                      </div>
                    </div>
                    {/* 安全码 */}
                    <div
                      className={`banknumber-input-wrapper cvv-input ${
                        validRes.securityCode ? '' : 'has-error'
                      }`}
                    >
                      <div className="banknumber-input-label">
                        <input
                          v-if="isAutocomplete === 'off'"
                          type="text"
                          style={{ display: 'none' }}
                        />

                        <input
                          v-if="isAutocomplete === 'off'"
                          type="password"
                          autoComplete="new-password"
                          style={{ display: 'none' }}
                        />
                        <input
                          type="type"
                          className="banknumber-input-input"
                          autoComplete="on"
                          placeholder=" "
                          value={cvvInput || ''}
                          onChange={(e) => {
                            cvvInputChange(e)
                          }}
                          onBlur={(e) => {
                            checkInputValid(
                              'securityCode',
                              validFunc().securityCode(e.target.value)
                            )
                          }}
                        />
                        <span className="banknumber-input-desc">
                          {i18n.cvc}
                        </span>
                        <div className="cvv-tip-wrapper">
                          <img
                            src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAKKADAAQAAAABAAAAKAAAAAB65masAAAETElEQVRYCc1ZOU9bQRAeG3NE4mxAIEEBRiBEgYgQLSWiwCQCQhdqFAqS1Dl6p4tSIqWgiAyBtIAQfwDRQMchEIdMwykZcTnft3m7sXk49trPhJUeM7s7x+eZvfFJluXk5KQpHo+H8D33+Xx1MFMHnlRQPwA5QP0A/Aq+X5WVlZvssy0+G4Xz8/Pqm5ubN3D8AnrtNrqQXQPQ2UAg8LWsrOwoU92MAAJQKSL2DvQ9DJdmajyF3AWAhhHRL6AXKWRMc1qAx8fHLyH9DeBqjJYHDMBFYWasqqrq57/M+VN1ApAP4D6if9prcPTp2JymD/pKhePBDig8Q0q/gw6lUvSyHdGMIOWvQWP37boA8tcA3I/HAqcBOSBfgcZ1G6krxQD34bHBEQh90jf5xJIUQWdCcMwltScq5JN3ojeYOHEMEIDiUrIB6ulstf1BABnFeAyCqiUooA0AHNc5a3DQk9XVVdnc3JTT01PBQizV1dXS0NAg3d3dqq59ZEKJgVgg+5nyKoLcIa6vr7kVWS3CKysrMjc3J9ClLVdBJGRwcFCCwaCrL03DRWFhYRN3HDVJsH2NQ8EK3MbGhkQiEQOOkWtublYfjCv/jO7U1JRgbKfB4+ou5ZbKVpVihHXAJZKmYXFx0Uh0dnbKwMCAFBUVqTamenJyUqLRqMRiMVlYWJDh4WEjnwkDTNzvP/jxK5vAtGeipGWgLHt7e6rKNPb39xtwbKyoqJC+vj4tLru7u4a3YNqJzQ9nIQslJYoZpsYV0xoKhaSkpMRlgiB1uby81KwVJbYA/jy30nKER0dH5fb2VgoKCh5U5xjVJYtJolSJzY9oqEOmNmZDU4Hb2dmR5eVlY6qlpcXwNgyxcZJkDfAhZ1wTZ2ZmBLNQdXd1dUlHR8dDopm01THFngFcWlqS+fl547inp0d6e3tN3ZYhNkYw6fRga0TLc9HW4DhpRkZGpLW1VXdnS+Mcg4fZaifqra+vm+rQ0JAX4Hj5OuROwhtYzmV7e1vZKC4u9gScA+iA66AnAGtra5XNtra2lEuPbRSIjfeOt2C+2Crfl7+6upKtrS1pbGxM2lXuy9nUkeJ3PN433d3d/V1VbSzkWdbv9wf92Et5zFrL1Rd3FS7Q+LG5mtL6a8SmTjMI5SzSbHVg0FZICS4cDqtjFQ+rExMTnIGJItY8MVFJnQf5HAE+7S0/lZf9/X1z5js6OhJ+OZYLB9MfgDy5AnE4W6P19fXqiE99HgxqaqxvDkmuiUW/35g8IMU5X5rOzs6kvLw8yZltBeCSLk0qxTSCDqZ4DDTrrc8DcPRNDGa4GYAE6dxH1W2K9f9QPifeienfpFiDQaqf9tMHU4z1hw85EQ0635S+HJ+u4ZWUYg0ECjEo8CHnEz6XkpbLldI2fTi+XC9btO9K8X2nznvNf3vATAuQgJ0l6Gk+ASdG1HlEHwdgXvRtt8b8PaIngtQ8T0EAmvd/Q/wGwJ7mxlMqKmsAAAAASUVORK5CYII="
                            alt=""
                            className="cvv-tip-img"
                          />
                          <div className="cvv-toast-wrapper">
                            <p className="cvv-toast-tip">{i18n.cvc_tip}</p>
                            <img
                              src="//www.iqiyipic.com/new-vip/icon_pay-cvvCard.png"
                              alt=""
                              className="cvv-toast-img"
                            />
                          </div>
                        </div>
                      </div>
                      <div className="other-err">{i18n.cvc_error}</div>
                    </div>
                  </>
                )}
              </>

              <section className="security-text-wrapper">
                <p className="security-text">
                  {vipLangPkg.PCW_VIP_1693909566473_708}
                </p>
                <img
                  className="dssicon"
                  src="data:image/png;base64,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"
                  alt=""
                />
              </section>
            </div>
          </>
        ) : (
          <>
            <div className="third-pay">
              {vipLangPkg.PCW_VIP_1648809587371_929}
            </div>
          </>
        )}
      </section>
    </PayWithWrapper>
  )
}

export default React.memo(forwardRef(PayWith))
