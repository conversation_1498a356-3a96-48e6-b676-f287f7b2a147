import React, { useCallback, useEffect, useRef, useState } from 'react'
import { SUBMIT_PAY_PHONE } from '@/constants/interfaces'
import $http from '@/kit/fetch'
import { getDevice } from '@/kit/device'
import { sendBlockPb } from '@/utils/pingBack'
import { bankLangPkg } from '../config'
import VipInfoCard from './VipInfoCard'
import { AlertIcon } from '../style/icon'
import StyleWrapper from './style'

const MobileContainer = props => {

  const { selectedPkg, mod, lang, vipLangPkg, resultInfo, setStep, abtest, params } = props
  const [mobile, setMobile] = useState(resultInfo.mobile)
  const [ischange, setIschange] = useState(false)
  const [mobileErr, setMobileErr] = useState(false)
  const [pageErr, setPageErr] = useState('')
  const [isLoading, setLoading] = useState(false)
  const localLangPkg = bankLangPkg[lang] || bankLangPkg['en_us']

  useEffect(() => {
    setMobile(resultInfo.mobile)
    if (!resultInfo.mobile) {
      setIschange(true)
    }
  }, [resultInfo.mobile])
  useEffect(() => {
    sendBlockPb('gopay_getphone', {
      bstp: 56,
      tjPb: {
        abtest: props.abtest,
        fc: props.params.fc || '',
        fv: props.params.fv || ''
      }
    })
  })

  const handleMobileChange = useCallback(e => {
    const value = e.target.value.toString()
    const valNum = value.replace(/\s/g, '')
    if (value.match(/^[\d\s]*$/)) {
      if (valNum[0] === '8' && valNum.length > 12) return
      if (valNum.slice(0, 2) === '08' && valNum.length > 13) return
      setMobileErr(false)
      setMobile(value)
    }
  }, [])
  const checkMobile = useCallback(() => {
    const number = mobile.replace(/\s/g, '')
    if (number[0] !== '8' && number.slice(0, 2) !== '08') {
      setMobileErr(true)
    }
    if (number[0] === '8' && number.length < 9) {
      setMobileErr(true)
    }
    if (number.slice(0, 2) === '08' && number.length < 10) {
      setMobileErr(true)
    }
  }, [mobile])

  const submitData = useCallback(async () => {
    if (mobileErr) return
    let mobileNum = mobile
    if (mobile.slice(0, 2) === '08') {
      mobileNum = mobile.slice(1, mobile.length)
    }
    const params = {
      sign: 'PCW',
      authcookie: 'PCW',
      order_code: resultInfo.order,
      dfp: (window.dfp && window.dfp.tryGetFingerPrint()) || '',
      mobile: mobileNum,
      local_lang: lang
    }
    const newwin = getDevice() === 'pc' ? window.open('about:blank', '_blank') : window
    try {
      setLoading(true)
      const res = await $http(SUBMIT_PAY_PHONE, { params })
      setLoading(false)
      if (res.code === 'A00000') {
        newwin.location.href = res.redirectUrl
        setStep('queryPaymentStep')
      } else {
        getDevice() === 'pc' && newwin.close()
        
        setPageErr(res.msg)
      }
    } catch (err) {
      getDevice() === 'pc' && newwin.close()
      console.log(err)
    }
  }, [mobile, lang, resultInfo.order, mobileErr])

  return (
    <StyleWrapper className="scroll-container">
      <div className="scroll-content">
        <h1>{vipLangPkg.p_gopay_telephone_page_title}</h1>
        <VipInfoCard selectedPkg={selectedPkg} mod={mod} />
        {pageErr && <div className="error"><AlertIcon />{pageErr}</div>}
        <div className="block">
          <div className="paytype">
            <img className="icon" src="//www.iqiyipic.com/lequ/20210722/<EMAIL>" alt="gopay" />
          </div>
          <div className="mobile">
            <div className="mobile-title">
              {vipLangPkg.p_gopay_telephone_page_title}
            </div>
            {ischange ? (
              <div className="mobile-input">
                <label htmlFor="mobile" className="static">
                  +62
                </label>
                <input
                  id="mobile"
                  className={mobileErr ? 'input-error' : ''}
                  placeholder={vipLangPkg.p_gopay_telephone_page_title}
                  value={mobile}
                  autoFocus
                  onBlur={checkMobile}
                  onChange={handleMobileChange}
                />
              </div>
            ) : (
              <div className="mobile-number">
                <span>+62</span>
                <span>{mobile.toString().replace(/(\d+)\d{4}(\d{4})/, '$1****$2')}</span>
                <span
                  className="change-btn"
                  role="button"
                  tabIndex="0"
                  onClick={() => {
                    setIschange(true)
                    setMobile('')
                  }}
                >
                  {vipLangPkg.p_gopay_telephone_page_otherNO}
                </span>
              </div>
            )}
            {mobileErr && (
              <p className="mobile-error">
                {vipLangPkg.p_gopay_telephone_page_checkNO}
              </p>
            )}
          </div>
          <p className="desc">
            {vipLangPkg.p_gopay_telephone_page_description}
          </p>
        </div>
      </div>
      <button className="containue" type="button" onClick={submitData} data-pb={`rpage=gopay_getphone&block=next&abtest=${abtest}&fc=${params.fc || ''}&fv=${params.fv || ''}`} rseat="next">
        {vipLangPkg.p_gopay_telephone_page_payment}
      </button>
      {isLoading ? <div className="loading"><div className="loading-img" />{localLangPkg.loading_text}</div> : null}
    </StyleWrapper>
  )
}

export default MobileContainer
