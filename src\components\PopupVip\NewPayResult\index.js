/**
 * @desc 新版本收银台结果页面
 * <AUTHOR>
 * @time 2022-03-11
 * @feature  GLOBALREQ-6796
 */

import React, { useState, useEffect, useRef } from 'react'
import { connect } from 'react-redux'
import { getCookies } from '@/kit/cookie'
import { sendBlockPb } from '@/utils/pingBack'
import { initLoginWindow } from '@/utils/loginWindow.js'
import { removeProtocol } from '@/kit/url'
import { QUERY_CARD } from '@/constants/interfaces'
import $http from '@/kit/fetch'
import { getUid } from '@/utils/userInfo'
import { getUserInfoAction } from '@/store/reducers/user'
// import { PopUpWindowCloseIcon } from '@/components/svgs'
import ResultCard from '../ResultCard'
import ResultWrapper from './style/index'
import Toast from '../Toast'
const NewPayResult = (props) => {
  const {
    resultInfo,
    vipLangPkg,
    feedbackUrl,
    resultType,
    hide,
    getVipData,
    pbInfoStr,
    pbInfo,
    goBack,
    setResultSuccess,
    userInfo,
    dispatch
  } = props
  const { activated, email } = userInfo
  const { platform, mod, lang, timeZone, vipOrder, payType } = resultInfo
  const [tipType, setTipType] = useState(resultType || 'pending')
  const [notFinishToast, setNotFinishToast] = useState(false)
  const [btnText, setBtnText] = useState(vipLangPkg.PCW_VIP_1649665010687_222)
  const [emailCard, updateMailCard] = useState({})
  const [marketingCard, setMarketingCard] = useState({})
  const [couponCard, setCouponCard] = useState({})
  const [resultOrderInfo, setResultOrderInfo] = useState({})
  // 邮箱展示开关
  const [emailControl, setEmailControl] = useState(false)
  const [onlyEmail, setOnlyEmail] = useState(false)
  // const [payResultInfo, setResultInfo] = useState({})
  // // const [actModal, updateModal] = useState(false)
  // const [actUrl, updateUrl] = useState('')
  // const [actImg, updateImg] = useState('')
  // 当前展示的cards
  const [resultCardList, setCardList] = useState([])
  const timerRef = useRef(null)
  useEffect(() => {
    if (vipOrder) {
      fetchQueryCard('enter')
    }
    return () => {
      clearTimeout(timerRef.current)
    }
  }, [resultInfo])
  useEffect(() => {
    setTipType(tipType)
  }, [resultType])
  useEffect(() => {
    sendBlockPb('redirection', {
      bstp: 56,
      rpage: 'cashier_payment_completed',
      tjPb: {
        ...pbInfo
      }
    })
    // fetchQueryCard()
  }, [])

  const fetchQueryCard = async (from) => {
    if ((from === 'click' || !from) && timerRef.current) {
      clearTimeout(timerRef.current)
    }
    try {
      const cardResult =
        (await $http(QUERY_CARD, {
          method: 'GET',
          credentials: true,
          params: {
            requestId: getCookies('QC005') + '_' + new Date().getTime(),
            platform,
            clientChannel: 'default',
            app_lm: mod,
            lang,
            timeZone,
            timeStamp: new Date().getTime(),
            clientVersion: '1.0.0',
            qyid: getCookies('QC005'),
            orderCode: vipOrder,
            bizSource: 'PCW'
          }
        })) || {}
      // 添加线上日志
      const addloggers = {
        popupPayResult: {
          time: new Date(),
          uid: getUid(),
          cardResult
        }
      }

      const loggers = localStorage.getItem('QiyiPlayerLogger')
      window.localStorage.setItem(
        'QiyiPlayerLogger',
        loggers + JSON.stringify(addloggers)
      )

      if (cardResult.code === 'B01002') {
        timerRef.current = setTimeout(fetchQueryCard, 5000)
        setTipType('pending')
        if (from === 'click') {
          setNotFinishToast(true)
          setTimeout(() => {
            setNotFinishToast(false)
          }, 2000)
        }
      } else if (cardResult.code === 'A00000') {
        let data = cardResult.data

        //处理用户邮箱
        const {
          orderInfo = {},
          marketingCard = {},
          couponCard = {},
          mailCard = {},
          payResultCard = {}
        } = data
        setResultOrderInfo(orderInfo || {})
        setMarketingCard(marketingCard || {})
        setCouponCard(couponCard || {})
        const _CardObj = { marketingCard, couponCard, mailCard, payResultCard }
        const _cardList = []
        Object.keys(_CardObj).map((item) => {
          if (_CardObj[item]) {
            _cardList.push(_CardObj[item])
            return (_CardObj[item].type = item)
          }
        })
        setCardList([..._cardList.sort((a, b) => a.sort - b.sort)])
        // setResultInfo(payResultCard)
        const { status } = orderInfo // 1--成功  3--取消  5--退款中  6--已退款  7--待支付
        const { guideSwitch = false } = mailCard || {} // 是否引导邮箱的开关
        setEmailControl(guideSwitch || false)
        if (status === 1) {
          // 支付成功
          window.gtag &&
            window.gtag('event', 'in_web_purchase', {
              currency: orderInfo.currencyUnit,
              value: orderInfo.realFee / 100,
              mod: mod || 'intl',
              rpage: 'cashier_popup'
            })
          setTipType('complete')
          setResultSuccess(true)
          clearTimeout(timerRef.current)
          updateMailCard(mailCard)
          if (!email || !activated) {
            setOnlyEmail(true)
            if (!window.sdkPackManager) {
              await initLoginWindow()
            } else if (!window.sdkPackManager.globalLogin) {
              await window.sdkPackManager.initLogin()
            }
            window.sdkPackManager &&
              window.sdkPackManager.globalLogin.on('emailManage', () => {
                dispatch(getUserInfoAction())
                // fetchQueryCard('click')
              })
          } else {
            setOnlyEmail(false)
          }
        } else if (status === 7) {
          timerRef.current = setTimeout(fetchQueryCard, 5000)
          setTipType('pending')
          if (from === 'click') {
            setNotFinishToast(true)
            setTimeout(() => {
              setNotFinishToast(false)
            }, 2000)
          }
        } else {
          setTipType('result-system')
          clearTimeout(timerRef.current)
        }
      } else {
        setTipType('result-system')
        clearTimeout(timerRef.current)
      }
    } catch (error) {
      if (error.message.match('timeout')) {
        setTipType('result-net')
      }
    }
  }
  // const fetchPayResult = async (from) => {
  //   if ((from === 'click' || !from) && timerRef.current) {
  //     clearTimeout(timerRef.current)
  //   }
  //   try {
  //     const resultData =
  //       (await $http(intlVipPayResultInterface, {
  //         method: 'POST',
  //         credentials: true,
  //         // timeout: 5,
  //         params: qs.stringify({
  //           platform,
  //           app_lm: mod,
  //           lang,
  //           timeZone,
  //           version: '1.0',
  //           orderCode: vipOrder
  //         })
  //       })) || {}

  //     // console.log(resultData, '**********query接口返回的结果********')
  //     const { i18nOrderInfo } = resultData.data || {}
  //     // 添加线上日志
  //     const addloggers = {
  //       popupPayResult: {
  //         time: new Date(),
  //         uid: getUid(),
  //         resultData
  //       }
  //     }
  //     const loggers = localStorage.getItem('QiyiPlayerLogger')
  //     window.localStorage.setItem(
  //       'QiyiPlayerLogger',
  //       loggers + JSON.stringify(addloggers)
  //     )
  //     if (
  //       (resultData.code === 'A00000' && i18nOrderInfo.status === 7) ||
  //       resultData.code === 'Q00301'
  //     ) {
  //       timerRef.current = setTimeout(fetchPayResult, 5000)
  //       setTipType('pending')
  //       if (from === 'click') {
  //         setNotFinishToast(true)
  //         setTimeout(() => {
  //           setNotFinishToast(false)
  //         }, 2000)
  //       }
  //     } else if (resultData.code === 'A00000' && i18nOrderInfo.status === 1) {
  //       // 支付成功
  //       setTipType('complete')
  //       setResultSuccess(true)
  //       fetchQueryCard()
  //       clearTimeout(timerRef.current)
  //     } else if (resultData.code === 'Q00332') {
  //       setTipType('result-system')
  //       clearTimeout(timerRef.current)
  //     } else {
  //       setTipType('result-system')
  //       clearTimeout(timerRef.current)
  //     }
  //   } catch (error) {
  //     if (error.message.match('timeout')) {
  //       setTipType('result-net')
  //     }
  //   }
  // }
  const manualQuery = () => {
    fetchQueryCard('click')
    // fetchPayResult('click')
  }
  const retryData = () => {
    getVipData({}, '')
  }
  //  绑定邮箱流程
  const bindEmail = () => {
    if (!email) {
      window.sdkPackManager.globalLogin.openLoginRegWindow({
        type: 2
      })
    } else {
      window.sdkPackManager.globalLogin.openLoginRegWindow({
        type: 1
      })
    }
  }
  return (
    <ResultWrapper>
      <div className={`result-content-wrapper`}>
        <div className="new-vip-result-header">
          {tipType !== 'complete' ? (
            <div
              role="button"
              tabIndex="0"
              className="back"
              data-pb={`rpage=redirection&block=extend_info&${pbInfoStr}`}
              rseat="back"
              onClick={() => goBack()}
            ></div>
          ) : (
            ''
          )}
          <div
            className="close"
            onClick={hide}
            rseat="close"
            data-pb={`rpage=redirection&block=extend_info&${pbInfoStr}`}
          ></div>
        </div>

        {tipType === 'pending' ? (
          <section className={`result-content`}>
            <div className="result-pending">
              {/* <div className="result-loading">
                <span></span>
              </div> */}
              <img
                className="result-loading-img"
                src="//www.iqiyipic.com/new-vip/loading.png"
              />
              <section className="result-status-wrapper">
                <p className="result-status-text">
                  {vipLangPkg.PCW_VIP_1645427174920_750}
                </p>
                {payType !== 10010 && payType !== 10009 ? (
                  <p className="result-substatus">
                    {vipLangPkg.pcashier_query_redirect}
                  </p>
                ) : (
                  ''
                )}
                <button className="complete-btn" onClick={manualQuery}>
                  {vipLangPkg.pcashier_mpage_payment_result}
                </button>
              </section>
            </div>
          </section>
        ) : (
          ''
        )}
        {tipType === 'system' ? (
          <section className={`result-content neterr-content`}>
            <div className="result-error">
              <img
                className="result-error-img"
                src="//www.iqiyipic.com/new-vip/system-error.png"
                alt=""
              />
              <p className="result-status-text">
                {vipLangPkg.PCW_VIP_1649665310077_113}
              </p>
              <p className="result-substatus">
                {vipLangPkg.pcashier_error_errorOccur}
              </p>
              <button className="complete-btn" onClick={retryData}>
                {vipLangPkg.pcashier_error_retry}
              </button>
            </div>
          </section>
        ) : (
          ''
        )}
        {tipType === 'neterr' ? (
          <section className={`result-content neterr-content`}>
            <div className="result-error">
              <img
                className="result-error-img"
                src="//www.iqiyipic.com/new-vip/network-error.png"
                alt=""
              />
              <p className="result-status-text">
                {vipLangPkg.pcashier_error_network}
              </p>
              <button className="complete-btn" onClick={retryData}>
                {vipLangPkg.pcashier_error_retry}
              </button>
            </div>
          </section>
        ) : (
          ''
        )}
        {tipType === 'result-net' ? (
          <section className={`result-content`}>
            <div className="result-error">
              <img
                className="result-error-img"
                src="//www.iqiyipic.com/new-vip/network-error.png"
                alt=""
              />
              <p className="result-status-text">
                {vipLangPkg.PCW_VIP_1649665310077_113}
              </p>
              <p className="result-substatus">
                {vipLangPkg.pcashier_error_network}
              </p>
              <button className="complete-btn" onClick={fetchQueryCard}>
                {vipLangPkg.pcashier_error_retry}
              </button>
            </div>
          </section>
        ) : (
          ''
        )}
        {tipType === 'result-system' ? (
          <section className={`result-content`}>
            <div className="result-error">
              <img
                className="result-error-img"
                src="//www.iqiyipic.com/new-vip/system-error.png"
                alt=""
              />
              <p className="result-status-text">
                {vipLangPkg.PCW_VIP_1649665310077_113}
              </p>
              <p className="result-substatus">
                {vipLangPkg.pcashier_error_errorOccur}
              </p>
              <button className="complete-btn" onClick={fetchQueryCard}>
                {vipLangPkg.pcashier_error_retry}
              </button>
            </div>
          </section>
        ) : (
          ''
        )}
        {tipType === 'complete' ? (
          <section className={`result-content result-content-complete`}>
            <div className="result-complete">
              {resultCardList.map((item, index) => {
                if (item.type === 'payResultCard') {
                  return (
                    <>
                      <img
                        className="result-success-img"
                        src={removeProtocol(
                          vipLangPkg.PCW_VIP_1677123019567_915
                        )}
                        alt=""
                      />
                      <p className="result-status-text">
                        {vipLangPkg.reselling_title}
                      </p>
                    </>
                  )
                }
                if (item.type === 'mailCard') {
                  if (emailControl) {
                    if (email && activated) {
                      sendBlockPb('email', {
                        rpage: 'cashier_payment_completed'
                      })
                      return (
                        <>
                          <p className="result-substatus" key={index}>
                            {emailCard.title}
                            {emailCard.desc1 &&
                              emailCard.desc1.replace('${email}', email)}
                          </p>
                        </>
                      )
                    } else {
                      if (!email) {
                        sendBlockPb('bind_email', {
                          rpage: 'cashier_payment_completed'
                        })
                        return (
                          <>
                            <p className="result-substatus" key={index}>
                              {emailCard.title}
                              {emailCard.desc2}
                            </p>
                            <div className="link-email-wrapper">
                              <img
                                src="//www.iqiyipic.com/vip-result/icon_link.png"
                                alt=""
                                className="link-email-icon"
                              />
                            </div>
                          </>
                        )
                      } else if (!activated) {
                        sendBlockPb('activate_email', {
                          rpage: 'cashier_payment_completed'
                        })
                        return (
                          <>
                            <p className="result-substatus" key={index}>
                              {emailCard.title}
                              {emailCard.desc3}
                            </p>
                            <p className="activate-email-title">{email}</p>
                          </>
                        )
                      }
                    }
                  } else {
                    if (email && activated) {
                      return (
                        <>
                          <p className="result-substatus" key={index}>
                            {emailCard.title}
                            {emailCard.desc1 &&
                              emailCard.desc1.replace('${email}', email)}
                          </p>
                        </>
                      )
                    } else {
                      if (!email) {
                        return (
                          <p className="result-substatus" key={index}>
                            {emailCard.title}
                            {emailCard.desc3}
                          </p>
                        )
                      } else if (!activated) {
                        return (
                          <p className="result-substatus" key={index}>
                            {emailCard.title}
                            {emailCard.desc1 &&
                              emailCard.desc1.replace('${email}', email)}
                          </p>
                        )
                      }
                    }
                  }
                } else {
                  if (!onlyEmail) {
                    return (
                      <ResultCard
                        cardData={item}
                        orderInfo={resultOrderInfo}
                        key={index}
                      ></ResultCard>
                    )
                  }
                }
              })}
              {resultCardList.length < 1 ? (
                <>
                  <img
                    className="result-success-img"
                    src={
                      removeProtocol(vipLangPkg.PCW_VIP_1677123019567_915) ||
                      '//www.iqiyipic.com/vip-result/icon_success.png'
                    }
                    alt=""
                  />
                  <p className="result-status-text">
                    {vipLangPkg.reselling_title}
                  </p>
                </>
              ) : (
                ''
              )}
              {emailControl ? (
                email && activated ? (
                  !marketingCard.tempType &&
                  !(couponCard.details && couponCard.details.length) ? (
                    <button
                      className="complete-btn"
                      onClick={hide}
                      rseat="finish"
                      data-pb={`rpage=redirection&block=redirection&${pbInfoStr}`}
                    >
                      {btnText}
                    </button>
                  ) : (
                    ''
                  )
                ) : (
                  <button
                    className="complete-btn"
                    onClick={bindEmail}
                    rseat={`${!email ? 'bind' : 'activate'}`}
                    data-pb={`rpage=cashier_payment_completed&block=${
                      !email ? 'bind' : 'activate'
                    }&${pbInfoStr}`}
                  >
                    {!email
                      ? emailCard.textButtonTips
                      : emailCard.activationButtonTips}
                  </button>
                )
              ) : (!marketingCard.tempType &&
                  !(couponCard.details && couponCard.details.length)) ||
                onlyEmail ? (
                <button
                  className="complete-btn"
                  onClick={hide}
                  rseat="finish"
                  data-pb={`rpage=redirection&block=redirection&${pbInfoStr}`}
                >
                  {btnText}
                </button>
              ) : (
                ''
              )}

              {/* <button
                className="complete-btn"
                onClick={hide}
                rseat="finish"
                data-pb={`rpage=redirection&block=redirection&${pbInfoStr}`}
              >
                {btnText}
              </button> */}
            </div>
          </section>
        ) : (
          ''
        )}
        {tipType === 'pending' ? (
          <p className="result-tip">
            {vipLangPkg.pcashier_query_note}{' '}
            <a
              className="other-question"
              target="_blank"
              rel="noopener noreferrer"
              href={feedbackUrl}
            >
              {vipLangPkg.pcashier_query_gotProblem}
            </a>
          </p>
        ) : (
          ''
        )}
        {notFinishToast ? <Toast msg={vipLangPkg.unpay_toast} /> : ''}
        {/* {actModal ? (
          <div className="act-modal-wrapper">
            <div className="act-modal-content">
              <div
                className="act-modal-close"
                onClick={() => updateModal(false)}
              >
                <PopUpWindowCloseIcon></PopUpWindowCloseIcon>
              </div>
              <a href={actUrl || '//www.iq.com'} className="act-modal-link">
                <img
                  src={removeProtocol(actImg)}
                  alt=""
                  className="act-modal-img"
                />
              </a>
            </div>
          </div>
        ) : (
          ''
        )} */}
      </div>
    </ResultWrapper>
  )
}

const mapStateToProps = (state) => ({
  userInfo: state['user']['userInfo']
})

export default connect(mapStateToProps)(NewPayResult)
