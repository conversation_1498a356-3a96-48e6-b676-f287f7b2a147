import React, { Component } from 'react'
import NumberAniWrapper from './style/number'

class NumberAnimation extends Component {
  constructor(props) {
    super(props)
    this.lineHeight = 33
    this.defaultSize = 16
    this.array = [1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1]
  }
  render() {
    const { startValue = 90, endValue = 60, startAni } = this.props
    let distance = (startValue - endValue) * 100

    let style = {
      transform: `translateY(${startAni ? 0 : -1 * 10 * this.lineHeight}px)`
    }

    return (
      <NumberAniWrapper>
        <div className="number-animation-wrap">
          {/* <div className="number-animation-wrap-hidden">0</div> */}
          <div className="number-animation" style={style}>
            {this.array.map((item, index) => {
              return (
                <div className="number" key={index}>
                  {Number(
                    (
                      (startValue * 100 - (distance / 10) * (10 - index)) /
                      100
                    ).toFixed(2)
                  ).toString()}
                </div>
              )
            })}
            {/* <div className="number">0</div>
            <div className="number">1</div>
            <div className="number">2</div>
            <div className="number">3</div>
            <div className="number">4</div>
            <div className="number">5</div>
            <div className="number">6</div>
            <div className="number">7</div>
            <div className="number">8</div>
            <div className="number">9</div>
            <div className="number">0</div>
            <div className="number">1</div>
            <div className="number">2</div>
            <div className="number">3</div>
            <div className="number">4</div>
            <div className="number">5</div>
            <div className="number">6</div>
            <div className="number">7</div>
            <div className="number">8</div>
            <div className="number">9</div>
            <div className="number">0</div>
            <div className="number">1</div>
            <div className="number">2</div>
            <div className="number">3</div>
            <div className="number">4</div>
            <div className="number">5</div>
            <div className="number">6</div>
            <div className="number">7</div>
            <div className="number">8</div>
            <div className="number">9</div> */}
          </div>
        </div>
      </NumberAniWrapper>
    )
  }
}

export default NumberAnimation
