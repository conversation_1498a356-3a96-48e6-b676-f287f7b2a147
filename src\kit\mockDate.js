const RealDate = Date
let now = null

class MockDate {
  constructor(y, m, d, h, M, s, ms) {
    // super();

    let date

    switch (arguments.length) {
      case 0:
        if (now !== null) {
          date = new RealDate(now.valueOf())
        } else {
          date = new RealDate()
        }
        break

      case 1:
        date = new RealDate(y)
        break

      default:
        d = typeof d === 'undefined' ? 1 : d
        h = h || 0
        M = M || 0
        s = s || 0
        ms = ms || 0
        date = new RealDate(y, m, d, h, M, s, ms)
        break
    }

    return date
  }
}

// MockDate.prototype = RealDate.prototype;
Object.setPrototypeOf(MockDate.prototype, Date.prototype)

MockDate.UTC = RealDate.UTC

MockDate.now = function() {
  return new MockDate().valueOf()
}

MockDate.parse = function(dateString) {
  return RealDate.parse(dateString)
}

MockDate.toString = function() {
  return RealDate.toString()
}

export function set(date) {
  const dateObj = new Date(date.valueOf())
  // eslint-disable-next-line no-restricted-globals
  if (isNaN(dateObj.getTime())) {
    throw new TypeError('mockdate: The time set is an invalid date: ' + date)
  }

  // @ts-ignore
  // eslint-disable-next-line no-global-assign
  window.Date = MockDate
  global.Date = MockDate

  if (date.valueOf) {
    date = date.valueOf()
  }

  now = dateObj.valueOf()
}
export function reset() {
  // eslint-disable-next-line no-global-assign
  Date = RealDate
}

export default {
  set,
  reset
}
