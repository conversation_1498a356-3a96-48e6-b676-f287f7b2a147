import styled from 'styled-components'

const Style = styled.div`
  .title {
    width: 280px;
    height: 28px;
    background: #E6E6E6;
    border-radius: 2px;
    margin: 6px auto 12px;
  }
  .tip {
    width: 560px;
    height: 20px;
    background: #E6E6E6;
    border-radius: 2px;
    margin: 0 auto 16px;
  }
  .content {
    width: 808px;
    height: 266px;
    background: #ECECEC;
    border-radius: 6px;
    margin: 16px auto;
  }
  .button {
    width: 504px;
    height: 44px;
    background: #E6E6E6;
    border-radius: 4px;
    margin: 16px auto 40px;
  }
  @media screen and (max-width: 1023px) {
    .tip {
      width: 428px;
    }
    .content {
      width: 532px;
    }
    .button {
      width: 532px;
    }
  }
`
export default Style