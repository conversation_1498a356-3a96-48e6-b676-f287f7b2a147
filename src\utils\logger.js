/**
 * @file 日志库
 */
function makeLogger() {
  const defaultUrl = '//msg-intl.qy.net/act'
  const serverLogimgList = {}

  const Logger = options => {
    this.moduleName = options.module
  }

  // 静态方法：服务器日志
  Logger.server = (param, options) => {
    let url = defaultUrl
    if (typeof options === 'string') {
      url = options
      options = null
    }
    options = options || {
      cache: false
    }
    if (options.url) {
      url = options.url
    }
    if (param) {
      let img = new Image()
      const key = 'slog_' + Math.floor(Math.random() * 2147483648).toString(36)
      serverLogimgList[key] = img
      const imgFun = () => {
        img.onload = null
        img.onerror = null
        img.onabort = null
        serverLogimgList[key] = null
        delete serverLogimgList[key]
        img = null
      }
      img.onload = imgFun
      img.onerror = imgFun
      img.onabort = imgFun
      const params = []
      if (options.cache === false) {
        param._ = Math.round(Math.random() * 2147483647)
      }
      for (const pname in param) {
        params.push(pname + '=' + encodeURIComponent(param[pname]))
      }
      img.src = url + '?' + params.join('&')
    }
  }
  return Logger
}

const Logger = makeLogger()
export default Logger
