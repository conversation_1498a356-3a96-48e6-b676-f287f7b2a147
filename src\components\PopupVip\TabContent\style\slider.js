import styled from 'styled-components'

const Style = styled.div`
  position: relative;
  margin: 0 40px;
  /* width: 100%; */
  .slider {
    overflow: hidden;
    position: relative;
    padding-top: 10px;
  }
  .slider-content {
    transition: 0.5s;
  }
  .no-transition {
    transition: 0s;
  }
  .left,
  .right {
    width: 32px;
    height: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    position: absolute;
    top: calc(50% + 5px);
    transform: translateY(-50%);
    background: #bb8b51;
    /* box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.16); */
    z-index: 1;
    cursor: pointer;
  }
  .right {
    right: -32px;
    background: url('//www.iqiyipic.com/new-vip/arrow-right.png') no-repeat
      center;
    background-size: cover;
    &:hover {
      background: url('//www.iqiyipic.com/new-vip/arrow-right-hover.png')
        no-repeat center;
      background-size: cover;
    }
  }
  .left {
    left: -32px;
    background: url('//www.iqiyipic.com/new-vip/arrow-left.png') no-repeat
      center;
    background-size: cover;
    &:hover {
      background: url('//www.iqiyipic.com/new-vip/arrow-left-hover.png')
        no-repeat center;
      background-size: cover;
    }
  }
  @media screen and (max-width: 1023px) {
    /* width: 532px; */
  }
`
export default Style
