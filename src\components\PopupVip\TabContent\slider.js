import React, {
  useState,
  useRef,
  useEffect,
  useLayoutEffect,
  useImperativeHandle,
  forwardRef
} from 'react'
import SliderStyle from './style/slider'
import { getViewWidth } from '@/kit/dom'

const Slider = (props, ref) => {
  const {
    pageNum,
    itemCount,
    children,
    pbInfoStr,
    handlePageChange,
    slideToPage,
    setNotabClick,
    tabClick,
    slideType = 'prod'
  } = props
  const slider = useRef(null)
  const content = useRef(null)
  const [page, setPage] = useState(0)
  const [pageNumSlider, setPageNumslider] = useState(pageNum)
  const [sliderCount, setSliderCount] = useState(itemCount)
  // console.log(pageNumSlider, 'itemCount', itemCount, '=======', type)
  const [sliderWidth, setSliderWidth] = useState(
    (slider.current ? slider.current.clientWidth : 0) + 12
  )
  // 滚动到默认位置
  const slideTo = () => {
    let _perPageNum = getViewWidth() < 1024 ? 3 : 4
    if (slideType !== 'prod') _perPageNum = 3
    let _page = Math.floor(slideToPage / _perPageNum)
    setPage(_page)
  }
  useLayoutEffect(() => {
    slideTo()
  }, [slideToPage, pageNum])
  useEffect(() => {
    setPageNumslider(pageNum)
  }, [pageNum])
  useEffect(() => {
    setSliderWidth((slider.current ? slider.current.clientWidth : 0) + 12)
  }, [slider])
  useEffect(() => {
    if (itemCount) {
      window.addEventListener('resize', resizeThrottler, false)
      var resizeTimeout
      function resizeThrottler() {
        // ignore resize events as long as an actualResizeHandler execution is in the queue
        if (!resizeTimeout) {
          resizeTimeout = setTimeout(function () {
            resizeTimeout = null
            actualResizeHandler()

            // The actualResizeHandler will execute at a rate of 15fps
          }, 200)
        }
      }

      function actualResizeHandler() {
        let _perPage = getViewWidth() < 1024 ? 3 : 4
        if (slideType !== 'prod') _perPage = 3
        setPageNumslider(itemCount / _perPage)
        setNotabClick()
        setSliderWidth((slider.current ? slider.current.clientWidth : 0) + 12)
        if (page !== 0 && page >= itemCount / _perPage) {
          setPage(page - 1)
        }
      }
    }
  }, [itemCount, page])
  return (
    <SliderStyle>
      <div
        className="left"
        onClick={() => {
          setNotabClick()
          setPage(page - 1)
          if (handlePageChange) handlePageChange(page - 1)
        }}
        role="button"
        tabIndex="0"
        style={{ display: page ? 'flex' : 'none' }}
        data-pb={`rpage=cashier_popup&block=extend_info&${pbInfoStr}`}
        rseat="previous"
      ></div>
      <div
        className="slider"
        ref={(ref) => {
          slider.current = ref
        }}
      >
        <div
          className={`slider-content ${tabClick ? 'no-transition' : ''}`}
          ref={(ref) => {
            content.current = ref
          }}
          style={{ transform: `translateX(-${page * sliderWidth}px)` }}
        >
          {children}
        </div>
      </div>
      <div
        className="right"
        onClick={() => {
          setNotabClick()
          setPage(page + 1)
          if (handlePageChange) handlePageChange(page + 1)
        }}
        role="button"
        tabIndex="0"
        style={{ display: page + 1 < pageNumSlider ? 'flex' : 'none' }}
        data-pb={`rpage=cashier_popup&block=extend_info&${pbInfoStr}`}
        rseat="next"
      >
        {/* <ArrowRight /> */}
      </div>
    </SliderStyle>
  )
}

export default React.memo(forwardRef(Slider))
