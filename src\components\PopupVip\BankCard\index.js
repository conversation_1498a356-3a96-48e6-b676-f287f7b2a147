import qs from 'qs'
import React from 'react'
import { bankLangPkg } from '../config'
import { getCookies } from '@/kit/cookie'
import { getUid, isLogin } from '@/utils/userInfo'
import { bankDoPayInterface } from '@/constants/interfaces'
import $http from '@/kit/fetch'
import { sendBlockPb } from '@/utils/pingBack'
import { rebuildHttpsUrl } from '@/kit/common'
import { getDevice } from '@/kit/device'
import BankCardWrapper from './style'
import { Popup } from './Popup'

class BankCard extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      cardInfo: {
        cardNum: '',
        expiryMonth: '',
        expiryYear: '',
        securityCode: ''
      },
      validRes: {
        cardNum: true,
        expiryMonth: true,
        expiryYear: true,
        securityCode: true
      },
      loadingSec: 0,
      errMsg: '',
      hideErr: true
    }
    this.loadingTimer = null
    this.cardNumInput = React.createRef()
    this.cursorPosition = React.createRef()
    this.validFunc = this.validFunc.bind(this)
    this.getCardIssuer = this.getCardIssuer.bind(this)
    this.clickSubmit = this.clickSubmit.bind(this)
    this.fetchData = this.fetchData.bind(this)
    this.getParams = this.getParams.bind(this)
    this.getUserInfo = this.getUserInfo.bind(this)
    this.setError = this.setError.bind(this)
    this.checkInputValid = this.checkInputValid.bind(this)
  }

  componentDidMount() {
    const { pageInfo, lang } = this.props
    const i18n = bankLangPkg[lang] || bankLangPkg['en_us']
    const userInfo = this.getUserInfo()
    if (
      !pageInfo ||
      !pageInfo.order ||
      !pageInfo.vipOrder ||
      !userInfo ||
      !userInfo.uid
    ) {
      this.setError(i18n.err_system)
    }
    sendBlockPb('bank_card_fill', {
      bstp: 56,
      tjPb: {
        ...this.props.pbInfo
      }
    })
  }

  componentDidUpdate(prevProps, prevState) {
    const { cardInfo } = this.state
    const prevCardInfo = prevState.cardInfo
    // 中间删除插入空格引发的光标位置问题
    if (cardInfo.cardNum !== prevCardInfo.cardNum) {
      if (
        cardInfo.cardNum[cardInfo.cardNum.length - 2] !== ' ' ||
        this.cursorPosition.current !==
          this.cardNumInput.current.selectionEnd - 1
      ) {
        this.cardNumInput.current.selectionStart = this.cursorPosition.current
        this.cardNumInput.current.selectionEnd = this.cursorPosition.current
      }
    }
  }

  componentWillUnmount() {
    if (this.loadingTimer) {
      clearInterval(this.loadingTimer)
    }
    this.setError()
  }

  getUserInfo() {
    let userInfo = {}
    if (isLogin()) {
      userInfo.uid = getUid()
      // 之前收银台的逻辑
      let lastGlobalLoginMsg = getCookies('lastGlobalLoginMsg')
      if (
        lastGlobalLoginMsg &&
        lastGlobalLoginMsg !== null &&
        lastGlobalLoginMsg !== ''
      ) {
        lastGlobalLoginMsg = window.JSON.parse(lastGlobalLoginMsg)
        userInfo.username = lastGlobalLoginMsg.realEmail || ''
      } else {
        userInfo.username = ''
      }
    } else {
      userInfo = {}
    }
    return userInfo
  }

  getParams(lang) {
    const PAY_SUC_URL = 'vip/payResult?vipOrder='
    const userInfo = this.getUserInfo()
    const { cardInfo } = this.state
    const { pageInfo } = this.props
    const { order, vipOrder, partner, cashierType } = pageInfo
    const _defaultUrl = rebuildHttpsUrl(
      PAY_SUC_URL + vipOrder + `&cashierType=${cashierType}`
    )
    const params = {
      partner: partner || 'qiyue_global',
      sign: 'PCW',
      authcookie: 'PCW',
      order_code: order,
      uid: userInfo.uid || '',
      check3d_response_url: encodeURIComponent(_defaultUrl),
      dfp: (window.dfp && window.dfp.tryGetFingerPrint()) || '',
      userName: userInfo.username || '',
      agent_type: '',
      local_lang: lang,
      card_issuer: this.getCardIssuer(cardInfo.cardNum.replace(/\s/g, '')),
      card_num: cardInfo.cardNum.replace(/\s/g, ''),
      expiry_month: cardInfo.expiryMonth.padStart(2, '0'),
      expiry_year: cardInfo.expiryYear,
      security_code: cardInfo.securityCode,
      _t: new Date().getTime()
    }
    return params
  }

  setError(msg) {
    if (msg) {
      this.setState({
        errMsg: msg,
        hideErr: false
      })
    } else {
      this.setState({
        errMsg: '',
        hideErr: true
      })
    }
  }

  async fetchData() {
    const { setErrorPop, setStep, lang } = this.props
    const i18n = bankLangPkg[lang] || bankLangPkg['en_us']
    const options = { timeout: 40000, method: 'POST', credentials: true, }
    options.params = qs.stringify(this.getParams(lang))
    const isMobile = getDevice() === 'mobile'
    let result
    try {
      const data = await $http(bankDoPayInterface, options)
      if (data) {
        if (data.is_success === 'T' && parseInt(data.order_status, 10) === 1) {
          result = {
            msg: 'sus',
            status: 'ok'
          }
        } else if (data.code === 'CHECK_3d_ACS') {
          // 到3d认证页面
          const newWindow = isMobile
          ? window
          : window.open('about:blank', '_blank')
          newWindow.document.write(data.htmlBodyContent)
          const form = newWindow.document.querySelector('form')
          if (form) {
            form.submit()
          } else {
            result = {
              msg: '__sysErr'
            }
          }
          // 跳到第三方等待页面
          if (!isMobile) {
            setStep('queryPaymentStep')
          }
        } else {
          result = {
            msg: data.msg || '__sysErr'
          }
        }
      } else {
        result = {
          msg: '__sysErr'
        }
      }
    } catch (error) {
      result = {
        msg: error.message.match('timeout') ? '__timeout' : '__sysErr'
      }
    }
    if (result && result.msg) {
      clearInterval(this.loadingTimer)
      this.setState({
        loadingSec: 0
      })
      if (result.status === 'ok') {
        this.setError()
        // 支付成功，跳到支付结果页
        setStep('payResultStep')
      } else if (result.msg === '__timeout') {
        // 请求超时，跳转超时兜底页
        setErrorPop('timeout')
      } else if (result.msg === '__sysErr') {
        this.setError(i18n.err_system)
      } else {
        this.setError(result.msg)
      }
    }
  }

  clickSubmit(btnDisable) {
    if (!btnDisable) return
    const { setErrorPop } = this.props
    this.setState({
      loadingSec: 40
    })
    if (this.loadingTimer) {
      clearInterval(this.loadingTimer)
    }
    this.loadingTimer = setInterval(() => {
      this.setState(
        preState => {
          return { loadingSec: preState.loadingSec - 1 }
        },
        () => {
          if (this.state.loadingSec === 0) {
            clearInterval(this.loadingTimer)
            setErrorPop('timeout')
          }
        }
      )
    }, 1000)
    this.fetchData()
  }

  getCardIssuer(cardNum) {
    // JCB	3528-3589	16
    // 万事达卡 MASTERCARD	51-55	16
    // Visa	4	16
    if (cardNum.length === 16) {
      if (Number(cardNum[0]) === 4) return 'VISA'
      if (cardNum.match(/^5[1-5]/)) return 'MASTERCARD'
      const font = Number(cardNum.slice(0, 4))
      if (font >= 3528 && font <= 3589) return 'JCB'
    }
    return ''
  } 

  validFunc() {
    return {
      cardNum: num => {
        return (
          num.replace(/\s/g, '').length >= 12 &&
          !!this.getCardIssuer(num.replace(/\s/g, ''))
        )
      },
      // eslint-disable-next-line
      expiryMonth: expiryMonth =>
        Number(expiryMonth) && Number(expiryMonth) <= 12,
      expiryYear: expiryYear => {
        if (!expiryYear || expiryYear.length > 2) return false
        return Number('20' + expiryYear) >= new Date().getFullYear()
      },
      securityCode: securityCode => securityCode.length >= 3
    }
  }

  checkInputValid(validKey, validValue) {
    const { validRes } = this.state
    const newValidRes = { ...validRes }
    newValidRes[validKey] = validValue
    this.setState({
      validRes: newValidRes
    })
  }

  render() {
    const { vipLangPkg, pageInfo, lang, pbInfo } = this.props
    const i18n = bankLangPkg[lang] || bankLangPkg['en_us']
    const pbInfoStr = qs.stringify(pbInfo) + '&bstp=56'
    const { cardInfo, validRes, loadingSec, errMsg, hideErr } = this.state
    const bankImgList = [
      {
        type: 'MASTERCARD',
        url:
          '//www.iqiyipic.com/lequ/********/icon_Mastercard.png'
      },
      {
        type: 'VISA',
        url: '//www.iqiyipic.com/lequ/********/icon_Visa.png'
      },
      {
        type: 'JCB',
        url: '//www.iqiyipic.com/lequ/********/icon_JCB.png'
      }
    ]
    let btnDisable = true
    Object.keys(cardInfo).forEach(key => {
      btnDisable = btnDisable && this.validFunc()[key](cardInfo[key])
    })
    
    const mod = getCookies('mod')
    const price = Number(
      (parseInt(pageInfo.price, 10) / 100).toFixed(2)
    ).toString()
    const originalPrice = Number(
      (parseInt(pageInfo.originalPrice, 10) / 100).toFixed(2)
    ).toString()
    
    return (
      <BankCardWrapper>
        <div className="scroll-container">
          <div className="scroll-content">
            <div className="step-title">{vipLangPkg.pcashier_card_title}</div>
            <div className="vip-info">
              <div className="info">
                <div className="name">
                  {`${pageInfo.name} `}
                  {pageInfo.typeName}
                </div>
                <div className="desc">
                  {`${pageInfo.detail} `}
                  {pageInfo.autorenewTip}
                </div>
              </div>
              <div className="vip-price">
                {mod === 'vn' ? (
                  <>
                    <span className="price price-margin">{price}</span>
                    <span className="symbol">{pageInfo.currencySymbol}</span>
                  </>
                ) : (
                  <>
                    <span className="symbol price-margin">{pageInfo.currencySymbol}</span>
                    <span className="price">{price}</span>
                  </>
                )}
              </div>
              {Number(price) < Number(originalPrice) && (
                <div className="vip-original-price">
                  {mod === 'vn' ? (
                    <>
                      <span className="price-margin">{originalPrice}</span>
                      <span>{pageInfo.currencySymbol}</span>
                    </>
                  ) : (
                    <>
                      <span className="price-margin">{pageInfo.currencySymbol}</span>
                      <span>{originalPrice}</span>
                    </>
                  )}
                </div>
              )}
            </div>
            {!hideErr && (
              <div className='bank-pay-err'>
                <i className="err-img" />
                <span className="err-desc">{errMsg}</span>
              </div>
            )}
            <div className="bank-card-content">
              <div className="bank-img">
                <div>
                  {bankImgList.map(item => (
                    <img src={item.url} key={item.type} alt="backImg" />
                  ))}
                </div>
                <img
                  src="//www.iqiyipic.com/lequ/********/icon_Dss.png"
                  alt=""
                />
              </div>
              <div className={validRes.cardNum ? '' : 'state-err'}>
                <div className="title">{i18n.card_no}</div>
                <input
                  className="input-cardNum"
                  type="text"
                  ref={this.cardNumInput}
                  placeholder={i18n.card_tip}
                  value={cardInfo.cardNum}
                  onChange={e => {
                    const value = e.target.value
                    const inputPosition = e.target.selectionEnd
                    this.cursorPosition.current = inputPosition
                    if (
                      value.match(/^[\d\s]*$/) &&
                      value.replace(/\s/g, '').length <= 19
                    ) {
                      let num = value
                      // 处理在空格处删除
                      if (
                        value.length === cardInfo.cardNum.length - 1 &&
                        cardInfo.cardNum[inputPosition] === ' '
                      ) {
                        num =
                          value.slice(0, inputPosition - 1) +
                          value.slice(inputPosition, value.length)
                        this.cursorPosition.current = inputPosition - 1
                      }
                      // 处理在空格处添加
                      if (
                        value.length === cardInfo.cardNum.length + 1 &&
                        (inputPosition - 1) % 4 === 0
                      ) {
                        this.cursorPosition.current = inputPosition + 1
                      }

                      num = num.replace(/\s/g, '')
                      if (!validRes.cardNum) {
                        this.checkInputValid(
                          'cardNum',
                          this.validFunc().cardNum(value)
                        )
                      }
                      this.setState({
                        cardInfo: {
                          ...cardInfo,
                          cardNum: num.replace(/(\d{4})/g, '$1 ').trim()
                        }
                      })
                    }
                  }}
                  onBlur={e => {
                    this.checkInputValid(
                      'cardNum',
                      this.validFunc().cardNum(e.target.value)
                    )
                  }}
                />
                <div className="bankcard-err">
                  {!(cardInfo.cardNum.replace(/\s/g, '').length >= 12)
                    ? i18n.card_error
                    : i18n.card_issuer_error}
                </div>
              </div>
              <div className="row">
                <div>
                  <div className="title">{i18n.validate_date}</div>
                  <div className="expiry-row">
                    <div
                      className={`two-column ${
                        validRes.expiryMonth ? '' : 'state-err'
                      }`}
                    >
                      <input
                        className="input-expiry"
                        type="text"
                        placeholder={i18n.valid_month_tip}
                        value={cardInfo.expiryMonth}
                        onChange={e => {
                          const value = e.target.value
                          if (
                            value.match(/^[\d\s]*$/) &&
                            value.replace(/\s/g, '').length <= 2
                          ) {
                            if (!validRes.expiryMonth) {
                              this.checkInputValid(
                                'expiryMonth',
                                this.validFunc().expiryMonth(value)
                              )
                            }
                            this.setState({
                              cardInfo: {
                                ...cardInfo,
                                expiryMonth: value.replace(/\s/g, '')
                              }
                            })
                          }
                        }}
                        onBlur={e => {
                          this.checkInputValid(
                            'expiryMonth',
                            this.validFunc().expiryMonth(e.target.value)
                          )
                        }}
                      />
                      <div className="other-err">
                        {i18n.valid_date_month_error}
                      </div>
                    </div>
                    <div className="slash">/</div>
                    <div
                      className={`two-column ${
                        validRes.expiryYear ? '' : 'state-err'
                      }`}
                    >
                      <input
                        className="input-expiry"
                        type="text"
                        placeholder={i18n.valid_year_tip}
                        value={cardInfo.expiryYear}
                        onChange={e => {
                          const value = e.target.value
                          if (
                            value.match(/^[\d\s]*$/) &&
                            value.replace(/\s/g, '').length <= 2
                          ) {
                            if (!validRes.expiryYear) {
                              this.checkInputValid(
                                'expiryYear',
                                this.validFunc().expiryYear(value)
                              )
                            }
                            this.setState({
                              cardInfo: {
                                ...cardInfo,
                                expiryYear: value.replace(/\s/g, '')
                              }
                            })
                          }
                        }}
                        onBlur={e => {
                          this.checkInputValid(
                            'expiryYear',
                            this.validFunc().expiryYear(e.target.value)
                          )
                        }}
                      />
                      <div className="other-err">{i18n.valid_date_year_error}</div>
                    </div>
                  </div>
                </div>
                <div className={`cvc-block ${validRes.securityCode ? '' : 'state-err'}`}>
                  <div className="title">
                    {i18n.cvc}
                    <div className="bank-bubble">
                      <div className="icon-bubble">
                        <div className="bubble-desc desc-show">
                          <div className="bank-bubble-txt">{i18n.cvc_tip}</div>
                          <img
                            src="https://www.iqiyipic.com/common/fix/global-img/<EMAIL>"
                            alt=""
                            className="bank-bubble-pic"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                  <input
                    className="input-cvc"
                    type="text"
                    placeholder={i18n.cvc}
                    value={cardInfo.securityCode}
                    onChange={e => {
                      const value = e.target.value
                      if (
                        value.match(/^[\d\s]*$/) &&
                        value.replace(/\s/g, '').length <= 4
                      ) {
                        if (!validRes.securityCode) {
                          this.checkInputValid(
                            'securityCode',
                            this.validFunc().securityCode(value)
                          )
                        }
                        this.setState({
                          cardInfo: {
                            ...cardInfo,
                            securityCode: value.replace(/\s/g, '')
                          }
                        })
                      }
                    }}
                    onBlur={e => {
                      this.checkInputValid(
                        'securityCode',
                        this.validFunc().securityCode(e.target.value)
                      )
                    }}
                  />
                  <div className="other-err">{i18n.cvc_error}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        {/* <div className="tip">{vipLangPkg.pcashier_bank_note}</div> */}
        <button
          type="submit"
          className={`containue ${!btnDisable ? 'disabled' : ''}`}
          rseat="pay"
          data-pb={`rpage=bank_card_fill&block=bank_card&${pbInfoStr}`}
          onClick={() => {
            this.clickSubmit(btnDisable)
          }}
        >
          {i18n.next}
        </button>
        {!!loadingSec && (
          <Popup title={i18n.loading_text} loadingSec={`${loadingSec}s`} />
        )}
      </BankCardWrapper>
    )
  }
}

export default BankCard
