import styled from 'styled-components'

const Style = styled.div`
  .box {
    padding: 48px 40px;
    margin: 0px 80px 104px;
    background: #fff;
    box-sizing: border-box;
    box-shadow: 0 2px 4px 0 rgba(0,0,0,0.05);
    border-radius: 4px;
    div {
      background: #EEEEEE;
      height: 24px;
    }
    .title {
      width: 264px;
    }
    .price {
      width: 184px;
      height: 44px;
      margin-top: 24px;
    }
    .name {
      width: 184px;
      margin-top: 40px;
    }
    .detail {
      margin-top: 16px;
    }
    .last {
      display: none;
    }
  }
  @media screen and (max-width: 1919px) {
    .box {
      padding: 22px 24px;
      margin: 0px 64px 40px;
      .detail:nth-last-child(2) {
        display: none
      }
    }
  }
  @media screen and (max-width: 1023px) {
    .box {
      padding: 32px 24px;
      margin: 0px 64px 108px;
    }
  }
  @media screen and (max-width: 767px) {
    .box {
      padding: 0;
      margin: 0px 16px 56px;
      box-shadow: none;
      div {
        background: #EEEEEE;
        height: 16px;
      }
      .title {
        width: 171px;
      }
      .price {
        width: 85px;
        height: 32px;
        margin-top: 16px;
      }
      .name {
        width: 85px;
        margin-top: 32px;
      }
      .last {
        display: block;
        margin-top: 16px;
      }
    }
  }
`
export default Style