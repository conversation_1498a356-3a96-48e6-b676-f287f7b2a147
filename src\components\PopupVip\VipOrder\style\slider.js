import styled from 'styled-components'

const Style = styled.div`
  position: relative;
  margin: 0 auto;
  width: 806px;
  .slider {
    overflow: hidden;
    position: relative;
  }
  .slider-content {
    transition: 0.5s;
  }
  .left,
  .right {
    width: 28px;
    height: 28px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    position: absolute;
    top: calc(50% - 16px);
    transform: translateY(-50%);
    background: #bb8b51;
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.16);
    z-index: 1;
    cursor: pointer;
  }
  .right {
    right: -32px;
    background: url('//www.iqiyipic.com/lequ/20210624/icon-plan-next-normal.png') no-repeat;
    background-size: cover;
    &:hover {
      background: url('//www.iqiyipic.com/lequ/20210624/icon-plan-next-hover.png') no-repeat;
      background-size: cover;
    }
  }
  .left {
    left: -32px;
    background: url('//www.iqiyipic.com/lequ/20210625/icon-plan-last-normal.png') no-repeat;
    background-size: cover;
    &:hover {
      background: url('//www.iqiyipic.com/lequ/20210702/icon-plan-last-hover.png') no-repeat;
      background-size: cover;
    }
  }
  @media screen and (max-width: 1023px) {
    width: 532px;
  }
`
export default Style
