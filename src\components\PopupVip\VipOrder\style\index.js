import styled from 'styled-components'

const Style = styled.div`
  margin-top: 4px;
  /* .error {
    height: 42px;
    margin-top: 16px;
    padding: 0 18px;
    display: flex;
    align-items: center;
    background: #feefef;
    border: 1px solid #e60000;
    border-radius: 2px;
    color: #e60000;
    font-size: 14px;
    svg {
      margin-right: 10px;
      fill: #e60000;
    }
  } */
  .bank-pay-err {
    box-sizing: border-box;
    width: 806px;
    min-height: 44px;
    background: #FEEBEB;
    border: 1px solid #FEC5C2;
    border-radius: 6px;
    margin: 12px auto;
    padding: 12px;
    display: flex;
    align-items: center;
    .err-img {
      flex-shrink: 0;
      display: inline-block;
      vertical-align: middle;
      width: 20px;
      height: 20px;
      background: url('//www.iqiyipic.com/lequ/********/icon_error.png') no-repeat;
      background-size: cover;
      margin-right: 8px;
    }
    .err-desc {
      line-height: 16px;
      font-size: 14px;
      color: #FE3D33;
      letter-spacing: 0;
      font-weight: 400;
      vertical-align: middle;
    }
  }
  .vip {
    padding-top: 17px;
    &-icon {
      display: flex;
      img {
        height: 24px;
        margin-right: 8px;
      }
    }
    &-text {
      margin-top: 15px;
      color: #666;
      p {
      }
    }
  }
  .redeem-code {
    display: flex;
    align-items: center;
    font-size: 14px;  
    line-height: 16px;
    color: #E09E51;
    &:hover {
      text-decoration: underline;
    }
    svg {
      margin-right: 4px;
    }
  }
  .pkg-card {
    /* position: relative; */
    box-sizing: border-box;
    background: #fff;
    cursor: pointer;
    flex-shrink: 0;
    &:hover {
      background: #fff9f2;
      border: 1px solid #eac48b;
      .label {
        top: -1px;
        left: -1px;
      }
    }
    &.select {
      border: 3px solid #a77c48;
      background: #ffefdd;
      .label {
        top: -3px;
        left: -3px;
      }
    }
    .label {
      position: absolute;
      top: 0;
      left: 0;
      height: 18px;
      line-height: 18px;
      padding: 0 3.6px;
      background-image: linear-gradient(269deg, #fff2de 3%, #f2d8b1 100%);
      background: #222222;
      border-radius: 1.8px;
      font-size: 12px;
      white-space: nowrap;
      color: #ffe3c2;
      transform: scale(0.8333) translateX(-10%) translateY(-8.333%);
      overflow: hidden;
    }
  }
  &.single {
    padding: 0 180px;
  }
  .bottom-text {
    margin: -24px 0 40px;
    text-align: center;
    color: #666;
  }
  @media screen and (max-width: 1023px) {
    &.single {
      padding: 0 40px;
    }
    .bank-pay-err {
      width: 532px;
    }
  }
`
export default Style
