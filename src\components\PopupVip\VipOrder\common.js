import React from 'react'

export const VipPreviledge = ({ item }) => {
  if (!item || !item.privilegeGroupLocation) return null

  const vipIconList = item.privilegeGroupLocation.map(item => item.icon)
  const vipTextList = item.privilegeGroupLocation.map(item => item.text)

  return (
    <div className="vip">
      <div className="vip-icon">
        {vipIconList.map(icon => (
          <img key={icon} id={icon} src={icon} alt="vip" />
        ))}
      </div>
      <div className="vip-text">
        {vipTextList.map(text => (
          <p key={text}>{text}</p>
        ))}
      </div>
    </div>
  )
}

export const PkgDetail = ({ item }) => {
  return (
    <div className="detail">
      {item.detail}
      <p>{item.autorenewTip}</p>
    </div>
  )
}

export const TileVipPreviledge = ({ item }) => {
  if (!item || !item.privilegeGroupLocation) return null

  const vipTextList = item.privilegeGroupLocation.map(item => item.text)
  return (
    <div className="vip">
      <div className="vip-text">
        {vipTextList.map((text, ind) => (
          <div key={ind}>
            <div className="icon"></div>
            <span key={text}>{text}</span>
          </div>
        ))}
      </div>
    </div>
  )
}
