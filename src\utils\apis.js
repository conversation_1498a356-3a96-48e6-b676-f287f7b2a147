/* 由于主页+频道页目前逻辑是写在saga中的，对于新增频道页的资源位请求，没办法单独拆出来
 使用。所以新建此文件，放入公用的api请求
*/
import $http from '@/kit/fetch'
import { getCookies } from '@/kit/cookie'
import { getDevice } from '@/kit/device'
import { handleFocusData, handleResData } from '@/store/sagas/home/<USER>'
import { RES_BATCH, TAB_INFO } from '@/constants/interfaces'
import {
  getTabInfoData,
  getTabRowResourceData,
  getTabFocusResourceData
} from '@/store/reducers/home/<USER>'

// 获取公参
function commonDeviceIdParams(ctx) {
  const params = {}
  if (ctx) {
    params.modeCode =
      ctx.req.headers['modecode'] || getCookies('mod', ctx) || 'intl' // 模式 to do: 上线之前去掉ntw默认值
    params.langCode =
      ctx.req.headers['langcode'] || getCookies('lang', ctx) || 'zh_tw' // 语言
    params.deviceId = getCookies('QC005', ctx) || 1213 // 个性化推荐用的，其实用不到
  } else {
    params.modeCode = getCookies('mod') || 'intl'
    params.langCode = getCookies('lang') || 'zh_tw'
    params.deviceId = getCookies('QC005') || 1213
  }
  params.platformId = getDevice() === 'pc' ? 3 : 4 // 平台id
  return params
}

// 资源位请求
/*
  @params param [Object] 请求入参，部分参数已经在下面备注
  @params param.size [Number] 每次返回多少个  

*/
export async function getBatchResInfo(hasFocus, param, ctx) {
  try {
    const rpage = param.rpage || '' // pingback使用
    const block = param.block || [] // pingback使用
    const position = param.position || [] // pingback使用，表明这是第几区块

    delete param.rpage
    delete param.block
    delete param.position

    const options = {}
    const initParams = commonDeviceIdParams(ctx)

    options.params = Object.assign(
      initParams,
      {
        sid: initParams.deviceId + '_' + new Date().getTime(),
        forbidDegradation: 'False',
        drmEnabled: '',
        filters: '',
        pspStatus: 1,
        pos: 0,
        size: 6,
        clientIp: global.clientIp || '***************'
      },
      param
    )
    if (hasFocus) {
      options.params.resType = 1
    }
    const url = RES_BATCH
    const data = await $http(url, options)
    const dataList = []
    const resIdArr = Array.isArray(param.resIds) ? param.resIds : [param.resIds]
    if (hasFocus && data.data[0]) {
      const focusData = handleFocusData(data.data[0], { rpage })
      // const dataWithColor = await Promise.all(
      //   focusData.map(async item => {
      //     const imgColorRes = await $http(GET_IMG_COLOR, {
      //       params: {
      //         left: '0.0',
      //         top: '0.0',
      //         right: '1.0',
      //         bottom: '1.0',
      //         debug_switch: 1,
      //         imgUrl: encodeURI('http:' + item.imgSrc)
      //       }
      //     })
      //     if (imgColorRes.code === 0) {
      //       return { ...item, color: imgColorRes.palette }
      //     }
      //     return item
      //   })
      // )
      dataList.push(focusData)
    }
    if (data.code === '0') {
      data.data.forEach((item, index) => {
        if (index === 0 && hasFocus) {
          // const focusData = handleFocusData(item, { rpage })
          // dataList.push(focusData)
        } else {
          const resId = resIdArr[index]

          if (item) {
            dataList.push(
              handleResData(item, {
                resId,
                rpage,
                block: block[index],
                position: position[index] || index
              })
            )
          } else {
            dataList.push(null)
          }
        }
      })
      if (hasFocus) {
        ctx.store.dispatch(getTabFocusResourceData(dataList[0]))
      } else {
        ctx.store.dispatch(getTabRowResourceData(dataList))
      }
    }
    return dataList
  } catch (e) {
    console.log('request error')
    console.log(e)
    return new Error(e)
  }
}

// tabInfo Tab列表管理接口请求
function getCurPageTabList(ctx, tabInfoList) {
  const reqUrl = ctx.req.url
  const path = reqUrl.split('?')[0]

  const res = tabInfoList.find(cur => {
    const curUrl = cur.value.trim()
    if (curUrl === path) {
      return true
    } else {
      return false
    }
  })

  return res || null
}

/*
  @params param [Object] 请求入参，部分参数已经在下面备注
  @params param.size [Number] 每次返回多少个  
*/
export async function getTabInfo(ctx) {
  const options = {}
  options.params = commonDeviceIdParams(ctx)
  delete options.params.deviceId

  const tabInfoList = await $http(TAB_INFO, options)
  if (tabInfoList.code === '0') {
    const curData = getCurPageTabList(ctx, tabInfoList.data[0].tconts)
    await ctx.store.dispatch(
      getTabInfoData({
        curTabInfo: curData,
        allTabInfoList: tabInfoList.data[0].tconts
      })
    )
    return curData
  }
  return null
}
