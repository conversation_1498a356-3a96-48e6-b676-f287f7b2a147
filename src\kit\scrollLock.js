/* 
  modal滚动防穿透
*/
class ScrollLock {
  constructor() {
    this.lastScrollTop = 0
    this.bodyOverflow = null
    this.bodyWidth = null
    this.isUnLocked = null
  }

  lock(scrollTop) {
    this.bodyOverflow = document.defaultView.getComputedStyle(
      document.body
    ).overflow
    this.lastScrollTop = scrollTop
    this.bodyWidth = document.defaultView.getComputedStyle(document.body).width
    document.body.style.overflow = 'hidden'
    document.body.style.position = 'fixed'
    document.body.style.top = -scrollTop + 'px' // 改变css中top的值，配合fixed使用
    document.body.style.width = '100%'
    this.isUnLocked = false
  }

  unlock() {
    // 防止多次调用unlock
    if (!this.isUnLocked) {
      // 用阻止冒泡处理滚动穿透再uc上有兼容问题，不得已改变样式，目前body上没有挂载样式，以后有需要特殊处理。
      document.body.style.removeProperty('overflow')
      document.body.style.removeProperty('width')
      document.body.style.removeProperty('position')
      document.body.style.removeProperty('top')
      if (window.pageXOffset) {
        window.pageYOffset = this.lastScrollTop
      } else {
        document.body.scrollTop = this.lastScrollTop
        document.documentElement.scrollTop = this.lastScrollTop
      }
      this.lastScrollTop = 0
      this.isUnLocked = true
    }
  }
}

export default ScrollLock
