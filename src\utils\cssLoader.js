function poll(node, callback) {
  if (callback.isCalled) {
    return
  }

  let isLoaded = false
  // webkit
  if (/webkit/i.test(navigator.userAgent)) {
    if (node.sheet) {
      isLoaded = true
    }
  }
  // for Firefox
  else if (node.sheet) {
    try {
      if (node.sheet.cssRules) {
        isLoaded = true
      }
    } catch (ex) {
      // NS_ERROR_DOM_SECURITY_ERR
      if (
        ex.code === 1000 ||
        ex.name === 'NS_ERROR_DOM_SECURITY_ERR' ||
        ex.name === 'SecurityError'
      ) {
        isLoaded = true
      }
    }
  }

  if (isLoaded) {
    // give time to render.
    setTimeout(function () {
      callback()
    }, 1)
  } else {
    setTimeout(function () {
      poll(node, callback)
    }, 1)
  }
}
/**
 * @file css文件加载工具方法
 */

function styleOnload(node, callback) {
  // for IE6-9 and Opera
  if (node.attachEvent) {
    node.attachEvent('onload', callback)
  } else {
    setTimeout(function () {
      poll(node, callback)
    }, 0) // for cache
  }
}

/**
 * 动态创建link标签
 * @param cssURL {String} css的地址
 * @param lnkId {String} link标签的id
 * @param charset {String} css的编码
 * @param media {String} css媒体类型
 */
function createLink(cssURL, lnkId, charset, media) {
  if (window) {
    const head = window.document.getElementsByTagName('head')[0]
    const firstChild = head.children[0]
    let linkTag = ''
    linkTag = window.document.createElement('link')
    linkTag.setAttribute('id', lnkId || 'dynamic-style')
    linkTag.setAttribute('rel', 'stylesheet')
    linkTag.setAttribute('charset', charset || 'utf-8')
    linkTag.setAttribute('media', media || 'all')
    linkTag.setAttribute('type', 'text/css')
    linkTag.href = cssURL

    head.insertBefore(linkTag, firstChild)
    return linkTag
  }
}

function loadBatch(cssList) {
  const defs = []
  cssList.forEach((item) => {
    defs.push(
      new Promise((resolve) => {
        const styleNode = createLink(item.url, item.id, 'utf-8', 'all')
        styleOnload(styleNode, () => {
          // 返回新实例
          resolve()
        })
      })
    )
  })
  return Promise.all(defs)
}

export const cssLoader = {
  /*
        get 获取css
        @return 返回Promise对象
     */
  get(cssList) {
    return loadBatch(cssList)
  }
}
