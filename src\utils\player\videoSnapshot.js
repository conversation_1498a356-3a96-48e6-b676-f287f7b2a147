const handleSubWrapper = (ctx, dom, canvas) => {
  const doms = findWrapper(dom)
  if (doms) {
    doms.forEach(domItem => {
      const subDom = findWrapper([].slice.call(domItem.children))
      if (subDom) {
        const secDom = findWrapper([].slice.call(subDom[0].children))
        if (secDom) {
          secDom.forEach((current, index) => {
            const s = domItem.style
            const fontSize = (s['font-size'] && parseFloat(s['font-size'])) || 0 // 字号
            const lineHeight =
              (((s['line-height'] && parseFloat(s['line-height'])) ||
                fontSize * 1.5) -
                fontSize) /
              2 // 上下边距
            const height = fontSize + lineHeight // 高度
            const lastHeight =
              (2 * lineHeight + fontSize) * (secDom.length - 1 - index) // 上一行Y轴位置

            let pY = lastHeight
            for (let i = 0; i < s.length; i++) {
              // 遍历拿到行内元素
              if (!s[i]) {
                break
              }
              const key = s[i]

              if (key === 'bottom') {
                pY = canvas.height - parseFloat(s[key]) - lineHeight - pY
              }

              if (key === 'top') {
                // css中的top和现在算的y值差一个高度 所以加上
                pY = parseFloat(s[key]) + height
              }
            }
            ctx.font = `${parseFloat(
              s['font-size']
            )}px PingFangSC-Regular,Microsoft Yahei,sans-serif `
            ctx.textAlign = 'center'
            ctx.fillStyle = '#fff'
            ctx.fillText(current.innerText, canvas.width / 2, pY)
          })
        }
      }
    })
    return true
  }
  return false
}

const findWrapper = dom => {
  const subtitleDom = dom.filter(sub => {
    return sub.style.display !== 'none'
  })
  if (subtitleDom.length) {
    return subtitleDom
  }
  return false
}

/**
 *
 * @param {*} dom dom元素
 * <AUTHOR> //贼麻烦
 */
const videoSnapShot = dom => {
  const _video = dom.querySelector('video')
  const _canvas = document.createElement('canvas')
  const _ctx = _canvas.getContext('2d')
  const _videoWidth = _video.videoWidth
  const _videoHeight = _video.videoHeight
  const ratio = _videoWidth / _videoHeight // 获取原视频宽高比
  _canvas.width = _video.clientWidth
  _canvas.height = _video.clientHeight
  const sY = _canvas.height / 2 - (_canvas.width * (1 / ratio)) / 2 // 将画面居中显示
  _ctx.fillRect(0, 0, _canvas.width, _canvas.height)
  // 有一种情况是video的宽度要大于视频原宽度，所以要拉伸
  _ctx.drawImage(
    _video,
    0,
    0,
    _videoWidth,
    _videoHeight,
    0,
    sY,
    _canvas.width,
    _canvas.width * (1 / ratio)
  )

  handleSubWrapper(
    _ctx,
    [].slice.call(dom.getElementsByClassName('iqp-subtitle'), 0, -1),
    _canvas
  )
  return _canvas
}

export default videoSnapShot
