/**
 * @desc 新版本收银台会员权益
 * <AUTHOR>
 * @time 2022-02-09
 * @feature  GLOBALREQ-6796
 */
import React, { useState, useLayoutEffect } from 'react'
import { getCookies } from '@/kit/cookie'
const lang = getCookies('lang') || 'en_us'
import VipPrivilegeWrapper from './style/index'
const VipPrivilege = (props) => {
  // 设置权益列表
  const {
    vipLangPkg,
    privilegeList,
    selectVipTag,
    selectProd,
    closePri,
    userClose
  } = props
  const { pkgItem = {} } = selectProd
  const [priList, setPriList] = useState([])
  const [showPri, setShowPri] = useState(false)
  const [defaultPri, setDefaultPri] = useState(false)
  useLayoutEffect(() => {
    const { vipTag } = pkgItem
    let _vipTag = vipTag
    if (!vipTag) {
      _vipTag = selectVipTag
    }
    setPriList(privilegeList[_vipTag] && privilegeList[_vipTag].privilegeCard_v)
    if (!userClose) {
      setDefaultPri(true)
      setShowPri(
        privilegeList[_vipTag] && privilegeList[_vipTag].isSelectedBenefit === 1 // 1展开 2折叠
      )
    }
  }, [privilegeList, selectVipTag, selectProd])
  return (
    <VipPrivilegeWrapper>
      <div className="privilege-title-wrapper">
        <p className="privilege-title">
          {pkgItem.detail
            ? `${pkgItem.detail}${lang === 'zh_cn' || lang === 'zh_tw' ? '。' : '. '}`
            : ''}
          {pkgItem.autorenewTip}
        </p>
        <div
          className="privilege-btn-wrapper"
          onClick={() => {
            closePri && closePri()
            setShowPri(!showPri)
            setDefaultPri(false)
          }}
        >
          <p className="privilege-btn-title">
            {vipLangPkg.PCW_VIP_1649665455092_183}
          </p>
          <i className={`privilege-btn-icon ${showPri ? 'icon-down' : ''}`}></i>
        </div>
      </div>
      <section
        className={`privilege-list ${
          defaultPri ? 'default-privilege-list' : ''
        }`}
        style={{ maxHeight: `${showPri ? '150px' : '0'}` }}
      >
        {priList &&
          priList
            .sort((a, b) => a.sort - b.sort)
            .map((item, index) => {
              return (
                <div
                  className="privilege-item"
                  key={item.name + index.toString()}
                >
                  <img src={item.icon} alt="" className="item-icon" />
                  <p className="item-detail">{item.name}</p>
                </div>
              )
            })}
      </section>
    </VipPrivilegeWrapper>
  )
}

export default VipPrivilege
