import { isDigit } from '@/kit/number'

const SECONDS_PER_SECOND = 1
const SECONDS_PER_MINUTE = 60
const SECONDS_PER_HOUR = 60 * SECONDS_PER_MINUTE
const SECONDS_PER_DAY = 24 * SECONDS_PER_HOUR

const designations = [
  ['D', SECONDS_PER_DAY],
  ['H', SECONDS_PER_HOUR],
  ['M', SECONDS_PER_MINUTE],
  ['S', SECONDS_PER_SECOND]
]

export default function secondsToDuration(seconds) {
  let duration = 'PT' // 影片时长不会超过一天，所以这里默认最大到小时
  let remainder
  if (isDigit(seconds)) {
    remainder = seconds
  } else {
    remainder = 0
  }

  designations.forEach(([sign, seconds]) => {
    const value = Math.floor(remainder / seconds)

    remainder %= seconds

    if (value) {
      duration += `${value}${sign}`
    }
  })

  if (duration === 'PT') {
    duration = 'PT0S'
  }

  return duration
}
