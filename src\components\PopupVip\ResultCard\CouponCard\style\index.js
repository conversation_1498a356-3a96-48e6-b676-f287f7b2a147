import styled from 'styled-components'
const CouponCardWrapper = styled.div`
  width: 360px;
  margin: 0 auto;
  .coupon-card-wrapper {
    width: 100%;
    padding: 16px;
    margin-bottom: 16px;
    background: #ffffff;
    border: 2px solid #F2BF83;
    /* box-shadow: 0px 4px 10px 0px rgba(255, 202, 146, 0.2); */
    border-radius: 6px;
    box-sizing: border-box;
  }
  .coupon-card-header {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
  }
  .coupon-card-top {
    width: 100%;
    display: flex;
    align-items: center;
  }
  .coupon-card-img {
    width: 52px;
    height: 52px;
    align-self: flex-start;
  }
  .coupon-card-left {
    flex: 1;
    margin-left: 16px;
    overflow: hidden;
  }
  .coupon-card-title-wrapper {
    text-align: left;
  }
  .coupon-card-title {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    font-size: 14px;
    color: #222222;
    font-weight: 500;
    line-height: 16px;
  }
  .coupon-card-subtitle {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    margin-top: 4px;
    font-size: 14px;
    color: #999999;
    font-weight: 400;
    line-height: 16px;
  }

  .coupon-card-detail-wrapper {
    display: flex;
    align-items: center;
    margin-top: 16px;
    width: 100%;
    justify-content: space-around;
    margin-left: 68px;
  }
  .coupon-card-name {
    flex: 1;
    font-size: 16px;
    text-align: left;
    color: #fe3b30;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    font-weight: bold;
  }
  .coupon-card-btn {
    position: relative;
    width: 116px;
    height: 36px;
    line-height: 36px;
    padding: 0 12px;
    margin-left: 16px;
    background: #f2bf83;
    border-radius: 4px;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    &:hover {
      &:after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 4px;
        background: rgba(255, 255, 255, 0.2);
      }
    }
  }
  .disable-coupon-btn {
    background: #e9e9e9;
    .coupon-card-btn-title {
      color: #999999;
    }
  }
  .go-use-coupon {
    background: transparent;
    border: 1px solid #e09e51;
    .coupon-card-btn-title {
      color: #e09e51;
    }
  }
  .coupon-card-btn-title {
    width: 100%;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    font-size: 14px;
    color: #111319;
    text-align: center;
    font-weight: 500;
  }
  .coupon-card-btn-loading {
    width: 20px;
    height: 20px;
    animation: Loading 1s linear infinite;
  }
  @keyframes Loading {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
`
export default CouponCardWrapper
