import styled from 'styled-components'
const ResultWrapper = styled.div`
  position: relative;
  min-height: 640px;
  width: 100%;
  height: 1px;
  /* padding: 40px; */
  box-sizing: border-box;
  /* overflow: scroll; */
  &:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 40px;
    background-image: linear-gradient(
      to top,
      rgba(255, 255, 255, 1),
      rgba(255, 255, 255, 0)
    );
    background-color: transparent;
  }
  &:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 40px;
    background-image: linear-gradient(
      to bottom,
      rgba(255, 255, 255, 1),
      rgba(255, 255, 255, 0)
    );
  }
  .result-content-wrapper {
    max-height: calc(100vh - 80px);
    /* margin-top: 40px; */
    height: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
  }
  .baseline-content {
    align-items: baseline;
  }
  .result-content {
    /* display: flex;
    align-items: center; */
    max-height: 100%;
    width: 100%;
    margin-top: 80px;
    padding: 40px;
    /* overflow: scroll; */
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: center;
    /* flex-direction: column; */
  }
  .neterr-content {
    margin-top: 0;
    .result-status-text {
      margin: 24px 0 8px;
    }
    .complete-btn {
      margin-top: 40px;
    }
  }
  .result-content-complete {
    margin-top: 0;
    padding: 40px 0;
    height: 100%;
    /* margin-bottom: 40px; */
  }
  .result-complete {
    width: 100%;
    overflow-y: auto;
    max-height: 100%;
    padding: 40px 0;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
  }

  /* .result-loading {
    position: relative;
    width: 80px;
    height: 80px;
    margin: 0 auto;
    background: transparent;
    border: 8px solid #f2f2f2;
    border-radius: 50%;
    text-align: center;
    line-height: 150px;
    font-family: sans-serif;
  }
  .result-loading:before {
    content: '';
    position: absolute;
    top: -8px;
    left: -8px;
    width: 100%;
    height: 100%;
    border: 8px solid transparent;
    border-top: 8px solid #f2bf83;
    border-right: 8px solid #f2bf83;
    border-radius: 50%;
    animation: animateC 1s linear infinite;
  }
  span {
    display: block;
    position: absolute;
    top: calc(50% - 2px);
    left: 50%;
    width: 50%;
    height: 4px;
    background: transparent;
    transform-origin: left;
    animation: animate 2s linear infinite;
  } */
  .result-pending {
    text-align: center;
  }
  .result-loading-img {
    width: 30px;
    height: 30px;
    /* background: url('//www.iqiyipic.com/new-vip/loading.png') no-repeat center;
    background-size: 100% 100%; */
    animation: Loading 1s linear infinite;
  }
  @keyframes Loading {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
  .new-vip-result-header {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 24px;
    .back {
      font-size: 16px;
      color: #666666;
      font-weight: 400;
      position: absolute;
      top: 12px;
      left: 12px;
      display: block;
      width: 24px;
      height: 24px;
      background: url('//www.iqiyipic.com/lequ/20210622/icon-nav-back-normal.png')
        no-repeat;
      background-size: cover;
      &:hover {
        background: url('//www.iqiyipic.com/lequ/20210622/icon-nav-back-hover.png')
          no-repeat;
        background-size: cover;
      }
    }
    .close {
      position: absolute;
      top: 12px;
      right: 12px;
      width: 24px;
      height: 24px;
      background: url('//www.iqiyipic.com/lequ/20210622/icon-nav-close-normal.png')
        no-repeat;
      background-size: cover;
      cursor: pointer;
      &:hover {
        background: url('//www.iqiyipic.com/lequ/20210622/icon-nav-close-hover.png')
          no-repeat;
        background-size: cover;
      }
    }
  }
  @keyframes animateC {
    0% {
      transform: rotate(10deg);
    }
    50% {
      transform: rotate(50deg);
    }
    100% {
      transform: rotate(370deg);
    }
  }
  @keyframes animate {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
  .result-error,
  .result-complete {
    text-align: center;
  }

  .result-error-img {
    width: 100px;
    height: 100px;
  }
  .result-success-img {
    width: 48px;
    height: 48px;
    margin-bottom: 16px;
  }
  .result-status-wrapper {
    width: 420px;
    margin: 24px auto 0;
    text-align: center;
  }
  .result-status-text {
    max-width: 420px;
    font-size: 20px;
    color: #222222;
    text-align: center;
    font-weight: bold;
  }
  .result-substatus {
    max-width: 420px;
    margin-top: 8px;
    font-size: 16px;
    color: #999999;
  }
  .activate-email-title {
    margin-bottom: 32px;
    font-size: 20px;
    color: #222222;
    text-align: center;
    font-weight: 700;
  }
  .link-email-wrapper {
    margin-bottom: 32px;
  }
  .link-email-icon {
    width: 108px;
  }
  .complete-btn {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    max-width: 420px;
    min-width: 108px;
    padding: 0 24px;
    margin: 0 auto;
    margin-top: 40px;
    height: 44px;
    font-size: 16px;
    line-height: 44px;
    color: #111319;
    background: #f2bf83;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    &:hover {
      background: #f4cb9b;
      color: #404247;
    }
  }
  .result-tip {
    margin-top: 120px;
    font-size: 12px;
    color: #999999;
    text-align: center;
  }
  .other-question {
    font-size: 12px;
    color: #e09e51;
  }
  .lds-ring {
    display: inline-block;
    position: relative;
    width: 80px;
    height: 80px;
    border: 8px solid #f2f2f2;
    border-radius: 50%;
    box-sizing: border-box;
  }
  .lds-ring div {
    box-sizing: border-box;
    display: block;
    position: absolute;
    top: -8px;
    left: -8px;
    width: 80px;
    height: 80px;
    border: 8px solid #fff;
    border-radius: 50%;
    animation: lds-ring 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
    border-color: #f2bf83 transparent transparent transparent;
  }
  .lds-ring div:nth-child(1) {
    animation-delay: -0.45s;
  }
  .lds-ring div:nth-child(2) {
    animation-delay: -0.3s;
  }
  .lds-ring div:nth-child(3) {
    animation-delay: -0.15s;
  }
  @keyframes lds-ring {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
  .act-modal-wrapper {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    .act-modal-content {
      position: absolute;
      width: 600px;
      height: 444px;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
    .act-modal-link {
      display: inline-block;
      width: 100%;
      height: 100%;
    }
    .act-modal-img {
      width: 100%;
      border-radius: 8px;
    }
    .act-modal-close {
      position: absolute;
      right: -28px;
      top: -28px;
      cursor: pointer;
      width: 24px;
      height: 24px;
      svg {
        width: 24px;
        height: 24px;
      }
      &:hover {
        svg rect,
        svg path {
          fill: rgba(28, 199, 73, 0.7);
        }
      }
    }
    @media screen and (min-width: 1680px) {
      .act-modal-close {
        right: -30px;
        top: -30px;
        width: 26px;
        height: 26px;
        svg {
          width: 26px;
          height: 26px;
        }
      }
    }
  }
`
export default ResultWrapper
