import styled from 'styled-components'

const Style = styled.div`
  display: flex;
  min-height: 507px;
  margin-top: 20px;
  .card {
    width: 404px;
    min-height: 507px;
    padding: 18px 16px;
    margin-right: 16px;
    box-sizing: border-box;
    background: #fff;
    border-radius: 8px;
  }
  .card-top {
    padding-bottom: 30px;
    border-bottom: 1px solid #dbdbdb;
    text-align: center;
  }
  .type-name {
    font-weight: bold;
    font-size: 20px;
    color: #000000;
  }
  .vip {
    padding-top: 11px;
    &-icon {
      justify-content: center;
    }
    &-text {
      margin-top: 8px;
      p {
        display: inline;
        padding-right: 12px;
      }
    }
  }
  .pkg-card {
    position: relative;
    width: 372px;
    height: 102px;
    margin-top: 16px;
    padding: 31px 12px 17px;
    display: flex;
    justify-content: space-between;
    border-radius: 4px;
    border: 1px solid #dbdbdb;
    .label {
      top: -1px;
      left: -1px;
    }
    &.select {
      border: 2px solid #a77c48;
      padding: 30px 11px 16px;
      .label {
        top: -2px;
        left: -2px;
      }
    }
    .title {
      font-size: 14px;
      color: #333333;
      font-weight: bold;
    }
    .price-top {
      display: flex;
      align-items: center;
      color: #a7763a;
    }
    .unit {
    }
    .real-price {
      font-size: 24px;
      font-weight: bold;
    }
    .origin-price {
      color: #999999;
      text-decoration: line-through;
    }
    .detail {
      margin-top: 4px;
      color: #666;
      line-height: 16px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }
  }
  .label {
    max-width: 439px;
  }
  &.single {
    .card {
      width: 544px;
    }
    .label {
      max-width: 607px;
    }
    .pkg-card {
      width: 512px;
    }
  }
  @media screen and (max-width: 1023px) {
    .card {
      width: 264px;
    }
    .pkg-card {
      width: 232px;
    }
  }
`
export default Style
