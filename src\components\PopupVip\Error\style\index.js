import styled from 'styled-components'

const ErrorWrapper = styled.div`
  width: 424px;
  margin: 0 auto;
  padding-top: 90px;
  text-align: center;
  .error-img {
    width: 110px;
    height: 110px;
    display: block;
    margin: 0 auto 24px;
  }
  .error-title {
    font-size: 14px;
    color: #333333;
    text-align: center;
    line-height: 20px;
    min-height: 40px;
    margin-bottom: 32px;
  }
  .error-button {
    box-sizing: border-box;
    margin: 0 auto 130px;
    width: 280px;
    height: 44px;
    line-height: 19px;
    font-size: 16px;
    color: #111319;
    letter-spacing: 0;
    text-align: center;
    font-weight: 500;
    background: #F2BF83;
    border-radius: 4px;
    border: none;
    padding: 0 16px;
    cursor: pointer;
    &:hover {
      background: #F4CB9B;
    }
  }
  @media screen and (max-width: 767px) {
    width: 270px;
    padding-top: 0;
    .error-img {
      width: 80px;
      height: 80px;
      margin-bottom: 16px;
    }
    .error-title {
      margin-bottom: 16px;
    }
    .error-button {
      min-width: 64px;
      max-width: 270px;
      height: 36px;
      padding: 0 12px;
      margin-bottom: 40px;
      line-height: 16px;
      font-size: 14px;
      color: #111319;
      letter-spacing: 0;
      text-align: center;
      font-weight: 500;
    }
  }
`
export default ErrorWrapper
