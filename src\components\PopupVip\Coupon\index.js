/**
 * @desc 新版本收银台优惠券
 * <AUTHOR>
 * @time 2022-02-18
 * @feature  GLOBALREQ-6796
 */
import React, { useState, useEffect, useRef } from 'react'
import { formatDate, getTimeZone, getPtid } from '@/utils/common'
import { getUa, getDevice } from '@/kit/device'
import { getCookies } from '@/kit/cookie'
import $http from '@/kit/fetch'
import { isLogin } from '@/utils/userInfo'
import { EXCHANGE_COUPON, USER_RISK } from '@/constants/interfaces'
import Fengkong from '../Fengkong'
import CouponWrapper from './style/index'
import Toast from '../Toast'
const lang = getCookies('lang') || 'en_us'
const mod = getCookies('mod') || 'intl'
const Coupon = (props) => {
  const {
    selectProd,
    vipLangPkg,
    cashierLangPkg,
    uniqueCoupons,
    getVipData,
    selectedPW,
    getCOU,
    selectedCOU,
    hasCoupon = true,
    tabChanged,
    startLogin,
    setManualRedeem,
    showRedeemCoupon,
    pbInfoStr
    // setScrollFix
  } = props
  // console.log(uniqueCoupons, 'in coupon comp')
  const {
    pkgItem,
    couponItem = {},
    canuseCoupon,
    couponCode: avaCouponCode
  } = selectProd
  const [startRedeem, setStartRedeem] = useState(showRedeemCoupon)
  const [showCouponTag, setShowCouponTag] = useState(false)
  const [showConditionTag, setShowConditionTag] = useState([])
  // 可用的优惠券
  const [avaCoupons, setAvaCoupons] = useState([])
  const [avaCList, setAvaCList] = useState([])
  const [couponList, setCouponList] = useState([])
  const [couponCode, setCouponcode] = useState('')
  const [userCoupon, setUserCoupon] = useState('')
  // 用户风险信息
  const [userRisk, setUserRisk] = useState('')
  const [riskLevel, setRiskLevel] = useState('')
  const [riskCode, setRiskCode] = useState('')
  const [fkToken, setFkToken] = useState('')
  // const [userError, setUserError] = useState(false)
  const [showFK, setShowFK] = useState(false)
  const [errMsg, setErrMsg] = useState('')
  const [dfp, setDFP] = useState(
    (window.dfp && window.dfp.tryGetFingerPrint()) || ''
  )
  const [selectCoupon, setSelectCoupon] = useState(couponItem)

  const [redeemLoading, setRedeemLoading] = useState(false)
  const [redeemSuccess, setRedeemSuccess] = useState(false)
  const wrapperRef = useRef(null)

  function useOutside(ref) {
    useEffect(() => {
      function handleClickOutside(event) {
        if (ref.current && !ref.current.contains(event.target)) {
          hideCoupon()
        }
      }
      if (window) {
        window.document.addEventListener('mousedown', handleClickOutside)
        return () => {
          // Unbind the event listener on clean up
          window.document.removeEventListener('mousedown', handleClickOutside)
        }
      }
    }, [ref])
  }
  useOutside(wrapperRef)
  //
  function choseCoupon(event, item, index) {
    event.stopPropagation()
    if (avaCList.indexOf(item.couponCode) < 0) {
      return
    }
    setShowCouponTag(false)
    setSelectCoupon(item)
    getCOU(item)
  }
  useEffect(() => {
    setDFP((window.dfp && window.dfp.tryGetFingerPrint()) || '')
  }, [])

  useEffect(() => {
    setSelectCoupon(selectedCOU)
  }, [selectedCOU])

  useEffect(() => {
    if (showRedeemCoupon) {
      setStartRedeem(showRedeemCoupon)
    }
  }, [showRedeemCoupon])

  useEffect(() => {
    setManualRedeem(false)
    setStartRedeem(false)
    setUserCoupon('')
    setErrMsg('')
  }, [tabChanged])

  useEffect(() => {
    let _tagList = []
    uniqueCoupons.map((item) => {
      _tagList.push(item.showDetail)
    })
    setShowConditionTag(_tagList)
  }, [uniqueCoupons])

  useEffect(() => {
    if (selectedPW.coupons && selectedPW.coupons.length > 0) {
      setAvaCoupons(...selectedPW.coupons)
      let _list = []
      selectedPW.coupons.map((item) => {
        _list.push(item.couponCode)
      })
      setAvaCList(_list)
      sortCoupons(_list, uniqueCoupons)
    } else {
      setAvaCList([])
      sortCoupons([], uniqueCoupons)
    }
    setCouponcode(avaCouponCode)
  }, [selectedPW, uniqueCoupons])
  useEffect(() => {
    setSelectCoupon(couponItem)
    getCOU(couponItem)
  }, [selectProd])
  // 对优惠券进行排序
  const sortCoupons = (avaList = [], totalList = []) => {
    // 先算出
    let _avaCoupons = []
    let _NoavaCoupons = []
    totalList.map((item) => {
      if (avaList.indexOf(item.couponCode) > -1) {
        item.noUse = false
        _avaCoupons.push(item)
      } else {
        item.noUse = true
        _NoavaCoupons.push(item)
      }
    })
    _avaCoupons.sort((a, b) => {
      // 金额从大到小
      if (b.discountPrice === a.discountPrice) {
        // 时间从小到大
        return a.couponEndTime - b.couponEndTime
      }
      return b.discountPrice - a.discountPrice
    })
    _NoavaCoupons.sort((a, b) => {
      // 金额从大到小
      if (b.discountPrice === a.discountPrice) {
        // 时间从小到大
        return a.couponEndTime - b.couponEndTime
      }
      return b.discountPrice - a.discountPrice
    })
    // .sort((a, b) => {
    //   // 时间从小到大
    //   return a.couponEndTime - b.couponEndTime
    // })
    setCouponList([..._avaCoupons, ..._NoavaCoupons])
  }
  // 兑换优惠券
  const startRedeemCoupon = async () => {
    // getVipData({}, '1PZZ-LZZ4-EBNL-I4YU-BZHG')
    // return
    if (userCoupon.length < 1) {
      return
    }
    if (redeemLoading) {
      return
    }
    setRedeemLoading(true)
    setErrMsg('')
    const params = {
      timestamp: new Date().getTime(),
      dfp: (window.dfp && window.dfp.tryGetFingerPrint()) || '',
      userAgent: getUa(),
      agentType: getDevice() === 'mobile' ? 479 : 426,
      qyid: getCookies('QC005'),
      lang: getCookies('lang') || 'en_us',
      app_lm: getCookies('mod') || 'intl',
      timeZone: getTimeZone(getCookies('mod')),
      exchangeCode: userCoupon
    }
    const riskRes = await $http(USER_RISK, { params })
    // return setShowFK(true)
    if (riskRes.code === 'A00000') {
      const { data = {} } = riskRes
      setUserRisk(data.risk)
      setRiskLevel(data.riskLevel)
      setRiskCode(data.riskCode)
      setFkToken(data.token)
      if (data.risk === 1) {
        // setShowFK(true)
        redeemCoupon(data)
      } else if (data.risk === 2) {
        setShowFK(true)
      }
    } else {
      setRedeemLoading(false)
      setErrMsg(riskRes.message || 'Error')
    }
  }
  // 请求兑换接口
  const redeemCoupon = async (fkData) => {
    const params = {
      timestamp: new Date().getTime(),
      dfp: (window.dfp && window.dfp.tryGetFingerPrint()) || '',
      userAgent: getUa(),
      agentType: getDevice() === 'mobile' ? 479 : 426,
      qyid: getCookies('QC005'),
      lang,
      app_lm: getCookies('mod') || 'intl',
      timeZone: getTimeZone(getCookies('mod')),
      exchangeCode: userCoupon,
      token: fkData.token,
      preRiskLevel: fkData.riskLevel,
      preRiskCode: fkData.riskCode
    }
    const exchangeResult = await $http(EXCHANGE_COUPON, {
      timeout: 50000,
      params
    })
    setShowFK(false)
    setRedeemLoading(false)

    if (exchangeResult.code === 'A00000') {
      let resData = exchangeResult.data
      setRedeemSuccess(true)
      setTimeout(() => {
        setRedeemSuccess(false)
        getVipData({}, resData.couponCode)
      }, 800)
    } else {
      setErrMsg(exchangeResult.message || 'Error')
    }
    // console.log('兑换结果是>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>: ', exchangeResult)
  }

  const verifySliderSus = () => {
    redeemCoupon({
      token: fkToken,
      riskLevel,
      riskCode
    })
  }
  const couponChange = (e) => {
    let key = e.target.value
    let codeStr = key.trim()
    // codeStr = codeStr
    //   .replace(/[^A-Za-z0-9]/g, '')
    //   .replace(/([A-Za-z0-9]{4})(?=[^$])/g, '$1-')
    setUserCoupon(codeStr)
  }
  const clickShowDetail = (event, index) => {
    event.stopPropagation()
    showConditionTag[index] = !showConditionTag[index]
    setShowConditionTag([...showConditionTag])
  }

  const showCoupon = () => {
    if (!uniqueCoupons || (uniqueCoupons && uniqueCoupons.length < 1)) {
      return
    }
    if (showCouponTag) {
      setShowCouponTag(false)
    } else {
      setShowCouponTag(true)
    }
  }

  const hideCoupon = () => {
    setShowCouponTag(false)
  }

  const showStartRedeem = () => {
    if (!isLogin()) {
      startLogin()
      return
    }
    // setManualRedeem(true)
    setStartRedeem(true)
  }
  const cancelRedeem = () => {
    setManualRedeem(false)
    setStartRedeem(false)
    setErrMsg('')
    setUserCoupon('')
  }
  return (
    <CouponWrapper>
      {redeemSuccess ? (
        <Toast>
          <div className="success-wrapper">
            <img
              src="//www.iqiyipic.com/new-vip/icon_pay_success.png"
              className="success-icon"
              alt=""
            />
            <p className="sucees-tip">
              {vipLangPkg.PCW_VIP_1650008700211_949 || ' Redeem Sccessful！'}
            </p>
          </div>
        </Toast>
      ) : (
        ''
      )}

      <div className="coupon-text-wrapper">
        <p className="coupon-text">{vipLangPkg.PCW_VIP_1648808132758_351}</p>
        {!startRedeem ? (
          <p
            className="redeem-btn"
            rseat="redeem"
            data-pb={`rpage=cashier_popup&block=coupon&${pbInfoStr}`}
            onClick={showStartRedeem}
          >
            {vipLangPkg.PCW_VIP_1648808183025_426}
          </p>
        ) : (
          <p className="cancel-btn" onClick={cancelRedeem}>
            {vipLangPkg.PCW_VIP_1645426708050_573}
          </p>
        )}
      </div>
      {startRedeem ? (
        <div className="redeem-coupon-wrapper">
          {errMsg ? (
            <div className="risk-error">
              <img
                src="//www.iqiyipic.com/new-vip/icon_error.png"
                alt=""
                className="risk-err-icon"
              />
              <p className="risk-err-tip">{errMsg || ''}</p>
            </div>
          ) : (
            ''
          )}
          {showFK ? (
            <div className="ibd-fengkong-wrapper">
              <div className="ibd-fengkong-outer">
                <div id="sign_fengkong"></div>
                <Fengkong
                  id="sign_fengkong"
                  dfp={dfp}
                  lang={lang}
                  token={fkToken}
                  ptid={getPtid()}
                  verifySliderSus={verifySliderSus}
                ></Fengkong>
              </div>
            </div>
          ) : (
            ''
          )}
          <div className="banknumber-input-wrapper">
            <div
              className={`banknumber-input-label ${
                showRedeemCoupon ? 'shake-border' : ''
              }`}
            >
              <div className="coupon-input-wrapper">
                <input
                  v-if="isAutocomplete === 'off'"
                  type="text"
                  style={{ display: 'none' }}
                />
                <input
                  v-if="isAutocomplete === 'off'"
                  type="password"
                  autoComplete="new-password"
                  style={{ display: 'none' }}
                />
                <input
                  type="type"
                  className="banknumber-input-input"
                  autoComplete="on"
                  placeholder=" "
                  onChange={(e) => couponChange(e)}
                  value={userCoupon || ''}
                />
                <span className="banknumber-input-desc">
                  {vipLangPkg.PCW_VIP_1645426739490_954}
                </span>
              </div>

              <div
                className={`start-redeem ${
                  redeemLoading ? 'start-reedem-loading' : ''
                }`}
                onClick={() => {
                  startRedeemCoupon()
                }}
                rseat="exchange"
                data-pb={`rpage=cashier_popup&block=coupon&${pbInfoStr}`}
              >
                {redeemLoading ? (
                  <i className="redeem-loading"></i>
                ) : (
                  vipLangPkg.PCW_VIP_1645426681493_976
                )}
              </div>
            </div>
            <div className="coupon-input-tip">
              {vipLangPkg.PCW_VIP_1648809297622_465}
            </div>
          </div>
        </div>
      ) : (
        <div
          className={`coupon-wrapper `}
          onClick={() => {
            showCoupon()
          }}
          ref={wrapperRef}
        >
          {!isLogin() ? (
            <p
              className="coupon-title"
              onClick={() => {
                startLogin()
              }}
            >
              {vipLangPkg.PCW_VIP_1645413329350_938}
            </p>
          ) : (
            <>
              {couponList && couponList.length > 0 ? (
                <>
                  {avaCList.length > 0 ? (
                    <p className="coupon-title selected-coupon">
                      
                      {selectCoupon.couponName}
                      {mod === 'vn' ? (
                        <span className="selected-coupon-price">
                          -{(selectCoupon.discountPrice / 100).toFixed(2)}
                          {selectCoupon.currencySymbol || ''}
                        </span>
                      ) : (
                        <span className="selected-coupon-price">
                          -{selectCoupon.currencySymbol || ''}
                          {(selectCoupon.discountPrice / 100).toFixed(2)}
                        </span>
                      )}
                    </p>
                  ) : (
                    <p className="coupon-title">
                      {vipLangPkg.PCW_VIP_1645413402347_269} (
                      {couponList.length})
                    </p>
                  )}
                </>
              ) : (
                <p
                  className={`coupon-title ${
                    couponList.length < 1 ? 'no-coupons-title' : ''
                  }`}
                >
                  {vipLangPkg.PCW_VIP_1645413299826_126}({couponList.length})
                </p>
              )}
            </>
          )}

          <i
            alt=""
            className={`arrow-show ${showCouponTag ? 'icon-down' : ''} ${
              couponList.length < 1 ? 'no-coupons-icon' : ''
            }`}
          ></i>
          <ul
            className="coupon-list"
            style={{ display: `${showCouponTag ? 'block' : 'none'}` }}
          >
            {couponList &&
              couponList.map((item, index) => {
                return (
                  <li
                    className={`coupon-item ${
                      item.couponCode === selectCoupon.couponCode
                        ? 'coupon-item-focus'
                        : ''
                    } ${
                      (item.couponStatus !== 1 && item.couponStatus !== 2) ||
                      avaCList.indexOf(item.couponCode) < 0
                        ? 'disabled-coupon-item'
                        : ''
                    }`}
                    key={index}
                    onClick={(event) => {
                      choseCoupon(event, item, index)
                    }}
                  >
                    <div className="coupon-top">
                      <div className="left-content">
                        <div className="coupon-item-title">
                          {item.couponName}
                        </div>
                        <div
                          className={`condition-btn ${
                            (item.couponStatus !== 1 &&
                              item.couponStatus !== 2) ||
                            avaCList.indexOf(item.couponCode) < 0
                              ? 'disable-condition-btn'
                              : ''
                          }`}
                          onClick={(event) => {
                            clickShowDetail(event, index)
                          }}
                        >
                          {avaCList.indexOf(item.couponCode) > -1
                            ? vipLangPkg.PCW_VIP_1648807452395_472
                            : vipLangPkg.PCW_VIP_1648807536784_736}
                          <i
                            alt=""
                            className={`condition-arrow ${
                              showConditionTag[index] ? 'icon-down' : ''
                            }`}
                          ></i>
                        </div>
                      </div>
                      <div className="right-content">
                        {item.couponType === 1 ? (
                          <div className="coupon-discount">
                            {mod === 'vn'
                              ? `-
                            ${Number(
                              (item.discountPrice / 100).toFixed(2)
                            ).toString()}${item.currencySymbol || ''}`
                              : `-${item.currencySymbol || ''}
                            ${Number(
                              (item.discountPrice / 100).toFixed(2)
                            ).toString()}`}
                          </div>
                        ) : (
                          <div className="coupon-discount">
                            {item.allowance}%
                          </div>
                        )}
                      </div>
                    </div>

                    <section
                      className="condition-detail"
                      style={{
                        maxHeight: showConditionTag[index] ? '240px' : '0',
                        paddingTop: showConditionTag[index] ? '12px' : '0'
                      }}
                    >
                      {new Date().getTime() < item.couponStartTime ? (
                        <p className="condition-detail-item">
                          • {cashierLangPkg.PCW_CASHIER_1650957184149_960}:
                          {formatDate(item.couponStartTime, 'yyyy-MM-dd')}
                        </p>
                      ) : (
                        ''
                      )}
                      <p className="condition-detail-item">
                        • {cashierLangPkg.PCW_CASHIER_1650957274891_660}:
                        {formatDate(item.couponEndTime, 'yyyy-MM-dd')}
                      </p>
                      {item.priceFloor > 0 ? (
                        <p className="condition-detail-item">
                          •{' '}
                          {(vipLangPkg.PCW_VIP_1649664804054_490 || '').replace(
                            '%s',
                            (item.priceFloor / 100).toFixed(2)
                          )}
                        </p>
                      ) : (
                        <p className="condition-detail-item">
                          • {cashierLangPkg.PCW_CASHIER_1650957940678_189}
                        </p>
                      )}

                      {item.vipSceneDesc ? (
                        <p className="condition-detail-item">
                          • {item.vipSceneDesc}
                        </p>
                      ) : (
                        ''
                      )}
                      {item.productSetSceneDesc ? (
                        <p className="condition-detail-item">
                          • {item.productSetSceneDesc}
                        </p>
                      ) : (
                        ''
                      )}
                      {item.autorenewSceneDesc ? (
                        <p className="condition-detail-item">
                          • {item.autorenewSceneDesc}
                        </p>
                      ) : (
                        ''
                      )}
                      {item.payChannelSceneDesc ? (
                        <p className="condition-detail-item">
                          • {item.payChannelSceneDesc}
                        </p>
                      ) : (
                        ''
                      )}
                      {item.customDesc ? (
                        <p className="condition-detail-item">
                          • {item.customDesc}
                        </p>
                      ) : (
                        ''
                      )}
                    </section>
                  </li>
                )
              })}
          </ul>
        </div>
      )}
    </CouponWrapper>
  )
}

export default Coupon
