import React from 'react'

export const RedeemIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="20px"
      height="20px"
      viewBox="0 0 20 20"
      version="1.1"
    >
      <g
        id="V1.8.0_UI_2504_Purchase-popup"
        stroke="none"
        strokeWidth="1"
        fill="none"
        fillRule="evenodd"
      >
        <g id="2504-1_plan-A" transform="translate(-770.000000, -87.000000)">
          <g id="Group-19" transform="translate(770.000000, 87.000000)">
            <rect id="Rectangle" x="0" y="0" width="20" height="20" />
            <rect
              id="Rectangle"
              stroke="#A7763A"
              strokeWidth="1.33333333"
              x="1.5"
              y="5.83333333"
              width="17"
              height="11.1666667"
              rx="1.66666667"
            />
            <rect
              id="Rectangle"
              fill="#A7763A"
              x="1.66666667"
              y="9.33333333"
              width="16.6666667"
              height="1.33333333"
            />
            <rect
              id="Rectangle-Copy-12"
              fill="#A7763A"
              x="11.6666667"
              y="13.75"
              width="5"
              height="1.33333333"
              rx="0.666666667"
            />
            <path
              d="M5.95759422,5.83333333 L8.36371372,3.4017055 C9.10586991,2.65168138 10.3155211,2.64530316 11.0655452,3.38745936 C11.1754881,3.49624899 11.2718135,3.61798691 11.3524062,3.75 C11.7465598,4.39563452 11.5426942,5.2385493 10.8970597,5.63270287 C10.6821332,5.76391343 10.4351942,5.83333333 10.1833815,5.83333333"
              id="Path-2"
              stroke="#A7763A"
              strokeWidth="1.33333333"
            />
            <path
              d="M1.66666667,5.83333333 L3.45287586,3.90784471 C3.98305452,3.33632543 4.87615717,3.30281224 5.44767646,3.8329909 C5.5524208,3.93015861 5.64180572,4.04266584 5.71277568,4.16666667 C6.01826052,4.70041885 5.83321327,5.38075488 5.29946109,5.68623972 C5.13105013,5.78262714 4.9403751,5.83333333 4.74633185,5.83333333"
              id="Path-2-Copy"
              stroke="#A7763A"
              strokeWidth="1.33333333"
              transform="translate(4.166667, 4.166667) scale(-1, 1) translate(-4.166667, -4.166667) "
            />
          </g>
        </g>
      </g>
    </svg>
  )
}

export const AlertIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20px"
    height="20px"
    viewBox="0 0 20 20"
    version="1.1"
  >
    <path d="M10,0 C15.5228475,0 20,4.4771525 20,10 C20,15.5228475 15.5228475,20 10,20 C4.4771525,20 0,15.5228475 0,10 C0,4.4771525 4.4771525,0 10,0 Z M10.5,13 L9.5,13 C9.22385763,13 9,13.2238576 9,13.5 L9,13.5 L9,14.5 C9,14.7761424 9.22385763,15 9.5,15 L9.5,15 L10.5,15 C10.7761424,15 11,14.7761424 11,14.5 L11,14.5 L11,13.5 C11,13.2238576 10.7761424,13 10.5,13 L10.5,13 Z M10.5,5 L9.5,5 C9.22385763,5 9,5.22385763 9,5.5 L9,5.5 L9,11.5 C9,11.7761424 9.22385763,12 9.5,12 L9.5,12 L10.5,12 C10.7761424,12 11,11.7761424 11,11.5 L11,11.5 L11,5.5 C11,5.22385763 10.7761424,5 10.5,5 L10.5,5 Z" />
  </svg>
)
export const ErrorIcon = () => (
  <svg
    width="24px"
    height="24px"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g transform="translate(2, 2)">
      <path d="M10,0 C15.5228475,0 20,4.4771525 20,10 C20,15.5228475 15.5228475,20 10,20 C4.4771525,20 0,15.5228475 0,10 C0,4.4771525 4.4771525,0 10,0 Z M10,2 C5.581722,2 2,5.581722 2,10 C2,14.418278 5.581722,18 10,18 C14.418278,18 18,14.418278 18,10 C18,5.581722 14.418278,2 10,2 Z M10.5,8 C10.7761424,8 11,8.22385763 11,8.5 L11,14.5 C11,14.7761424 10.7761424,15 10.5,15 L9.5,15 C9.22385763,15 9,14.7761424 9,14.5 L9,8.5 C9,8.22385763 9.22385763,8 9.5,8 L10.5,8 Z M10.5,5 C10.7761424,5 11,5.22385763 11,5.5 L11,6.5 C11,6.77614237 10.7761424,7 10.5,7 L9.5,7 C9.22385763,7 9,6.77614237 9,6.5 L9,5.5 C9,5.22385763 9.22385763,5 9.5,5 L10.5,5 Z" />
    </g>
  </svg>
)
