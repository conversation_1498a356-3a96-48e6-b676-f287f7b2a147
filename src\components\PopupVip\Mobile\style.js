import styled from 'styled-components'

const Style = styled.div`
  width: 504px;
  min-height: 360px;
  margin: 0 auto;
  .block {
    margin-top: 16px;
    padding-bottom: 16px;
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    line-height: 18px;
  }
  h1 {
    font-size: 24px;
    color: #222222;
    margin-bottom: 16px;
    font-weight: 800;
    text-align: center;
  }
  .paytype {
    display: flex;
    align-items: center;
    color: #222;
    font-size: 20px;
    font-weight: bold;
  }
  .icon {
    height: 24px;
  }
  .mobile {
    margin-top: 16px;
    span {
      margin-right: 12px;
    }
  }
  .mobile-title {
    color: #222222;
  }
  .mobile-input {
    margin-top: 8px;
    height: 44px;
    line-height: 44px;
    color: #222;
    position: relative;
    label {
      margin: 0 12px;
      width: 30px;
      text-align: center;
      position: absolute;
    }
    input {
      width: 100%;
      height: 44px;
      border: 1px solid #e6e6e6;
      line-height: 44px;
      border-radius: 4px;
      padding-left: 54px;
      box-sizing: border-box;
      &:active {
        border: 1px solid #edb97b;
      }
      &:focus {
        border: 1px solid #edb97b;
      }
      &.input-error {
        border: 1px solid #fe3d33;
      }
    }
  }
  .mobile-error {
    color: #fe3d33;
    margin-top: 8px;
  }
  .mobile-number {
    margin-top: 16px;
    font-size: 16px;
    font-weight: bold;
    color: #222222;
    display: flex;
  }
  .change-btn {
    color: #e09e51;
    font-size: 14px;
    cursor: pointer;
    font-weight: normal;
    &:hover {
      text-decoration: underline;
    }
  }
  p.desc {
    margin-top: 16px;
  }
  .error {
    min-height: 44px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    padding: 11px 15px;
    background: #FEEBEB;
    border: 1px solid #FEC5C2;
    border-radius: 4px;
    font-size: 14px;
    color: #FE3D33;
    margin: 12px 0;
    svg {
      fill: #FE3D33;
      margin-right: 8px;
    }
  }
  .loading {
    position: absolute;
    top: 183px;
    left: 50%;
    transform: translateX(-50%);
    width: 120px;
    box-sizing: border-box;
    height: 90px;
    padding: 16px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    background: #37383D;
    font-size: 14px;
    color: #ECECEC;
    border-radius: 4px;
    .loading-img {
      width: 30px;
      height: 30px;
      background-image: url('https://www.iqiyipic.com/common/fix/global-img/<EMAIL>');
      background-size: 301px auto;
      background-position: -252.5px -29px;
      animation: rotate 1s linear infinite;
    }
    @keyframes rotate {
      from {
        transform: rotate(0deg);
      }
      to {
        transform: rotate(360deg);
      }
    }
  }
  @media screen and (max-width: 1023px) {
    width: 532px;
  }
  @media screen and (max-width: 767px) {
    width: auto;
    margin: 0 12px;
  }
`
export default Style
