export const isServer = typeof window === 'undefined'

export function getUa() {
  if (isServer) {
    return global.deviceAgent
  }
  return navigator.userAgent.toLowerCase()
}

// 输出pc | mobile
export function getDevice() {
  const ua = getUa()
  let deviceTag = 'pc'
  if (
    /(android)|(like mac os x)/i.test(ua) ||
    (/(intel mac os x)/i.test(ua) && !isServer && 'ontouchend' in document)
  ) {
    deviceTag = 'mobile'
  }
  return deviceTag
}
// http://wiki.qiyi.domain/pages/viewpage.action?pageId=869799749
export const iqSwitchPlatformId = () => {
  if (getDevice() === 'pc') {
    return 47
  } else {
    // 移动端
    return 48
  }
}
// 输出 pc | android | iphone | ipad
export function getMobileType() {
  const ua = getUa()
  let deviceTag = 'pc'
  if (/(android)/i.test(ua)) {
    if (/(mobile)/i.test(ua)) {
      deviceTag = 'android'
    } else {
      deviceTag = 'ipad'
    }
  } else if (
    /(like mac os x)/i.test(ua) ||
    (/(intel mac os x)/i.test(ua) && !isServer && 'ontouchend' in document)
  ) {
    if (/(iphone)/i.test(ua)) {
      deviceTag = 'iphone'
    } else {
      deviceTag = 'ipad'
    }
  }
  return deviceTag
}

export function getOs() {
  const isMac = /mac/i.test(navigator.platform)
  if (isMac) {
    return 'Mac'
  }
  return ''
}

export function getDeviceByScreen() {
  return document.body.clientWidth < 1000 ? 'mobile' : 'pc'
}

export function detectH5Ua() {
  const ua = getUa()
  const os = {}
  const browser = {}
  const webkit = ua.match(/webkit[/]{0,1}([\d.]+)/)
  const android = ua.match(/(android);?[\s/]+([\d.]+)?/)
  const ipad = ua.match(/(ipad).*os\s([\d_]+)/)
  const ipod = ua.match(/(ipod)(.*os\s([\d_]+))?/)
  const iphone = !ipad && ua.match(/(iphone\sos)\s([\d_]+)/)
  const chrome = ua.match(/chrome\/([\d.]+)/) || ua.match(/crios\/([\d.]+)/)
  const webview =
    !chrome && ua.match(/(iphone|ipod|ipad).*applewebkit(?!.*safari)/)
  const weixin = /MicroMessenger/.test(ua)
  const iosEgt9 =
    /iphone os (\d+)/.exec(ua) && /iphone os (\d+)/.exec(ua)[1] >= 9

  // eslint-disable-next-line no-cond-assign
  if ((browser.webkit = !!webkit)) {
    browser.version = webkit[1]
  }

  if (android) {
    os.android = true
    os.version = android[2]
  }
  if (iphone && !ipod) {
    os.ios = true
    os.iphone = true
    os.version = iphone[2].replace(/_/g, '.')
  }
  if (ipad) {
    os.ios = true
    os.ipad = true
    os.version = ipad[2].replace(/_/g, '.')
  }
  if (ipod) {
    os.ios = true
    os.ipod = true
    os.version = ipod[3] ? ipod[3].replace(/_/g, '.') : null
  }
  if (webview) browser.webview = true
  if (iosEgt9) os.iosEgt9 = true
  if (chrome) {
    browser.chrome = true
    browser.version = chrome[1]
  }
  if (weixin) browser.weixin = true

  return {
    os,
    browser
  }
}
