export default function intersectionObserver(ele, container, callback) {
  // 使用IntersectionObserver
  if (
    'IntersectionObserver' in window &&
    'IntersectionObserverEntry' in window &&
    'intersectionRatio' in window.IntersectionObserverEntry.prototype
  ) {
    const observer = new window.IntersectionObserver(entries => {
      if (entries[0].intersectionRatio > 0) {
        console.log('进入可视区域')
      } else {
        console.log('移出可视区域')
      }
    })
    observer.observe(ele)
  }

  window.addEventListener('scroll', callback, true)
}
