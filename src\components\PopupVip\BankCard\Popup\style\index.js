import styled from 'styled-components'

const PopupWrapper = styled.div`
  .global-popup {
    .popup-mask {
      position: fixed;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      z-index: 99;
    }
    .popup-fixed {
      position: fixed;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      width: 400px;
      border-radius: 4px;
      background-color: #fff;
      box-shadow: 0 3px 10px 0 rgba(0, 0, 0, 0.2);
      z-index: 999;
    }
    .popup-content {
      position: relative;
    }
    .popup-close {
      position: absolute;
      right: 16px;
      top: 16px;
      width: 24px;
      height: 24px;
      background-image: url(https://www.iqiyipic.com/common/fix/global-img/<EMAIL>);
      background-size: 500px auto;
      background-position: -120px -100px;
      cursor: pointer;
    }
    .popup-con {
      text-align: center;
      padding: 48px 40px 40px;
    }
  }
  /* 银行卡支付 css */
  .popup-bankcard-tips {
    position: relative;
    .icon-wait {
      display: block;
      margin: 0 auto;
      width: 49px;
      height: 49px;
      background-image: url(https://www.iqiyipic.com/common/fix/global-img/<EMAIL>);
      background-size: 500px auto;
      background-position: -419.5px -49.5px;
      margin-bottom: 24px;
      animation: rotate 1s linear infinite;
    }
    .load-sec {
      position: absolute;
      top: 10px;
      left: 50%;
      width: 30px;
      height: 30px;
      transform: translate(-50%, 0);
      font-size: 16px;
      line-height: 26px;
    }
    @keyframes rotate {
      from {
        transform: rotate(0deg);
      }
      to {
        transform: rotate(360deg);
      }
    }
    .tips-major {
      color: #222222;
      font-size: 16px;
      font-weight: 500;
      line-height: 26px;
      margin-bottom: 10px;
    }
    .tips-minor {
      color: #666666;
      font-size: 14px;
      line-height: 20px;
    }
  }
`
export default PopupWrapper
