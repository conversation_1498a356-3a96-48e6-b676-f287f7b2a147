/**
 * @desc 新版本收银台购买模块
 * <AUTHOR>
 * @time 2022-02-18
 * @feature  GLOBALREQ-6796
 */

import React, { useEffect, useState, useCallback } from 'react'
import { getCookies } from '@/kit/cookie'
import BuyBtnWrapper from './style/index'
import { isLogin } from '@/utils/userInfo'
import { sendClickPb } from '@/utils/pingBack'
import UnlockCoupon from '../UnlockCoupon/index'
const mod = getCookies('mod') || 'intl'
const BuyBtn = React.memo((props) => {
  const {
    pbInfo,
    vipLangPkg,
    agreeList = {},
    selectProd,
    selectedPW,
    handleCreateVipOrder,
    selectedCOU,
    collectInfo,
    showFreeze,
    setHideFreeze,
    startLogin,
    pageInfo,
    createOrdering
  } = props
  const [showFreezePop, setShowFreezePop] = useState({ showFreeze })
  // 避免多次下单
  const [orderLoading, setOrderLoading] = useState(false)
  const [buyPbInfo, setBuyPbInfo] = useState({})
  const [inputData, setInputData] = useState({})
  const [pageData, setPageData] = useState(pageInfo)
  const [agreeChecked, setAgreeChecked] = useState(false)
  const [showCheckTip, setShowCheckTip] = useState(false)
  const checkoutRef = React.useRef()
  // useEffect(() => {
  //   setAgreeObj([selectVipTag])
  // }, [selectVipTag])
  useEffect(() => {
    setShowFreezePop(showFreeze)
  }, [showFreeze])
  useEffect(() => {
    return () => {
      setOrderLoading(false)
    }
  }, [])
  useEffect(() => {
    setBuyPbInfo({
      cashier_type: 'popup',
      abtest: pbInfo.abtest,
      fc: pbInfo.fc || '',
      fv: pbInfo.fv || '',
      v_pid: selectProd && selectProd.pkgItem ? selectProd.pkgItem.pid : '',
      v_prod:
        selectProd && selectProd.pkgItem
          ? selectProd.pkgItem.productSetCode
          : '',
      pay_type: selectedPW ? selectedPW.payType : ''
    })
  }, [selectProd, selectedPW])
  useEffect(() => {
    setPageData(pageInfo)
    setAgreeChecked(agreeList.agreementSelected === 1)
  }, [pageInfo])
  const createOrder = async () => {
    sendClickPb({
      block: 'extend_info',
      rseat: 'continue',
      rpage: 'cashier_popup',
      tjPb: {
        ...buyPbInfo
      }
    })
    let _checkedVal = checkoutRef.current && checkoutRef.current.checked
    if (agreeList.showAgreement === 1 && !_checkedVal) {
      setShowCheckTip(true)
      return
    }
    if (!isLogin()) {
      startLogin()
      return
    }
    const inputInfo = collectInfo(selectedPW.payType)
    setInputData(inputInfo)
    const { cardNoErr, phoneNoErr, hasCard,boundCardInfo } = inputInfo
    if (selectedPW.payType === 10010 || selectedPW.payType === 10009) {
      if (!hasCard) {
        if (
          !cardNoErr &&
          (selectedPW.payType === 10010 || selectedPW.payType === 10009)
        ) {
          return
        }
      } else {
        if (!boundCardInfo.cardId) {
          return 
        }
      }
    }
    let payPrice =
      selectedCOU.discountPrice > 0
        ? ((selectedPW.needPayFee - selectedCOU.discountPrice) / 100).toFixed(2)
        : (selectedPW.needPayFee / 100).toFixed(2)
    let data = await handleCreateVipOrder(
      selectProd.pkgItem,
      selectedPW,
      selectedCOU,
      inputInfo,
      payPrice
    )
  }
  const hideFreeze = useCallback(() => {
    setShowFreezePop(false)
    setHideFreeze()
  }, [])
  const clickBox = (e) => {
    let _value = e.target.checked

    setAgreeChecked(_value)
    if (_value) {
      setShowCheckTip(false)
    }
  }
  return (
    <BuyBtnWrapper>
      {agreeList && agreeList.showAgreement === 1 ? (
        <div className="agree-wrapper">
          {showCheckTip ? (
            <div className="agree-tip-wrapper">
              <p className="agree-tip">
                {vipLangPkg.pcashier_payment_iagree_notice}
              </p>
            </div>
          ) : (
            ''
          )}

          <input
            type="checkbox"
            name="agreeBox"
            value={agreeChecked}
            defaultChecked={agreeList.agreementSelected === 1}
            className={`checkbox-input ${showCheckTip ? 'err-check-box' : ''}`}
            onChange={clickBox}
            ref={checkoutRef}
          />
          <p className="agree-title">
            {vipLangPkg.PCW_VIP_1645426795985_929.replace('%s', '')}
          </p>
          <a
            className="agree-btn"
            target="_blank"
            href={agreeList.agreementUrl}
          >
            {agreeList.agreementText}
          </a>
        </div>
      ) : (
        ''
      )}

      <div className="button-wrapper">
        <div className="final-price">
          {mod === 'vn' ? (
            <p className="origin-price">
              {selectProd.pkgItem
                ? `${
                    selectedCOU.discountPrice > 0
                      ? Number(
                          (selectedPW.needPayFee - selectedCOU.discountPrice) /
                            100
                        )
                          .toFixed(2)
                          .toString()
                      : Number(
                          (selectedPW.needPayFee / 100).toFixed(2)
                        ).toString()
                  }${selectProd.pkgItem.currencySymbol} `
                : ''}
            </p>
          ) : (
            <p className="origin-price">
              {selectProd.pkgItem
                ? `${selectProd.pkgItem.currencySymbol} ${
                    selectedCOU.discountPrice > 0
                      ? Number(
                          (selectedPW.needPayFee - selectedCOU.discountPrice) /
                            100
                        )
                          .toFixed(2)
                          .toString()
                      : Number(
                          (selectedPW.needPayFee / 100).toFixed(2)
                        ).toString()
                  }`
                : ''}
            </p>
          )}

          {selectProd.pkgItem &&
          selectProd.pkgItem.price -
            selectedPW.needPayFee +
            selectedCOU.discountPrice >
            0 ? (
            <p className="price-off">
              {mod === 'vn'
                ? (vipLangPkg.PCW_VIP_1648886955865_929 || '%s off').replace(
                    '%s',
                    `${Number(
                      (
                        (selectProd.pkgItem.price -
                          selectedPW.needPayFee +
                          selectedCOU.discountPrice) /
                        100
                      ).toFixed(2)
                    ).toString()}${selectedCOU.currencySymbol || ''}`
                  )
                : (vipLangPkg.PCW_VIP_1648886955865_929 || '%s off').replace(
                    '%s',
                    `${selectedCOU.currencySymbol || ''} ${Number(
                      (
                        (selectProd.pkgItem.price -
                          selectedPW.needPayFee +
                          selectedCOU.discountPrice) /
                        100
                      ).toFixed(2)
                    ).toString()}`
                  )}
            </p>
          ) : (
            ''
          )}
        </div>
        <div
          className="buy-btn"
          onClick={() => {
            window.gtag &&
              window.gtag('event', 'click_pay_button', {
                mod: mod || 'intl',
                rpage: 'cashier_popup'
              })
            createOrder()
          }}
        >
          {createOrdering ? (
            <img
              className="buy-loading"
              src="//www.iqiyipic.com/new-vip/blackloading.png"
              alt=""
            />
          ) : (
            <p className="buy-btn-text">
              {vipLangPkg.PCW_VIP_1625052080309_194}
            </p>
          )}
        </div>
      </div>
      {showFreezePop ? (
        <UnlockCoupon
          vipLangPkg={vipLangPkg}
          createOrder={createOrder}
          hideFreeze={hideFreeze}
          couponCode={selectedCOU.couponCode}
        ></UnlockCoupon>
      ) : (
        ''
      )}
    </BuyBtnWrapper>
  )
})

export default BuyBtn
