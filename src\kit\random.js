/**
 * 生成随机串。
 * @param {Number} length 指定生成的随机串长度。
 * @return {String} 返回生成的随机串。
 */
 const generateRandomString = (length = 10) => {
    let text = "";
    const possible = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";

    for (let i = 0; i < length; i++) {
        text += possible.charAt(Math.floor(Math.random() * possible.length));
    }

    return text;
}

export default generateRandomString