import styled from 'styled-components'

const Style = styled.div`
  display: flex;
  margin: 16px 0 16px;
  .card-warp {
    position: relative;
  }
  .tag {
    box-sizing: border-box;
    display: inline-block;
    position: absolute;
    top: -4px;
    margin-left: 24px;
    padding: 1px 6px;
    /* top: -4px;
    left: 24px; */
    border-radius: 4px;
    border-top-right-radius: 0;
    border-top-left-radius: 0;
    max-width: 210px;
    background: #F2BF83;
    box-shadow: 0px 1px 4px 0px rgba(242,191,131,0.5);
    font-size: 12px;
    color: #111319;
    line-height: 14px;
    font-weight: 400;
    & > span {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      width: 100%;
      display: inline-block;
      vertical-align: middle;
    }
    &:before {
      display: block;
      content: ' ';
      border-top: 3px solid transparent;
      border-bottom: 3px solid transparent;
      border-right: 3px solid #91724E;
      position: absolute;
      left: -2px;
      top: 0px;
      transform: rotate(225deg);
    }
  }
  .pkg-card {
    /* position: relative; */
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 258px;
    height: 100%;
    padding: 24px;
    margin-right: 16px;
    border-radius: 6px;
    background: #FFFFFF;
    border: 1px solid #E9E9E9;
    box-shadow: 0px 1px 4px 0px rgba(0,0,0,0.02);
    outline: none;
    &-main {
      /* height: 184px; */
      /* border-bottom: 1px solid #E9E9E9; */
    }
    &:hover {
      padding: 23px;
      /* box-shadow: 0px 1px 4px 0px rgba(0,0,0,0.02); */
      background: #FFFFFF;
      border: 2px solid #EDB97B;
      box-shadow: 0px 1px 4px 0px rgba(0,0,0,0.02);
      .vip-text {
        .icon {
          background: url('//www.iqiyipic.com/lequ/20210625/icon-plan-gold-hover.png') no-repeat;
          background-size: cover;
        }
      }
    }
    &.select {
      padding: 22px;
      background: #FCF2E6;
      border: 3px solid #EDB97B;
      box-shadow: 0px 1px 4px 0px rgba(0,0,0,0.02);
      .vip {
        border-top: 1px solid #E2D9CE;
      }
      .vip-text {
        .icon {
          background: url('//www.iqiyipic.com/lequ/20210625/icon-plan-gold-hover.png') no-repeat;
          background-size: cover;
        }
      }
      /* padding: 30px 13px; */
    }
    .title {
      font-weight: 700;
      font-size: 14px;
      line-height: 16px;
      color: #222222;
    }
    .sub-title {
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      white-space: normal;
    }
    .price {
      margin-top: 12px;
      display: flex;
      align-items: flex-end;
      flex-wrap: wrap;
      min-height: 30px;
      max-height: 45px;
      color: #222222;
    }
    .real {
      margin-right: 7px;
      font-weight: bold;
    }
    .unit {
      line-height: 24px;
      font-size: 16px;
    }
    .real-price {
      line-height: 29px;
      font-size: 24px;
    }
    .origin-price {
      font-size: 13px;
      color: #666666;
      line-height: 22px;
      text-decoration: line-through;
    }
    .detail {
      margin: 12px 0 16px;
      color: #666;
      line-height: 15px;
      font-size: 13px;
      font-weight: 400;
      /* transform: scale(0.8333) translateX(-10.333%) translateY(-8.333%); */
      & > p {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 4;
        -webkit-box-orient: vertical;
        white-space: normal;
      }
    }
    .vip-text {
      margin-top: 4px;
      color: #666666;
      font-size: 13px;
      line-height: 15px;
      font-weight: 400;
      & > div {
        margin-bottom: 4px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        white-space: normal;
        &:last-child {
          margin-bottom: 0;
        }
      }
      .icon {
        display: inline-block;
        width: 13px;
        height: 13px;
        margin-right: 8px;
        vertical-align: bottom;
        background: url('//www.iqiyipic.com/lequ/20210624/icon-plan-gold.png') no-repeat;
        background-size: cover;
      }
      /* transform: scale(0.8333) translateX(-10.333%) translateY(-8.333%); */
    }
    .vip {
      border-top: 1px solid #E9E9E9;
      padding-top: 16px;
    }
  }
`
export default Style
