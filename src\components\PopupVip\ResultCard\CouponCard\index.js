import React, { useState, useEffect } from 'react'
import { removeProtocol } from '@/kit/url'
import { sendBlockPb } from '@/utils/pingBack'
import $http from '@/kit/fetch'
import { getCookies } from '@/kit/cookie'
import { RESULT_COUPON } from '@/constants/interfaces'
import Toast from '@/components/PopupVip/Toast'
import CouponCardWrapper from './style'

const lang = getCookies('lang') || 'en_us'
const CouponCard = ({ cardData, orderInfo }) => {
  useEffect(() => {
    sendBlockPb('coupon', {
      rpage: 'redirection'
    })
  }, [])
  const {
    title,
    icon,
    ruleDesc,
    currencySymbol,
    discountPrice,
    linkUrl,
    supportAutorenewScene,
    supportSkuScene,
    postButtonTips,
    preButtonTips
  } = cardData
  const [btnLoading, setBtnLoading] = useState(false)
  const [hasCoupon, setHasCoupon] = useState(false)
  const [errorToast, setErrToast] = useState('')
  const [showToast, setShowToast] = useState(false)
  const getCoupon = async () => {
    if (btnLoading) {
      return
    }
    setBtnLoading(true)
    const { batchNo } = cardData
    const options = {
      method: 'GET',
      credentials: true,
      params: {
        requestId: getCookies('QC005') + '_' + new Date().getTime(),
        orderCode: orderInfo.orderCode || '',
        batchNo: batchNo,
        lang: lang
      }
    }
    try {
      const resultData = await $http(RESULT_COUPON, options)
      if (resultData.code === 'A00000') {
        setBtnLoading(false)
        setHasCoupon(true)
      } else {
        setBtnLoading(false)
        setErrToast(resultData.message || 'Error!')
        setShowToast(true)
        setTimeout(() => {
          setShowToast(false)
        }, 800)
      }
    } catch (err) {
      console.log('get coupon error:', err)
    }
  }
  return (
    <CouponCardWrapper>
      <div className="coupon-card-wrapper">
        <div className="coupon-card-header">
          <div className="coupon-card-top">
            <img
              className="coupon-card-img"
              alt=""
              src={
                removeProtocol(icon) ||
                '//www.iqiyipic.com/global-parental/toast-success-icon.png'
              }
            />
            <div className="coupon-card-left">
              <div className="coupon-card-title-wrapper">
                <p className="coupon-card-title">{title}</p>
                <p className="coupon-card-subtitle">{ruleDesc}</p>
              </div>
            </div>
          </div>
          <div className="coupon-card-detail-wrapper">
            <p className="coupon-card-name">
              {currencySymbol}
              {discountPrice}
            </p>
            {hasCoupon ? (
              !linkUrl ||
              (supportAutorenewScene === 2 &&
                supportSkuScene === 2) ? (
                <div
                  className={`coupon-card-btn 
                       disable-coupon-btn
                    `}
                >
                  <p className="coupon-card-btn-title">
                    {postButtonTips}
                  </p>
                </div>
              ) : (
                <a target="_blank" href={linkUrl}>
                  <div className={`coupon-card-btn go-use-coupon`}>
                    <p className="coupon-card-btn-title">
                      {postButtonTips}
                    </p>
                  </div>
                </a>
              )
            ) : (
              <div
                className="coupon-card-btn"
                onClick={getCoupon}
                rseat="receive"
                data-pb="block=coupon&rpage=redirection"
              >
                {btnLoading ? (
                  <img
                    src="//www.iqiyipic.com/new-vip/blackloading.png"
                    alt=""
                    className="coupon-card-btn-loading"
                  />
                ) : (
                  <p className="coupon-card-btn-title">
                    {preButtonTips || 'OK'}
                  </p>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {showToast ? <Toast msg={errorToast} /> : ''}
    </CouponCardWrapper>
  )
}
export default CouponCard
