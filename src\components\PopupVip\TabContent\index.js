/**
 * @desc 新版本收银台会员类型
 * <AUTHOR>
 * @time 2022-02-05
 * @feature  GLOBALREQ-6796
 */

import React, { useState, useEffect, useRef, useCallback } from 'react'
import { Tab<PERSON>ontentWapper, TabListWrapper } from './style/index'
import { getViewWidth } from '@/kit/dom'
import { getCookies } from '@/kit/cookie'
import { initLogin } from '@/utils/loginWindow.js'
import { isLogin } from '@/utils/userInfo'
import { sendBlockPb } from '@/utils/pingBack'
import { formatDate, isEmptyStr } from '@/utils/common'
import VipPrivilege from '../VipPrivilege/index'
import PayWith from '../PayWith/index'
import Coupon from '../Coupon/index'
import BuyBtn from '../BuyBtn/index'
import UserInfo from '../UserInfo'
import DiscountPop from '../DiscountPop'
import NumberAnimation from './animationNumber'
import Slider from './slider'
const mod = getCookies('mod')
const TabContent = (props) => {
  // console.log('TabContent ====zhxingle')
  const {
    vipLangPkg = {},
    cashierLangPkg = {},
    vipInfo = {},
    userCards,
    orderInfo,
    popLoading,
    getVipData,
    handleCreateVipOrder,
    hasCoupon = false,
    showFreeze,
    setHideFreeze,
    defaultVipModalInfo,
    goPayResult,
    dopayError,
    createOrdering,
    compSetProd,
    aniPopCount,
    pbInfo,
    pbInfoStr,
    hasRedeemed,
    resetRedeem
  } = props
  const [firstComin, setFirstComin] = useState(true)
  const [activeTab, setActiveTab] = useState(0)
  const [tabList, setTabList] = useState([])
  const [noTab, setNotab] = useState(false)
  const [productsList, setproductsList] = useState({})
  const [priceRank, setPriceRank] = useState({})
  const [selectedPW, setSelectedPW] = useState({})
  const [selectedCOU, setSelectedCOU] = useState({})
  const [selectVipTag, setSelectVipTag] = useState(0)
  const [privilegeList, setPrivilegeList] = useState({})
  const [selectProd, setSelectProd] = useState({})
  const [btnAgreeList, setBtnAgreeList] = useState({})
  const [couponList, setCouponList] = useState([])
  const [tabChanged, setTabChanged] = useState('')
  const [tabClick, setTabClick] = useState(false)
  // 用户主动关闭权益展示
  const [userClose, setUserClose] = useState(false)
  // 默认选中的套餐的顺序
  const [selectedProdIndex, setSelectedProdIndex] = useState(-1)
  const [afterLogin, setAfterLogin] = useState(true)
  const [defaultCoupon, setDefaultCoupon] = useState('')
  const [defaultPayWith, setDefaultPayWith] = useState('')
  const [pkgChange, setPkgChange] = useState('')
  const [pwChange, setPwChange] = useState('')

  const [showRedeemCoupon, setShowRedeemCoupon] = useState(false)
  const [redeemSuccess, setRedeemSuccess] = useState(false)
  const tabRef = useRef()
  const contentRef = useRef()
  const tabContentRef = useRef()

  // 这里进弹窗逻辑的设置
  const [discountPopVisible, setDiscountPopVisible] = useState(false)
  const [haveShowPop, setHaveShowPop] = useState(false)
  const [prodPosition, setProdPosition] = useState({})
  const refObj = useRef({})
  // 进入页面第一次的数据处理

  useEffect(() => {
    // setRedeemSuccess(false)
    if (!popLoading) {
      // console.log('进入tabcontent')
      const {
        abCard,
        priList,
        productsList,
        plainProds,
        couponList,
        agreeList,
        cheapRank,
        defaultCode,
        couponDefaultProd
      } = vipInfo
      // 如果登录刷新前存储的数据存在
      // console.log(defaultVipModalInfo, '这里是取到的登录刷新存储的数据')
      if (defaultVipModalInfo && defaultVipModalInfo.productSetCode) {
        const { vipTag, productSetCode } = defaultVipModalInfo
        const { vipTypeNameLocation = [], vipTag: interfaceVipTag } = abCard
        // console.log('取到存在cookie里面的值')
        if (vipTag === 11) {
          if (interfaceVipTag === 11) {
            setSelectVipTag(interfaceVipTag)
          } else {
            // 这里去计算
            let _index = -1
            _index = plainProds.findIndex(
              (item) => item.productSetCode === productSetCode
            )
            let _vipTag = null
            if (_index > -1) {
              _vipTag = plainProds[_index].vipTag
            } else {
              _vipTag = interfaceVipTag
            }
            setActiveTab(_vipTag)
            setSelectVipTag(_vipTag)
          }
        } else {
          if (interfaceVipTag === 11) {
            setSelectVipTag(interfaceVipTag)
          } else {
            setActiveTab(vipTag)
            setSelectVipTag(vipTag)
          }
        }
      } else {
        // 如果默认优惠券存在
        if (defaultCode && couponDefaultProd.price) {
          // setRedeemSuccess(true)
          setActiveTab(couponDefaultProd.vipTag)
          setSelectVipTag(couponDefaultProd.vipTag)
        }
        // else if (abCard.vipTag !== 11) {
        //   // console.log('执行哪个vip，，，，，，，，，，，', abCard.vipTag)
        //   setActiveTab(abCard.pageDefaultVipTag)
        //   setSelectVipTag(abCard.pageDefaultVipTag)
        // }
        else {
          // console.log('执行哪这个vip，，，，，，，，，，，', abCard.vipTag)
          setActiveTab(abCard.vipTag)
          setSelectVipTag(abCard.vipTag)
        }
      }
      // 设置列表数据
      setproductsList(productsList)
      setPrivilegeList(priList)
      setInitTab(abCard)
      setCouponList(couponList)
      setBtnAgreeList(agreeList)
      setPriceRank(cheapRank)
    }
    return () => {
      // console.log('这里执行了吗')
      clearInterval(intervalRef.current)
    }
  }, [popLoading])

  useEffect(() => {
    productsList[selectVipTag] &&
      productsList[selectVipTag].forEach((item, index) => {
        sendBlockPb('product_type', {
          bstp: 56,
          rpage: 'cashier_popup',
          tjPb: {
            position: index,
            cashier_type: pbInfo.cashier_type,
            abtest: pbInfo.abtest,
            fc: pbInfo.fc,
            fv: pbInfo.fv,
            v_pid: item.pid,
            v_prod: item.productSetCode
          }
        })
      })
  }, [productsList])
  // 设置默认选中
  useEffect(() => {
    if (vipInfo) {
      if (
        afterLogin &&
        defaultVipModalInfo &&
        defaultVipModalInfo.productSetCode
      ) {
        const { productSetCode, payType } = defaultVipModalInfo
        setDefaultPayWith(payType)
        // 这里开始计算是不是存在合适的套餐
        if (selectVipTag > 0) {
          let _vipPkgList = productsList[selectVipTag] || []
          let index = _vipPkgList.findIndex(
            (item) => item.productSetCode === productSetCode
          )
          // console.log(index, '计算出来是什么呢？')
          if (index > -1) {
            setSelectedProdIndex(index)
            setTabClick(true)
            let filterArr = priceRank[selectVipTag]
              .filter((prodItem) => {
                // console.log(prodItem.productSetCode, productSetCode)
                return prodItem.productSetCode === productSetCode
              })
              .filter((payItem) => {
                // console.log(payItem.payType, payType)
                return payItem.payType === payType
              })
            if (filterArr.length > 0) {
              setSelectProd(filterArr[0])
              compSetProd(filterArr[0].pkgItem)
            } else {
              setSelectProd(priceRank[selectVipTag][0])
              compSetProd(priceRank[selectVipTag][0].pkgItem)
            }
          } else {
            setSelectProd(priceRank[selectVipTag][0])
            compSetProd(priceRank[selectVipTag][0].pkgItem)
            let findCode = priceRank[selectVipTag][0].productSetCode
            let __index = _vipPkgList.findIndex(
              (item) => item.productSetCode === findCode
            )
            setSelectedProdIndex(__index)
            setTabClick(true)
          }
        }
        setAfterLogin(false)
        return
      } else {
        let _scrollIndex = 0
        const { defaultCode, couponDefaultProd, productsList } = vipInfo
        let recommendIndex = -1
        if (defaultCode && couponDefaultProd.price && hasRedeemed) {
          setSelectProd(couponDefaultProd)
          setSelectedPW(couponDefaultProd.payItem)
          compSetProd(couponDefaultProd.pkgItem)
          let _vipTag = couponDefaultProd.vipTag
          _scrollIndex = productsList[_vipTag].findIndex(
            (_item) => _item.productSetCode === couponDefaultProd.productSetCode
          )
          setSelectedProdIndex(_scrollIndex)
          setTabClick(true)
          setFirstComin(false)
          // resetRedeem()
          // console.log(firstComin, 'in tab content 是不是第一次进入888888')
          return
        }
        if (selectVipTag > 0) {
          let _vipPkgList = productsList[selectVipTag] || []
          recommendIndex =
            _vipPkgList && _vipPkgList.findIndex((item) => item.recommend)
          if (recommendIndex === -1) recommendIndex = 0
          setSelectedProdIndex(recommendIndex)
          setTabClick(true)
          let defaultProd = _vipPkgList[recommendIndex]
          if (priceRank[selectVipTag]) {
            let _selectIndex = priceRank[selectVipTag].findIndex((item) => {
              return item.productSetCode === defaultProd.productSetCode
            })

            setSelectProd(priceRank[selectVipTag][_selectIndex])
            compSetProd(priceRank[selectVipTag][_selectIndex].pkgItem)
          } else {
            setSelectProd({})
          }
        }
      }
    }
  }, [selectVipTag, vipInfo])

  useEffect(() => {
    if (pkgChange && selectedCOU.couponCode) {
      const _list = [...priceRank[selectVipTag]]
      let _selectProdArr = _list
        .filter((item) => {
          return item.productSetCode === pkgChange
        })
        .filter((item) => item.couponCode === selectedCOU.couponCode)
      if (_selectProdArr.length > 0) {
        setSelectProd(_selectProdArr[0])
        compSetProd(_selectProdArr[0].pkgItem)
        setSelectedCOU(_selectProdArr[0].couponItem)
      }
    }
  }, [pkgChange])
  useEffect(() => {
    if (pwChange) {
      if (selectedCOU.couponCode) {
        const _list = [...priceRank[selectVipTag]]
        let _selectProdArr = _list
          .filter((item) => {
            return (
              item.payType === pwChange &&
              item.productSetCode === selectProd.productSetCode
            )
          })
          .filter((item) => item.couponCode === selectedCOU.couponCode)
        if (_selectProdArr.length > 0) {
          setSelectedPW(_selectProdArr[0].payItem)
          setSelectedCOU(_selectProdArr[0].couponItem)
        } else {
          setSelectedCOU({})
        }
        return
      } else {
        const _list = [...priceRank[selectVipTag]]
        let _selectProdArr = _list.filter((item) => {
          return (
            item.payType === pwChange &&
            item.productSetCode === selectProd.productSetCode
          )
        })
        if (_selectProdArr.length > 0) {
          setSelectedPW(_selectProdArr[0].payItem)
          setSelectedCOU(_selectProdArr[0].couponItem)
        } else {
          setSelectedCOU({})
        }
      }
    }
  }, [pwChange])

  // 设置tab
  const setInitTab = (tabInfo) => {
    if (tabInfo.vipTag === 11) {
      setNotab(true)
    } else {
      setNotab(false)
      setTabList(tabInfo.vipTypeNameLocation)
    }
  }
  //  切换会员tab
  const changeTab = (item, index) => {
    if (popLoading) return
    setTabClick(true)
    setTabChanged(item.vipTag)
    setActiveTab(item.vipTag)
    setSelectVipTag(item.vipTag)
    resetRedeem()
  }
  const setNotabClick = useCallback(() => {
    setTabClick(false)
  }, [])

  const setManualRedeem = useCallback((show) => {
    if (show) {
      if (tabContentRef.current) {
        tabContentRef.current.scrollTop =
          tabContentRef.current.scrollHeight + 40
      }
    }
    setShowRedeemCoupon(show)
  }, [])
  // 切换套餐
  const selectPkg = (pkgItem) => {
    let length = priceRank[selectVipTag].length
    for (let i = 0; i < length; i++) {
      if (
        priceRank[selectVipTag][i].productSetCode === pkgItem.productSetCode
      ) {
        setSelectProd(priceRank[selectVipTag][i])
        compSetProd(priceRank[selectVipTag][i].pkgItem)
        setPkgChange(pkgItem.productSetCode)
        return
      }
    }
  }
  // 获取当前选中的支付方式
  const getPayWith = useCallback((selected) => {
    // console.log('这里应该是辅助性')
    setSelectedPW(selected)
  }, [])
  // 获取当前的选中优惠券
  const getCOU = useCallback((selected) => {
    // console.log('这里选中的优惠券', selected)
    setSelectedCOU(selected)
  }, [])

  const collectInfo = useCallback((type) => {
    const data = contentRef.current.getInputInfo(type)
    return data
  })

  const changePW = useCallback((paywith) => {
    setPwChange(paywith)
  })

  // 登录信息操作
  const startLogin = useCallback(async () => {
    if (!isLogin()) {
      if (!window.sdkPackManager) {
        await initLogin()
      } else if (!window.sdkPackManager.globalLogin) {
        await window.sdkPackManager.initLogin()
      }
      window.isRefresh = () => {
        window.location.reload()
      }
      window.sdkPackManager.globalLogin.openLoginRegWindow({
        vipModalInfo: {
          vipTag: selectVipTag,
          productSetCode: selectProd.productSetCode,
          payType: selectedPW.payType
          // couponCode: selectedCOU.couponCode
        }
      })

      // this.setStateInLocal()
      return
    }
  }, [selectVipTag, selectProd, selectedPW])

  //  新加弹窗动效逻辑
  const intervalRef = useRef()
  // 展示价格滚动
  const [showPriceAni, setshowPriceAni] = useState(false)
  // 设置套餐为活动套餐
  const [prodAni, setProdAni] = useState(false)
  // 倒计时数据
  const [countDay, setCountDay] = useState(0)
  const [countH, setCountH] = useState(0)
  const [countM, setCountM] = useState(0)
  const [countS, setCountS] = useState(0)
  const [showProdTimer, setShowProdTimer] = useState(false)
  const [aniProdCode, setAniProdCode] = useState('')
  // 用于判断是不是打开过动画弹窗
  const [aniPopClose, setAniPopclose] = useState(false)
  // 不弹窗其他展示
  const [moreAni, setMoreAni] = useState(false)

  const countTimer = (time) => {
    let index = 0
    // setShowProdTimer(true)
    intervalRef.current = setInterval(() => {
      index++
      countTimefFun(time, index)
    }, 1000)
  }
  const countTimefFun = (time, index = 0) => {
    let distance = time - index * 1000
    if (distance > -1) {
      let _days = Math.floor(distance / (1000 * 60 * 60 * 24))
      let _hours = Math.floor(
        (distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
      )
      let _minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60))
      let _seconds = Math.floor((distance % (1000 * 60)) / 1000)
      let days = _days > 9 ? _days : '0' + _days
      let hours = _hours > 9 ? _hours : '0' + _hours
      let minutes = _minutes > 9 ? _minutes : '0' + _minutes
      let seconds = _seconds > 9 ? _seconds : '0' + _seconds
      setCountDay(() => days)
      setCountH(() => hours)
      setCountM(() => minutes)
      setCountS(() => seconds)
    }
  }

  // 计算弹窗出现位置和时机
  useEffect(() => {
    if (!haveShowPop && selectProd.productSetCode) {
      setHaveShowPop(true)
      const { payItem = {} } = selectProd
      const {
        promotionShow,
        countDown,
        ensureButtonDesc,
        discountMode,
        discountValue = '',
        discountPercent = '',
        needPayFee,
        originPrice
      } = payItem
      const storageName = formatDate(new Date(), 'yyyyMMdd')
      const storageCount =
        (localStorage.getItem('aniCountObj') &&
          JSON.parse(localStorage.getItem('aniCountObj'))[storageName]) ||
        0
      if (
        countDown !== 0 &&
        promotionShow &&
        ensureButtonDesc &&
        discountMode &&
        (!isEmptyStr(discountValue) || !isEmptyStr(discountPercent)) &&
        needPayFee < originPrice
      ) {
        if (aniPopCount > storageCount) {
          setHaveShowPop(true)
          setAniProdCode(selectProd.productSetCode)
          setTimeout(() => {
            setDiscountPopVisible(true)
          }, 300)
        } else {
          setProdAni(true)
          setAniPopclose(true)
          setHaveShowPop(true)
          setAniProdCode(selectProd.productSetCode)
          setMoreAni(true)
          if (countDown > 0) {
            setShowProdTimer(true)
          }
          countTimefFun(countDown)
          countTimer(countDown - 1)
        }
      }
    }
  }, [selectedProdIndex, selectProd])

  const closePri = useCallback(() => {
    setUserClose(true)
  }, [])

  // 关闭弹窗动画
  const hidePop = useCallback(() => {
    setProdAni(true)

    let prodElePos =
      refObj.current[selectProd.productSetCode].getBoundingClientRect()
    setProdPosition({
      top: prodElePos.top,
      left: prodElePos.left
    })
    const storageName = formatDate(new Date(), 'yyyyMMdd')
    let storageCount =
      (localStorage.getItem('aniCountObj') &&
        JSON.parse(localStorage.getItem('aniCountObj'))[storageName]) ||
      0
    localStorage.setItem(
      'aniCountObj',
      JSON.stringify({
        [storageName]: storageCount + 1
      })
    )
    setTimeout(() => {
      setDiscountPopVisible(false)
      setshowPriceAni(true)
    }, 600)
    setTimeout(() => {
      setAniPopclose(true)
    }, 700)

    // 从这里开启套餐效果
  }, [refObj, selectProd])

  const setProdTime = useCallback((time) => {
    if (time < 0) {
      return setShowProdTimer(false)
    }
    countTimefFun(time)
    setShowProdTimer(true)
    countTimer(time)
  }, [])

  // return
  return (
    <>
      {/* <Toast>
      <img className='success-icon'  src="" alt="" />
    </Toast> */}
      <UserInfo
        hasCoupon={hasCoupon}
        pbInfo={pbInfo}
        pbInfoStr={pbInfoStr}
        vipInfo={vipInfo}
        vipLangPkg={vipLangPkg}
        popLoading={popLoading}
        startLogin={startLogin}
        setManualRedeem={setManualRedeem}
      />
      <TabListWrapper>
        {noTab ? (
          <></>
        ) : !popLoading ? (
          <ul className="tab-list">
            {tabList.length > 0 &&
              tabList
                .sort((a, b) => b.vipTag - a.vipTag)
                .map((item, index) => {
                  return (
                    <li
                      key={item.name + index}
                      className={`tab-item ${
                        activeTab === item.vipTag || (popLoading && index === 0)
                          ? 'tab-active'
                          : ''
                      } `}
                      onClick={() => {
                        changeTab(item, index)
                        // startLogin()
                      }}
                    >
                      <p className="tab-name">{!popLoading ? item.name : ''}</p>
                    </li>
                  )
                })}
          </ul>
        ) : (
          ''
        )}
      </TabListWrapper>
      <TabContentWapper
        style={{
          marginBottom: `${
            btnAgreeList && btnAgreeList.showAgreement === 1 ? '120px' : '90px'
          }`
        }}
        ref={tabContentRef}
      >
        {popLoading ? (
          <div className="loading-img-wrapper">
            <img
              src="//www.iqiyipic.com/new-vip/loading.png"
              alt=""
              className="loading-img"
            />
          </div>
        ) : (
          <>
            {/* <div className="tab-content-outter"> */}
            <div className="tab-detail-wrapper">
              <Slider
                pageNum={
                  productsList[selectVipTag] &&
                  productsList[selectVipTag].length /
                    (getViewWidth() < 1024 ? 3 : 4)
                }
                itemCount={
                  productsList[selectVipTag] &&
                  productsList[selectVipTag].length
                }
                tabClick={tabClick}
                slideToPage={selectedProdIndex}
                setNotabClick={setNotabClick}
                pbInfo={pbInfo}
                pbInfoStr={pbInfoStr}
                ref={tabRef}
              >
                <div className="goods-list-wrapper">
                  {productsList[selectVipTag] &&
                    productsList[selectVipTag].map((item, index) => {
                      return (
                        <div
                          className={`goods-item-wrapper ${
                            item.productSetCode === selectProd.productSetCode
                              ? 'goods-item-focused'
                              : ''
                          } ${
                            item.productSetCode === aniProdCode &&
                            item.productSetCode === selectProd.productSetCode &&
                            prodAni
                              ? 'goods-item-ani-focus'
                              : ''
                          } ${
                            prodAni && item.productSetCode === aniProdCode
                              ? 'goods-item-ani'
                              : ''
                          }`}
                          ref={(r) => (refObj.current[item.productSetCode] = r)}
                          key={item.productSetCode + index}
                          onClick={() => {
                            selectPkg(item)
                          }}
                          rseat={`${
                            selectProd.pkgItem && selectProd.pkgItem.index
                          }:${item.index}`}
                          data-pb={`rpage=cashier_popup&block=product_type&position=${index}&${pbInfoStr}`}
                        >
                          {prodAni &&
                          showProdTimer &&
                          item.productSetCode === aniProdCode ? (
                            <div
                              className={`goods-timecount-wrapper ${
                                aniPopClose ? '' : 'goods-timecount-ani'
                              }`}
                            >
                              <i className="clock-icon"></i>
                              <p className="goods-timecount">
                                {+countDay > 0
                                  ? `${countDay}${
                                      cashierLangPkg.PCW_CASHIER_1658476805935_412 ||
                                      'Day(s)'
                                    }`
                                  : ''}
                                {countH}:{countM}:{countS}
                              </p>
                            </div>
                          ) : (
                            <>
                              {item.promotion ? (
                                <p
                                  className={`goods-promotion ${
                                    prodAni &&
                                    !showProdTimer &&
                                    item.productSetCode === aniProdCode
                                      ? 'goods-promition-ani'
                                      : ''
                                  }`}
                                >
                                  {item.promotion}
                                </p>
                              ) : (
                                ''
                              )}
                            </>
                          )}
                          {selectVipTag === 11 ? (
                            <p className="goods-type">{item.tabName}</p>
                          ) : (
                            ''
                          )}
                          <div className="goods-title-blank">
                            <p className="goods-title">{item.name}</p>
                          </div>

                          {/* 这一行是为了在UI上对齐格式 */}
                          <section className="goods-blank-div"></section>
                          <section className="price-wrapper">
                            {item.showDiscountPop &&
                            item.productSetCode === aniProdCode &&
                            !moreAni ? (
                              <div className="goods-price">
                                {mod === 'vn' ? (
                                  <>
                                    <NumberAnimation
                                      startValue={Number(
                                        (item.originalPrice / 100).toFixed(2)
                                      )}
                                      endValue={Number(
                                        (item.needPayFee / 100).toFixed(2)
                                      )}
                                      startAni={showPriceAni}
                                    ></NumberAnimation>
                                    <i className="currency-icon">
                                      {item.currencySymbol}
                                    </i>
                                  </>
                                ) : (
                                  <>
                                    <i className="currency-icon">
                                      {item.currencySymbol}
                                    </i>
                                    <NumberAnimation
                                      startValue={Number(
                                        (item.originalPrice / 100).toFixed(2)
                                      )}
                                      endValue={Number(
                                        (item.needPayFee / 100).toFixed(2)
                                      )}
                                      startAni={showPriceAni}
                                    ></NumberAnimation>
                                  </>
                                )}
                              </div>
                            ) : (
                              <div className="goods-price">
                                {mod === 'vn' ? (
                                  <>
                                    <p>
                                      {Number(
                                        (item.needPayFee / 100).toFixed(2)
                                      ).toString()}
                                    </p>

                                    <i className="currency-icon">
                                      {item.currencySymbol}
                                    </i>
                                  </>
                                ) : (
                                  <>
                                    <i className="currency-icon">
                                      {item.currencySymbol}
                                    </i>
                                    <p>
                                      {Number(
                                        (item.needPayFee / 100).toFixed(2)
                                      ).toString()}
                                    </p>
                                  </>
                                )}
                              </div>
                            )}

                            {item.needPayFee < item.originalPrice &&
                            item.productSetCode !== aniProdCode ? (
                              <p className="goods-origin-price">
                                {mod === 'vn'
                                  ? `${Number(
                                      (item.originalPrice / 100).toFixed(2)
                                    ).toString()}${item.currencySymbol}`
                                  : `${item.currencySymbol}${Number(
                                      (item.originalPrice / 100).toFixed(2)
                                    ).toString()} `}
                              </p>
                            ) : (
                              <>
                                {(showPriceAni || moreAni) &&
                                item.productSetCode === aniProdCode ? (
                                  <p
                                    className={`origin-ani-price ${
                                      aniPopClose
                                        ? ''
                                        : 'origin-ani-price-start'
                                    }`}
                                  >
                                    {mod !== 'vn'
                                      ? item.discountMode === 1
                                        ? cashierLangPkg.PCW_CASHIER_1658477892568_588.replace(
                                            '%s',
                                            `${item.currencySymbol}${Number(
                                              (
                                                item.discountValue / 100
                                              ).toFixed(2)
                                            ).toString()}`
                                          )
                                        : cashierLangPkg.PCW_CASHIER_1658477892568_588.replace(
                                            '%s',
                                            `${item.discountPercent}%`
                                          )
                                      : item.discountMode === 1
                                        ? cashierLangPkg.PCW_CASHIER_1658477892568_588.replace(
                                            '%s',
                                            `${Number(
                                              (
                                                item.discountValue / 100
                                              ).toFixed(2)
                                            ).toString()}${item.currencySymbol}`
                                          )
                                        : cashierLangPkg.PCW_CASHIER_1658477892568_588.replace(
                                            '%s',
                                            `${item.discountPercent}%
                                      `
                                          )}
                                  </p>
                                ) : (
                                  ''
                                )}
                              </>
                            )}
                          </section>
                        </div>
                      )
                    })}
                </div>
              </Slider>
              <VipPrivilege
                vipLangPkg={vipLangPkg}
                userClose={userClose}
                closePri={closePri}
                privilegeList={privilegeList}
                selectVipTag={selectVipTag}
                selectProd={selectProd}
              ></VipPrivilege>
              <PayWith
                userCards={userCards}
                selectProd={selectProd}
                vipLangPkg={vipLangPkg}
                cashierLangPkg={cashierLangPkg}
                getPayWith={getPayWith}
                hasCoupon={hasCoupon}
                tabClick={tabClick}
                changePW={changePW}
                setNotabClick={setNotabClick}
                startLogin={startLogin}
                afterLogin={afterLogin}
                defaultPayWith={defaultPayWith}
                tabChanged={tabChanged}
                dopayError={dopayError}
                pbInfo={pbInfo}
                pbInfoStr={pbInfoStr}
                ref={contentRef}
              ></PayWith>
              {hasCoupon ? (
                <Coupon
                  selectProd={selectProd}
                  vipLangPkg={vipLangPkg}
                  cashierLangPkg={cashierLangPkg}
                  uniqueCoupons={couponList}
                  getVipData={getVipData}
                  selectedPW={selectedPW}
                  getCOU={getCOU}
                  mod={mod}
                  selectedCOU={selectedCOU}
                  tabChanged={tabChanged}
                  afterLogin={afterLogin}
                  startLogin={startLogin}
                  defaultCoupon={defaultCoupon}
                  showRedeemCoupon={showRedeemCoupon}
                  setManualRedeem={setManualRedeem}
                  pbInfoStr={pbInfoStr}
                ></Coupon>
              ) : (
                ''
              )}
              {/* {showFreeze ? (
                <UnlockCoupon vipLangPkg={vipLangPkg}></UnlockCoupon>
              ) : (
                ' '
              )} */}
            </div>

            {/* </div> */}
          </>
        )}
      </TabContentWapper>
      {!popLoading ? (
        <BuyBtn
          pbInfo={pbInfo}
          popLoading={popLoading}
          selectVipTag={selectVipTag}
          agreeList={btnAgreeList}
          vipLangPkg={vipLangPkg}
          selectProd={selectProd}
          selectedPW={selectedPW}
          selectedCOU={selectedCOU}
          collectInfo={collectInfo}
          showFreeze={showFreeze}
          setHideFreeze={setHideFreeze}
          pageInfo={orderInfo}
          createOrdering={createOrdering}
          startLogin={startLogin}
          goPayResult={goPayResult}
          handleCreateVipOrder={handleCreateVipOrder}
        ></BuyBtn>
      ) : (
        ''
      )}
      {discountPopVisible ? (
        <DiscountPop
          selectProd={selectProd}
          cashierLangPkg={cashierLangPkg}
          prodPosition={prodPosition}
          hidePop={hidePop}
          setProdTime={setProdTime}
          pbInfoStr={pbInfoStr}
          pbInfo={pbInfo}
        ></DiscountPop>
      ) : (
        ''
      )}
    </>
  )
}

export default TabContent
