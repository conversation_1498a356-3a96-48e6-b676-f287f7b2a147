import React, { useState, useEffect, useCallback } from 'react'
import { getCookies } from '@/kit/cookie'
import Style from './style'
import EmptyComponent from './empty'
import { formatDate } from '@/utils/common'
import { isLogin } from '@/utils/userInfo'
import { createOrder, getAbtest } from '../api'
import { ErrorIcon } from '../style/icon'
import { sendBlockPb, sendClickPb } from '@/utils/pingBack'
import { initLogin } from '@/utils/loginWindow.js'

const mod = getCookies('mod')
const lang = getCookies('lang') || 'en_us'
export default (props) => {
  const { data, params, vipLangPkg, abtest, handleSubmit } = props

  useEffect(() => {
    sendBlockPb('web_tvod_casher', {
      bstp: 56,
      tjPb: {
        abtest,
        fc: params.fc || '',
        fv: params.fv || ''
      }
    })
  }, [])

  useEffect(() => {
    if (data) {
      const recommendSelect = packageInfo.payTypeOptions.find(
        (item) => item.recommend
      )
      setSelectedType(recommendSelect || packageInfo.payTypeOptions[0])
    }
  }, [data])

  const [selectedType, setSelectedType] = useState()
  const [error, setError] = useState('')

  const handleClickNext = useCallback(async () => {
    if (!selectedType) return
    if (!isLogin()) {
      if (!window.sdkPackManager) {
        await initLogin()
      } else if (!window.sdkPackManager.globalLogin) {
        await window.sdkPackManager.initLogin()
      }
      window.sdkPackManager.globalLogin.openLoginRegWindow()
      this.setStateInLocal()
      return
    }
    window.gtag &&
      window.gtag('event', 'click_pay_button', {
        mod: mod || 'intl',
        rpage: 'web_tvod_casher'
      })
    try {
      const res = await createOrder(
        {
          ...params,
          fv_abtest: params.abtest,
          abtest: getAbtest() + _abtest,
          payType: selectedType.payType,
          amount: packageInfo.vodAmount
        },
        lang,
        mod
      )
      handleSubmit(res, selectedType)
    } catch (err) {
      console.log(err)
      setError(err.message || 'network error')
    }
  }, [selectedType, data, params, handleSubmit])

  if (!data) return <EmptyComponent />
  const { product, packageInfo, _abtest } = data

  const deadline = formatDate(product.deadline, 'yyyy-MM-dd HH:mm:ss')
  const vodPrice = Number((packageInfo.vodPrice / 100).toFixed(2)).toString()
  const vodOriginPrice = Number(
    (packageInfo.vodOriginPrice / 100).toFixed(2)
  ).toString()
  return (
    <Style>
      <div className={`scroll-box ${error && 'iserror'}`}>
        {error && (
          <div className="error">
            <ErrorIcon />
            {error}
          </div>
        )}
        <div className="main-box">
          <div className="title">{params.title}</div>
          <div className="price">
            {mod === 'vn' ? (
              <>
                <span className="real-price">{vodPrice}</span>
                <span className="unit">{product.currencySymbol}</span>
              </>
            ) : (
              <>
                <span className="unit">{product.currencySymbol}</span>
                <span className="real-price">{vodPrice}</span>
              </>
            )}
            {packageInfo.vodPrice < packageInfo.vodOriginPrice && (
              <span className="origin-price">
                {mod === 'vn'
                  ? vodOriginPrice + product.currencySymbol
                  : product.currencySymbol + vodOriginPrice}
              </span>
            )}
          </div>
          {!params.isLiveTvod && (
            <div className="time">
              <div className="label">{vipLangPkg.viewing_period}</div>
              <div>{vipLangPkg.tvod_expiry.replace('%s', deadline)}</div>
            </div>
          )}
          <div className="label">{vipLangPkg.pay_by}</div>
          {packageInfo.payTypeOptions.map((item, i) => (
            <div
              className={`paytype-card ${
                selectedType && selectedType.id === item.id ? 'selected' : ''
              }`}
              key={item.id}
              id={item.id}
              onClick={() => {
                setSelectedType(item)
              }}
              role="button"
              tabIndex={i}
            >
              <img className="card-icon" alt={item.name} src={item.iconUrl} />
              <div className="card-main">
                <div className="card-name">{item.name}</div>
                <div className="card-promotion">{item.promotion}</div>
              </div>
              <i className="select-icon" />
            </div>
          ))}
        </div>
      </div>
      {error && <div className="error-h5">{error}</div>}
      <button
        className={`step-btn ${!selectedType && 'disabled'}`}
        rseat="popup_next"
        data-pb={`rpage=web_tvod_casher&block=payment_action&bstp=56&abtest=${abtest}&fc=${
          params.fc || ''
        }&fv=${params.fv || ''}`}
        onClick={handleClickNext}
        rseat={'passport_pay' + (isLogin() ? '' : '_un')}
      >
        {vipLangPkg.tvod_rentNow}
      </button>
    </Style>
  )
}
