import { getCookies } from '@/kit/cookie'
import { getDevice } from '@/kit/device'
import { getUid } from '@/utils/userInfo'
const md5 = require('md5')
const domain = 'http://msg-intl.qy.net/qos'
const isMobile = getDevice() === 'mobile'
const device_id = getCookies('QC005')
export const createLog = (params = {}) => {
  const img = new Image()
  const paramsArr = []
  try {
    Object.keys(params).forEach((paramName) => {
      paramsArr.push(`${paramName}=${encodeURIComponent(params[paramName])}`)
    })
  } catch (e) {
    console.log(e)
  }
  try {
    img.src = `${domain}?${paramsArr.join('&')}`
  } catch (e) {
    console.log(e)
  }
}

export const sendQosLog = (param) => {
  if (!window) {
    return
  }
  const { abtest = '', ce = '' } = param
  const params = {
    t: 9,
    p1: '1_10_222', // 平台码
    u: device_id, // 终端用户设备唯一标识
    pu: getUid() || 0, // 登录用户的爱奇艺Passportid，即登录账号，非登录用户为空。
    stime: Date.now(),
    mod: getCookies('mod'),
    lang: getCookies('lang'),
    ct: 'cashierqos',
    ce: ce || (window._performance && window._performance.eventId) || ''
  }
  const _Params = Object.assign({}, params, param)
  createLog(_Params)
}

export const calCE = () => {
  let ceVal = md5(device_id + Date.now() + Math.random() + '')
  
  return ceVal
}
