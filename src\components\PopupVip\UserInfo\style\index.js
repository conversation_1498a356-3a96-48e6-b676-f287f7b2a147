import styled from 'styled-components'

const UserInfoWrapper = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: stretch;
  width: 100%;
  padding: 16px 16px 16px 40px;
  background: #23252b;
  color: #fff;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  box-sizing: border-box;
  .user-info-wrapper {
    display: flex;
    justify-content: center;
  }
  .user-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    border: solid 1px rgba(255, 255, 255, 0.1);
  }
  .user-info-right {
    display: flex;
    flex-wrap: wrap;
    align-content: center;
    margin-left: 12px;
    font-size: 14px;
    flex: 1;
    overflow: hidden;
  }
  .nologin-wrapper {
    cursor: pointer;
  }
  .loading-empty {
    flex-direction: column;
    justify-content: center;
    .user-name {
      width: 80px;
      height: 14px;
      background: #37383d;
      margin-bottom: 8px;
    }
    .user-vip-info {
      width: 260px;
      height: 14px;
      background: #37383d;
    }
  }
  .user-name {
    width: 100%;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    /* margin-bottom: 4px; */
    text-overflow: ellipsis;
    line-height: 16px;
  }
  .user-vip-info {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    color: #bcbdbe;
    cursor: pointer;
    max-width: 100%;
    overflow: hidden;
    margin-top: 4px;
    line-height: 16px;
    text-overflow: ellipsis;
  }
  .user-has-vip {
    color: #f2bf83;
  }
  .header-btn-wrapper {
    display: flex;
    margin-left: 12px;
  }
  .coupon-btn-wrapper {
    display: flex;
    align-items: flex-end;
  }
  .coupon-icon-wrapper {
    margin-right: 8px;
  }
  .vip-code-wrapper {
    display: flex;
    align-items: flex-end;
    justify-content: flex-end;
    text-align: right;
    font-size: 12px;
    color: #bcbdbe;
    cursor: pointer;
    line-height: 26px;
  }
  .coupon-icon-wrapper,
  .voucher-icon-wrapper {
    display: flex;
    align-items: center;
    padding: 0px 10px;
    background: #23252b;
    border: 1px solid rgba(93, 94, 98, 1);
    border-radius: 12px;
    line-height: 12px;
    color: #ffffff;
    cursor: pointer;
    max-width: 160px;
    overflow: hidden;
    text-overflow: ellipsis;
    box-sizing: border-box;
    &:hover {
      border: 1px solid #828387;
      background: #37383d;
    }
  }
  .coupon-icon,
  .voucher-icon {
    width: 20px;
    height: 20px;

    background: url(//www.iqiyipic.com/new-vip/icon_code.png) no-repeat center;
    background-size: 100% 100%;
  }
  .coupon-icon {
    background: url(//www.iqiyipic.com/new-vip/icon_coupon.png) no-repeat center;
    background-size: 100% 100%;
  }
  .skip-link {
    white-space: nowrap;
    margin-left: 4px;
    color: #ffffff;
    text-overflow: ellipsis;
    overflow: hidden;
    flex: 1;
  }

  @media screen and (max-width: 1023px) {
    .user-vip-info {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      cursor: pointer;
      line-height: 16px;
      text-overflow: ellipsis;
    }
    .coupon-icon-wrapper,
    .voucher-icon-wrapper {
      max-width: 140px;
    }
  }
  @media screen and (max-width: 767px) {
    padding-left: 32px;
  }
`
export default UserInfoWrapper
