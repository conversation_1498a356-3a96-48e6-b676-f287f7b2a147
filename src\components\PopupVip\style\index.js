import styled from 'styled-components'

const Style = styled.div`
  display: ${(props) => (props.visible ? 'flex' : 'none')};
  justify-content: center;
  align-items: center;
  position: fixed;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  top: 0;
  font-size: 12px;
  .content {
    max-height: calc(85vh - 40px);
    overflow-y: auto;
    scrollbar-width: none;
    overflow-y: overlay;
    box-sizing: border-box;
    padding-bottom: 32px;
    ::-webkit-scrollbar {
      width: 14px;
    }
    ::-webkit-scrollbar-thumb {
      border-radius: 8px;
      background: #999;
      border: solid 4px #f0f0f0;
    }
    ::-webkit-scrollbar-track {
      padding-right: 4px;
      background: #f0f0f0;
    }
  }
  .container {
    background: #f6f6f6;
    width: 902px;
    box-sizing: border-box;
    position: absolute;
    overflow-x: visible;
    border-radius: 6px;
  }
  .vod-container {
    .back {
      width: auto;
      height: auto;
      background: none;
      &:hover {
        background: none;
      }
    }
  }
  .new-vip-container {
    flex-direction: column;
    background: #ffffff;
    max-height: calc(100vh - 80px);
    width: 1024px;
    min-height: 640px;
    box-sizing: border-box;
    position: absolute;
    overflow-x: visible;
    border-radius: 8px;
    overflow: hidden;
    /* padding-bottom: 120px; */
    /* min-height: 640px; */
  }
  .header-bar {
    height: 24px;
    padding: 12px 12px 4px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .new-vip-header-bar {
    /* height: 50px; */
    /* padding: 12px 12px 4px; */
    display: flex;
    align-items: center;
    justify-content: space-between;
    .close {
      position: absolute;
      top: 12px;
      right: 12px;
      width: 24px;
      height: 24px;
      background: url('//www.iqiyipic.com/lequ/20220430/icon_close_darkNormal.png')
        no-repeat;
      background-size: cover;
      &:hover {
        background: url('//www.iqiyipic.com/lequ/20220430/icon_close_darkHover.png') no-repeat;
        background-size: cover;
      }
    }
  }
  .new-vip-result-header {
    height: 24px;
    .back {
      font-size: 16px;
      color: #666666;
      font-weight: 400;
      &:hover {
        color: #222222;
        .icon {
          background: url('//www.iqiyipic.com/lequ/20210622/icon-nav-back-hover.png')
            no-repeat;
          background-size: cover;
        }
      }
      .icon {
        position: absolute;
        top: 12px;
        left: 12px;
        display: block;
        width: 24px;
        height: 24px;
        background: url('//www.iqiyipic.com/lequ/20210622/icon-nav-back-normal.png')
          no-repeat;
        background-size: cover;
      }
    }
    .close {
      position: absolute;
      top: 12px;
      right: 12px;
      width: 24px;
      height: 24px;
      background: url('//www.iqiyipic.com/lequ/20220430/icon_close_darkNormal.png')
        no-repeat;
      background-size: cover;
      cursor: pointer;
      &:hover {
        background: url('//www.iqiyipic.com/lequ/20220430/icon_close_darkHover.png')
          no-repeat;
        background-size: cover;
      }
    }
  }
  .close,
  .back {
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
  }
  .back {
    font-size: 16px;
    color: #666666;
    font-weight: 400;
    &:hover {
      color: #222222;
      .icon {
        background: url('//www.iqiyipic.com/lequ/20210622/icon-nav-back-hover.png')
          no-repeat;
        background-size: cover;
      }
    }
    .icon {
      display: block;
      width: 24px;
      height: 24px;
      background: url('//www.iqiyipic.com/lequ/20210622/icon-nav-back-normal.png')
        no-repeat;
      background-size: cover;
    }
  }
  .close {
    position: absolute;
    top: 12px;
    right: 12px;
    width: 24px;
    height: 24px;
    background: url('//www.iqiyipic.com/lequ/20210622/icon-nav-close-normal.png')
      no-repeat;
    background-size: cover;
    &:hover {
      background: url('//www.iqiyipic.com/lequ/20210622/icon-nav-close-hover.png')
        no-repeat;
      background-size: cover;
    }
  }
  .step {
    font-weight: bold;
    color: #222222;
    line-height: 16px;
  }
  .step-title {
    margin: 0 80px;
    text-align: center;
    color: #222222;
    letter-spacing: 0;
    line-height: 29px;
    font-size: 24px;
    font-weight: 800;
  }
  .step-detail {
    position: relative;
    margin: 8px 48px 16px;
    color: #666666;
    line-height: 16px;
    font-size: 14px;
    font-weight: 400;
    & > div {
      display: flex;
    }
    .step-detail-text {
      display: inline-block;
      text-align: center;
      width: 536px;
      margin: 0 auto;
    }
    .redeem-code {
      position: absolute;
      right: 0;
      top: 0;
      text-align: right;
      display: inline-block;
      margin-left: 16px;
      width: 120px;
    }
  }
  .step-tip {
    margin: 8px 80px 16px;
    font-size: 14px;
    color: #666666;
    letter-spacing: 0;
    text-align: center;
    font-weight: 400;
    line-height: 16px;
  }
  .containue {
    width: 504px;
    height: 44px;
    padding: 0 16px;
    background: #f2bf83;
    border-radius: 4px;
    font-size: 16px;
    color: #111319;
    display: block;
    margin: 16px auto 40px;
    line-height: 44px;
    border: none;
    text-align: center;
    cursor: pointer;
    &:hover {
      background: #f4cb9b;
    }
  }
  .step-btn {
    display: block;
    margin: 0 auto;
    width: 496px;
    box-sizing: border-box;
    padding: 0 16px;
    height: 44px;
    line-height: 44px;
    background: #f2bf83;
    border-radius: 4px;
    border: none;
    font-size: 16px;
    color: #111319;
    text-align: center;
    cursor: pointer;
    &:hover {
      background: #d9ab75;
    }
    &:active {
      background: #c19868;
    }
    &.disabled {
      background: #f9e5cd;
    }
  }
  /* .empty {
    .step {
      display: none;
    }
    .step-title {
      width: 145px;
      height: 16px;
      margin-top: 28px;
      background: rgba(0, 0, 0, 0.1);
    }
    .step-detail-text {
      width: 404px;
      height: 16px;
      background: rgba(0, 0, 0, 0.1);
    }
    .redeem-code {
      width: 94px;
      height: 16px;
      background: rgba(0, 0, 0, 0.1);
    }
    .containue {
      display: none;
    }
  } */
  .scroll-container {
    /* padding: 0 48px; */
    max-height: calc(90vh);
    overflow-y: auto;
    scrollbar-width: none;
    overflow-y: overlay;
  }
  .scroll-content {
    min-height: 360px;
  }
  @media screen and (min-width: 1024px) and (max-width: 1919px)  {
    .new-vip-container {
      width: 905px;
      min-height: 540px;
    }
  }
  @media screen and (max-width: 1023px) {
    .container {
      width: 628px;
    }
    .new-vip-container {
      width: 650px;
      min-height: 540px;
    }
    .step-title {
      margin: 0 64px;
    }
    .step-detail {
      .step-detail-text {
        width: 260px;
      }
    }
    .step-tip {
      margin: 8px 64px 16px;
    }
    .containue {
      width: 532px;
    }
    /* .scroll-container {
      max-height: calc(60vh);
    } */
    .scroll-content {
      min-height: 280px;
    }
  }
  @media screen and (max-width: 767px) {
    .container {
      /* width: 288px; */
      width: 343px;
      background: #ffffff;
    }
    
    /* .header-bar {
      height: 28px;
      padding: 0 6px;
    } */
    .content {
      max-height: unset;
      padding-bottom: 16px;
    }
    .step-btn {
      width: 256px;
    }
    .scroll-container {
      padding: 0;
    }
    .step-title {
      margin: 0 auto 4px;
      width: 311px;
      line-height: 19px;
      font-size: 16px;
      font-weight: 700;
    }
    .step-tip {
      margin: 0 auto 8px;
      width: 311px;
    }
    .containue {
      width: 311px;
      margin: 16px auto;
      line-height: 16px;
      font-size: 14px;
      font-weight: 500;
    }
  }
`
export default Style
