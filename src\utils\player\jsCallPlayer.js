const prevCalls = [];
const playerManagerObj = {};
const playerEvent = (pars, id) => {
  const par = pars || {};
  if (!playerManagerObj[id]) {
    return;
  }
  const player = playerManagerObj[id].getPlayerById(par.id);
  const fn = par.fn;
  const param = par.param;
  if (param) {
    player[par.name](param);
  } else if (fn) {
    player[par.name](fn);
  } else {
    player[par.name]();
  }
};
/**
 * js调用播放器方法
 * @param {Object} par 参数对象。
 */
const jsCallPlayer = par => {
  const id = par.id;
  if (playerManagerObj[id]) {
    playerEvent(par, id);
  } else {
    prevCalls.push(par);
    if (!window.QiyiPlayerLoader) {
      return;
    }
    window.QiyiPlayerLoader.ready(qiyiPlayerManager => {
      qiyiPlayerManager.ready(id, () => {
        playerManagerObj[id] = qiyiPlayerManager;
        if (prevCalls.length) {
          for (let i = 0; i < prevCalls.length; i += 1) {
            try {
              playerEvent(prevCalls[i], id);
            } catch (e) {
              console.log(e);
            }
          }
          prevCalls.length = 0;
        }
      });
    });
  }
};

export default jsCallPlayer;
