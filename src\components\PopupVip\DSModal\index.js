/**
 * @desc 3DS验证弹窗
 * <AUTHOR>
 * @time 2025-03-13
 * @feature  GLOBALREQ-13729
 */

import React, { useEffect } from 'react'
import { sendBlockPb } from '@/utils/pingBack'
import { HeaderCloseIcon } from '@/components/svgs'
import ModalWrapper from './style/index'

const DSModal = (props) => {
  const { confirmClk, cancelClk, vipLangPkg } = props

  useEffect(() => {
    sendBlockPb('3ds_gaid', {
      rpage: 'cashier_popup',
      tjPb: {
        cashier_type: 'popup'
      }
    })
  }, [])
  const modalConfirm = () => {
    if (typeof confirmClk === 'function') {
      confirmClk()
    }
  }
  return (
    <ModalWrapper>
      <div className={`ds-modal-wrapper`}>
        <div
          className="ds-modal-close-wrapper"
          rseat="close"
          data-pb="rpage=cashier_popup&block=3ds_gaid&cashier_type=popup"
          role="button"
          tabIndex={0}
          onClick={cancelClk}
        >
          <HeaderCloseIcon />
        </div>
        <p className="ds-modal-title">{vipLangPkg.PCW_VIP_1742886668743_710}</p>
        <p className="ds-modal-subtitle">
          {vipLangPkg.PCW_VIP_1742179424426_771 ||
            'Please proceed with 3DS verification first'}
        </p>
        <button
          className="ds-modal-btn ds-modal-confirm"
          type="button"
          rseat="click"
          data-pb="rpage=cashier_popup&block=3ds_gaid&cashier_type=popup"
          onClick={modalConfirm}
        >
          {vipLangPkg.PCW_VIP_1742179473668_487 || 'To Verify'}
        </button>
      </div>
    </ModalWrapper>
  )
}
export default DSModal
