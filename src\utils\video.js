/**
 * 判断是否是专辑id
 * @param {*} id
 */
const isAlbumId = id => {
  id += ''
  return id.endsWith('01') || id.endsWith('08')
}

/**
 * 判断是否是播单id
 */
const isBodanId = id => {
  id += ''
  return id.endsWith('02')
}
/**
 * 判断是否是视频id
 * @param {*} id
 */
const isVideoId = id => {
  if (id > 0) {
    id += ''
    return !(isAlbumId(id) || isBodanId(id)) && id.endsWith('00')
  } else {
    return false
  }
}
// 判断subType
const handleSubType = (sourceCode, albumId) => {
  if (sourceCode) {
    return 2
  } else if (isAlbumId(albumId)) {
    return 1
  } else {
    return 7
  }
}
// 将剧集、来源视频列表中当前播放的视频滚动到可视区
const handleVideoScrollTop = (parWrap, listWrap) => {
  const parWrapHeight = parWrap.offsetHeight // 内容可视高度
  const titleWrap = parWrap.getElementsByClassName('intl-play-top-title') || []
  const titleHeight = titleWrap.length ? titleWrap[0].offsetHeight : 0
  const selectedItemArr = listWrap.getElementsByClassName('selected') || []
  const filterItem = Array.from(selectedItemArr).filter(item => {
    return item.getAttribute('juji-order') || item.getAttribute('period-order')
  })
  if (filterItem.length) {
    const selectedItem = filterItem[0]
    const order = selectedItem.getAttribute('juji-order')
    const period = selectedItem.getAttribute('period-order')
    if (order) {
      let itemNum = 5
      let itemBottom = 12
      if (window.innerWidth >= 1680) {
        itemNum = 6
        itemBottom = 16
      } else if (window.innerWidth < 1280) {
        itemNum = 4
        itemBottom = 10
      }
      const itemClientHeight = selectedItem.clientHeight + itemBottom
      const lineNum = (parseInt(order, 10) + 1) / itemNum // 剧集行号
      const itemHeight = itemClientHeight * Math.ceil(lineNum)
      const scrollHeight = titleHeight + itemHeight
      if (scrollHeight > parWrapHeight - 100) {
        // 当前播放视频在列表中可见且在底部时不太好看所以加了100
        parWrap.scrollTo(0, itemHeight)
      } else {
        parWrap.scrollTo(0, 0)
      }
    }
    if (period) {
      const periodNum = parseInt(period, 10) + 1 // 来源期数行号
      const itemHeight = selectedItem.clientHeight * periodNum
      const scrollHeight = titleHeight + itemHeight
      if (scrollHeight > parWrapHeight - 200) {
        // 当前播放视频在列表中可见且在底部时不太好看所以加了200
        parWrap.scrollTo(0, itemHeight - selectedItem.clientHeight * 2)
      } else {
        parWrap.scrollTo(0, 0)
      }
    }
  }
}

// 处理视频列表页签展示，每50集为一页
const handlePageRange = (param = {}) => {
  const params = Object.assign(
    {
      totalNum: 0,
      size: 50,
      firstOrder: 1
    },
    param
  )
  const { totalNum, size, firstOrder } = params
  let from
  let to
  let rangeMsg
  let totalPage
  const pageRange = []
  if (totalNum % size === 0) {
    totalPage = totalNum / size
  } else {
    totalPage = totalNum / size + 1
  }
  totalPage = parseInt(totalPage, 10)
  for (let i = 0; i < totalPage; i++) {
    from = i * size + 1
    to = (i + 1) * size
    if (i === totalPage - 1) {
      to = totalNum
    }
    // from--> to之间有内容才会生成标签,tab签数才会++
    if (to >= firstOrder) {
      rangeMsg = from + '-' + to
      pageRange.push({
        pageNo: i + 1,
        from,
        to,
        msg: rangeMsg
      })
    }
  }
  return pageRange
}

const getCurOrderInfo = (params = {}) => {
  const { list, curVideoInfo, pkg } = params
  const tvid = curVideoInfo.get('tvId') || curVideoInfo.get('tvid')
  const curOrderVideo = list.filter(item => {
    return item.tvId === tvid
  })
  let curOrder = curVideoInfo.get('order')
  let publishTime = curVideoInfo.get('publishTime')
  let subTitle = curVideoInfo.get('subTitle')
  let orderInfo
  let titleName = curVideoInfo.get('name')
  let albumName
  let videoType = ''
  if (curOrderVideo.length) {
    videoType = curOrderVideo[0].videoType
    curOrder = curOrderVideo[0].order
    publishTime = curOrderVideo[0].publishTime
    if (videoType !== 'laiyuan') {
      subTitle = curOrderVideo[0].subInfo
    } else {
      subTitle = curOrderVideo[0].subTitle
    }
    titleName = curOrderVideo[0].name
    albumName = curOrderVideo[0].albumName || ''
  }
  const channelId = curVideoInfo.get('channelId')
  const curAlbumName = curVideoInfo.get('albumName')
  albumName = curAlbumName ? curAlbumName + ' ' : albumName + ' '
  // 来源类视频也支持分页功能，也有order了，所以加type判断
  if (curOrder && videoType !== 'laiyuan') {
    const episode = pkg.episode || ''
    const aName = albumName.replace(/^\s+|\s+$/g, '')
    if (channelId === 4 && subTitle && aName !== subTitle) {
      // 动漫
      orderInfo =
        subTitle.indexOf(aName) === -1 ? albumName + subTitle : subTitle
    } else {
      orderInfo = albumName + episode.replace('{}', curOrder)
    }
  } else {
    orderInfo = subTitle || publishTime
    if (channelId === 6) {
      // 综艺
      orderInfo = albumName + orderInfo
    } else if (channelId === 7) {
      // 娱乐
      orderInfo = titleName || albumName + orderInfo
    }
  }
  return {
    info: orderInfo,
    title: titleName
  }
  // changeOrderInfo({ info: orderInfo, title: titleName })
}

module.exports = {
  isAlbumId,
  isVideoId,
  isBodanId,
  handleSubType,
  handleVideoScrollTop,
  handlePageRange,
  getCurOrderInfo
}
