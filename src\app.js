import React from 'react';
import ReactDOM from 'react-dom';
import { <PERSON><PERSON>utton, PopupVip, createVipOrder, hideVipPopup } from './components';

function App() {
  // const {
  //   origin,
  //   pathname,
  //   search
  // } = window.location
  // const newSearch = '?mod=th&lang=zh_cn&QC005=8fc57f60c3d382cc0b99dc6562873d45'

  // if (search !== newSearch) {
  //   window.location.href = origin + pathname + newSearch
  // }
  return (
    <>
      <NewButton name="新按钮" clickFn={createVipOrder}/>
      <PopupVip />
    </>
  )
}

ReactDOM.render(<App />, document.getElementById('root'));