import styled from 'styled-components'

const StayPopWrapper = styled.div`
  position: fixed;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  z-index: 10;

  /* 整体弹窗容器 - 相对于收银台弹窗定位 */
  .retention-popup-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 400px;
    border-radius: 8px;
    /* 弹窗由多个部分组成，背景色和圆角由子元素定义 */
  }

  /* 1. 背景图区域 (.retention-popup-image-area) */
  .retention-popup-image-area {
    position: relative;
    width: 400px;
    opacity: 1;
    border-radius: 8px 8px 0px 0px;
    display: flex;
    overflow: hidden;
  }

  /* 1张图片时的样式 */
  ${props => props.imageCount === 1 && `
    .retention-popup-image-area {
      height: 225px;
    }
    .retention-popup-image {
      width: 100%;
      height: 225px;
      object-fit: cover;
      border-radius: 8px 8px 0px 0px;
    }
  `}

  /* 2张图片时的样式 */
  ${props => props.imageCount === 2 && `
    .retention-popup-image-area {
      width: 400px;
      height: 267px;
    }
    .retention-popup-image {
      width: 200px;
      height: 267px;
      object-fit: cover;
    }
    .retention-popup-image:first-child {
      border-radius: 8px 0px 0px 0px;
    }
    .retention-popup-image:last-child {
      border-radius: 0px 8px 0px 0px;
    }
  `}

  /* 3张图片时的样式 */
  ${props => props.imageCount === 3 && `
    .retention-popup-image-area {
      width: 400px;
      height: 178px;
    }
    .retention-popup-image:first-child {
      width: 133px;
      height: 178px;
      object-fit: cover;
      border-radius: 8px 0px 0px 0px;
    }
    .retention-popup-image:nth-child(2) {
      width: 134px;
      height: 178px;
      object-fit: cover;
      border-radius: 0px;
    }
    .retention-popup-image:last-child {
      width: 133px;
      height: 178px;
      object-fit: cover;
      border-radius: 0px 8px 0px 0px;
    }
  `}

  /* 兜底图片样式 */
  .retention-popup-image-placeholder,
  .retention-popup-image-fallback {
    border-radius: 8px 8px 0px 0px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 16px;
    font-weight: bold;
    text-align: center;
  }

  /* 2. 合并的内容区域 - 包含描述和按钮 (.retention-popup-content-area) */
  .retention-popup-content-area {
    position: relative;
    width: 400px;
    height: 230px; /* 70px(描述) + 160px(按钮) = 230px */
    background-color: #FFFFFF;
    border-radius: 0px 0px 8px 8px;
    display: flex;
    flex-direction: column;
    padding: 0;
    box-sizing: border-box;
    margin: 0;
  }

  /* 2.1 描述区域 (.retention-popup-description-block) */
  .retention-popup-description-block {
    width: 100%;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 40px;
    box-sizing: border-box;
    background: transparent;
  }

  /* 2.2 描述文案 (.retention-popup-description-text) */
  .retention-popup-description-text {
    width: 320px;
    height: 38px;
    font-family: SFPro-Bold;
    font-size: 16px;
    color: #222222;
    font-weight: 700;
    text-align: center;
    letter-spacing: 0;
    line-height: 1.2;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* 2.3 按钮区域 (.retention-popup-actions-block) */
  .retention-popup-actions-block {
    width: 100%;
    height: 160px;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 16px 40px 40px;
    box-sizing: border-box;
    gap: 12px;
    background: transparent;
  }

  /* 3.1 继续购买按钮 (.retention-popup-button-continue) */
  .retention-popup-button-continue {
    position: relative;
    width: 320px;
    height: 44px;
    background: #F2BF83;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      &:after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 4px;
        background: rgba(255, 255, 255, 0.2);
        z-index: 1;
      }
    }
  }

  /* 3.1.1 继续购买按钮文字 (.retention-popup-button-continue-text) */
  .retention-popup-button-continue-text {
    position: relative;
    font-family: SFPro-Bold;
    font-size: 16px;
    color: #111319;
    font-weight: 700;
    text-align: center;
    line-height: 20px;
    z-index: 2;
  }

  /* 3.2 放弃优惠按钮 (.retention-popup-button-giveup) */
  .retention-popup-button-giveup {
    position: relative;
    width: 320px;
    height: 44px;
    border: 1px solid #E09E51;
    border-radius: 4px;
    background-color: transparent;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      &:after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 4px;
        background: rgba(242, 191, 131, 0.3);
        z-index: 1;
      }
    }
  }

  /* 3.2.1 放弃优惠按钮文字 (.retention-popup-button-giveup-text) */
  .retention-popup-button-giveup-text {
    position: relative;
    font-family: SFPro-Bold;
    font-size: 16px;
    color: #E09E51;
    font-weight: 700;
    text-align: center;
    line-height: 20px;
    z-index: 2;
  }
`

export default StayPopWrapper
