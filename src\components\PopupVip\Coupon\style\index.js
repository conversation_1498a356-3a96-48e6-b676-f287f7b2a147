import styled from 'styled-components'
const CouponWrapper = styled.div`
  padding: 20px 40px;
  .coupon-text-wrapper {
    display: flex;
    align-items: center;
  }
  .coupon-text {
    font-size: 16px;
    color: #222222;
    font-weight: bold;
  }
  .redeem-btn,
  .cancel-btn {
    margin-left: 12px;
    font-size: 14px;
    color: #e09e51;
    cursor: pointer;
  }
  .coupon-title {
    font-size: 16px;
    color: #999999;
  }
  .selected-coupon {
    color: #000000;
  }
  .selected-coupon-price {
    margin-left: 5px;
    color: #fa6400;
  }
  .coupon-wrapper {
    position: relative;
    width: 420px;
    margin-top: 12px;
    padding: 12px;
    border: 1px solid #e6e6e6;
    border-radius: 4px;
    cursor: pointer;
    box-sizing: border-box;
    &:hover {
      .arrow-show {
        background: url('//www.iqiyipic.com/new-vip/<EMAIL>')
          no-repeat center;
        background-size: 100% 100%;
      }
      .no-coupons-icon {
        background: url('//www.iqiyipic.com/new-vip/arrow-down.png') no-repeat
          center;
        background-size: 100% 100%;
      }
    }
  }
  .no-coupons {
    opacity: 0.5;
  }

  .arrow-show {
    position: absolute;
    right: 12px;
    top: 16px;
    width: 12px;
    height: 12px;
    background: url('//www.iqiyipic.com/new-vip/arrow-down.png') no-repeat
      center;
    background-size: 100% 100%;
    transition: transform 0.1s linear;
  }
  .no-coupons-icon {
    opacity: 0.5;
    background: url('//www.iqiyipic.com/new-vip/arrow-down.png') no-repeat
      center;
    background-size: 100% 100%;
  }
  .icon-down {
    transform: rotate(180deg);
  }
  @keyframes rollDown {
    0% {
      transform: rotate(0);
    }
    100% {
      transform: rotate(180deg);
    }
  }

  .risk-error {
    display: flex;
    align-items: center;
    width: 420px;
    padding: 8px;
    margin-bottom: 12px;
    background: rgba(254, 59, 48, 0.08);
    border-radius: 4px;
    box-sizing: border-box;
  }
  .risk-err-icon {
    width: 14px;
    height: 14px;
    margin-right: 4px;
  }
  .risk-err-tip {
    font-size: 12px;
    color: #fe3b30;
    letter-spacing: 0;
  }
  /* 优惠券部分 */
  .coupon-list {
    position: absolute;
    bottom: calc(100% + 8px);
    left: 0;
    padding: 16px 15px;
    background: #ffffff;
    border: 1px solid #e6e6e6;
    box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.1),
      0 8px 10px 1px rgba(0, 0, 0, 0.06), 0 3px 14px 2px rgba(0, 0, 0, 0.05);
    border-radius: 4px;
    box-sizing: border-box;
    max-height: 400px;
    overflow-y: scroll;
    overflow-x: hidden;
    z-index: 4;
  }
  .coupon-item {
    position: relative;
    width: 388px;
    /* height: 88px; */
    padding: 16px;
    background: #ffffff;
    border: 1px solid #e6e6e6;
    border-radius: 4px;
    /* box-sizing: ; */
    box-sizing: border-box;
    & + .coupon-item {
      margin-top: 12px;
    }
    &:hover {
      background: #ffffff;
      border-color: #f2bf83;
    }
    &.coupon-item-focus {
      background: #fcf2e6;
      border: 2px solid #f2bf83;
    }
  }
  .disabled-coupon-item {
    .coupon-item-title {
      color: #999999;
    }
    .coupon-discount {
      color: #999999;
    }
    &:hover {
      background: #ffffff;
      border: 1px solid #e6e6e6;
    }
  }

  .coupon-top {
    display: flex;
    align-items: stretch;
    justify-content: space-between;
    height: 100%;
    .left-content {
      display: flex;
      flex: 1;
      flex-wrap: wrap;
      overflow: hidden;
      align-content: center;
    }
    .right-content {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      width: 140px;
      height: 54px;
    }
  }
  .coupon-item-title {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    width: 100%;
    font-size: 14px;
    color: #000000;
  }
  .condition-btn {
    display: flex;
    align-items: center;
    width: 100%;
    margin-top: 6px;
    font-size: 14px;
    color: #999999;
    &:hover {
      .condition-arrow {
        background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAGKADAAQAAAABAAAAGAAAAADiNXWtAAABGElEQVRIDWNgGAWjITAaAozoQaCsrNwJFCv4//8/G7ocPj4jI+NvoPzEu3fvliKrY0LmgNhAgyNINRyqjxWoLxjdPAwLgAoKgfgfukIi+CA9JejqmNEF3r9/f11ISOgdUNwLXQ4fHxhEeffu3VuIrgbDApACoCWnhIWFOYFetkHXgI3PxMTUCQz7NmxyGJEMUwQ0nBEY4YuA/BiYGA56CdDwOKAP/mOTxxYHYHUgDYKCgklAzh5sGqFie0BqcBkOUoPTBzBD1dXVeX///n0IyDeAiUHpC6ysrHY3b978jCaOwsXpA5gqkAHs7OygCH8AEwOxQWKEDAepJ+gDmKFAn6j/+fNnGUgPCwtLJNDwmzC5UXo0BGgbAgDgoVQ6s2CX2QAAAABJRU5ErkJggg==)
          no-repeat center;
        background-size: 100% 100%;
      }
    }
  }
  .disable-condition-btn {
    font-size: 14px;
    color: #fe3b30;
  }
  .condition-arrow {
    width: 12px;
    height: 12px;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAGKADAAQAAAABAAAAGAAAAADiNXWtAAABC0lEQVRIDWNgGAWjITAaAozoQTBr1qxOoFjB////2dDlCPB/MzIyTkxLSytFVseEzAGxgQZHkGE4SCsrEAeDGMgAwwImJqZCoIJ/yIqIZP8D+qAEXS2GBampqeuAivLRFRLBz4fqRVGKYQFINj09fQrQNaC4IAqA1IL0YFOMEckwRcB4YARG+CIgPwYmhoNeAozYOKAl/7HJY/UBSCFUQxKQ3oNNI1QNSA6kBqvhYDW4NMPE586dy/v3799DQB8ZwMTAGhkZLzAzM9slJyd/RhZHZ+P0AUwhyACg4V5A/gOYGIgNEiNkOEg9zjhAMgzMBPpE/c+fP8tAelhYWCKBht9EVzPKHw0B2oQAACcsSbnBlzQ5AAAAAElFTkSuQmCC)
      no-repeat center;
    background-size: 100% 100%;
    margin-left: 6px;
  }
  .coupon-discount {
    font-size: 16px;
    color: #fa6400;
    text-align: right;
  }
  .condition-detail {
    padding-top: 12px;
    height: auto;
    overflow: hidden;
    transition: max-height 0.15s linear;
    box-sizing: border-box;
  }
  .condition-detail-item {
    font-size: 14px;
    color: #999999;
  }
  .redeem-coupon-wrapper {
    margin-top: 12px;
  }
  .banknumber-input-wrapper {
    position: relative;
    font-size: 16px;
    font-weight: 400;
    display: flex;
    width: 420px;
    flex: 1;
    align-items: stretch;
    flex-wrap: wrap;
    .banknumber-input-label {
      position: relative;
      box-sizing: border-box;
      border: 1px solid #e6e6e6;
      border-radius: 4px;
      transform-origin: bottom center;
      transition: border 0.3s;
      /* margin-bottom: 16px; */
      /* border: 1px solid #ff0000; */
      // overflow: hidden;
      display: flex;
      flex: 1;
      outline: none;
      &:focus-within {
        border: 1px solid #f2bf83;
        & .start-redeem {
          border-color: #f2bf83;
        }
      }
    }
    .banknumber-input-input {
      display: block;
      box-sizing: border-box;
      margin: 0;
      border: none;
      outline: none;
      // border-radius: 4px;
      padding: 0 12px 12px;
      width: 100%;
      height: 60px;
      /* flex: 1; */
      border-top: solid 27px transparent;
      // color: rgba(var(--pure-material-onsurface-rgb, 0, 0, 0), 0.87);
      color: #222222;
      box-shadow: none;
      border-radius: 4px;
      font-size: 16px;
      transition: border-bottom 0.2s, background-color 0.2s;
      -webkit-appearance: none;
      &[type='password'] {
        font-size: 20px;
      }

      // &:focus{
      //   +span{
      //     font-size: 12px;
      //     line-height: 36px;
      //     // &::after{
      //     //     border-color: #00CC36;
      //     // }
      //   }
      // }
      &:not(:focus):placeholder-shown + span {
        font-size: 14px;
        line-height: 60px;
      }
    }
    .banknumber-input-desc {
      // font-size: 14px;
      // line-height: 60px;
      font-size: 12px;
      line-height: 36px;
      color: #999999;
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: block;
      box-sizing: border-box;
      padding: 0 12px;
      pointer-events: none;
      /* border: 1px solid #FFFF00; */
      transition: color 0.2s, font-size 0.2s, line-height 0.2s;
    }
  }
  .shake-border {
    animation: shakeBorder 1.2s linear 1;
    .start-redeem,
    .banknumber-input-desc {
      animation: shakeBorder 1.2s linear 1;
    }
  }
  @keyframes shakeBorder {
    0% {
      border-color: #e6e6e6;
      background: #ffffff;
    }
    35% {
      border-color: #f2bf83;
      background: #fcf2e6;
    }
    45% {
      border-color: #e6e6e6;
      background: #ffffff;
    }
    65% {
      border-color: #f2bf83;
      background: #fcf2e6;
    }
    70% {
      border-color: #e6e6e6;
      background: #ffffff;
    }
  }
  .coupon-input-wrapper {
    display: flex;
    position: relative;
    align-content: center;
    align-items: stretch;
    flex-wrap: wrap;
    flex: 1;
  }
  .start-redeem {
    min-width: 88px;

    padding: 0 12px;
    border-left: 1px solid #e6e6e6;
    cursor: pointer;
    font-size: 16px;
    color: #e09e51;
    box-sizing: border-box;
    white-space: nowrap;
    line-height: 60px;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
  }
  .start-reedem-loading {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .disable-start-redeem {
    color: #999999;
  }
  .redeem-loading {
    width: 30px;
    height: 30px;
    background: url('//www.iqiyipic.com/new-vip/loading.png') no-repeat center;
    background-size: 100% 100%;
    animation: Loading 1s linear infinite;
  }
  @keyframes Loading {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
  .coupon-input-tip {
    width: 100%;
    margin-top: 8px;
    font-size: 12px;
    color: #999999;
    font-weight: 400;
  }
  .ibg-vote-loading-wrapper,
  .ibd-fengkong-wrapper {
    position: fixed;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    background: rgba(0, 0, 0, 0.4);
    z-index: 13;
    .passport-loading-outside__txt {
      color: rgba(255, 255, 255, 0.6);
    }
  }
  .ibd-fengkong-outer {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  .slide-tips {
    p {
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  .success-icon {
    width: 40px;
    height: 40px;
  }
  .sucees-tip {
    font-size: 16px;
    color: #ececec;
  }
`

export default CouponWrapper
