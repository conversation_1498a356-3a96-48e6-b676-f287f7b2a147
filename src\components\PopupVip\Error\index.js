import React from 'react'
import ErrorWrapper from './style'

const TemplateErrorPop = props => {
  const { image, title, btnText, btnClick } = props
  return (
    <div className="scroll-container">
      <div className="scroll-content">
        <ErrorWrapper>
          <img alt="" className="error-img" src={image} />
          <div className="error-title">{title}</div>
          <button type="button" className="error-button" onClick={btnClick}>
            {btnText}
          </button>
        </ErrorWrapper>
      </div>
    </div>
  )
}

export default TemplateErrorPop
