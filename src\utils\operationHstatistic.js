// 计算网页停留时间
import Browser from '@/kit/browser'
import generateRandomString from '@/kit/random'
import { sendLog } from '@/utils/pingBack'
import { getDevice } from '@/kit/device'
import { getCookies } from '@/kit/cookie'

let startTime = +new Date()
let params
const url = '//msg-intl.qy.net/act'
let dfp
// let close = false;

let timeOut = null
let sendTime = 0 // 已成功投递日志的条数
let lastTm = 0
const FIRST_TIME_CELL = 15000 // 第一次投递的时间间隔是15s
const SECOND_TIME_CELL = 60000 // 第二次投递的时间间隔是60s
const OTHER_TIME_CELL = 120000 // 以后每隔120s发一次

let p1 = '1_10_222'
if (getDevice() === 'mobile') {
  p1 = '2_20_223'
}

function timer(time) {
  timeOut = setTimeout(() => {
    clearTimeout(timeOut)
    // doSend()
    callLogger(params)
    resetTM()
    sendTime++
    if (sendTime === 1) {
      timer(SECOND_TIME_CELL)
    } else {
      timer(OTHER_TIME_CELL)
    }
  }, time)
}

function clearTimer() {
  clearTimeout(timeOut)
  sendTime = 0
}
function getDur() {
  return (+new Date() - startTime) / 1000
}

function doSend() {
  // 可能切换选集
  const browser = new Browser()
  params.qtcurl = window.location.href
  // params.pu = ''
  // hack  chrome 防止 播放页会员登录，会重复发两条请求
  const now = new Date()
  if (browser.CHROME && now - lastTm <= 40) {
    return
  }
  lastTm = now
  params.tm = getDur()
  params.dfp = window.dfp && window.dfp.tryGetFingerPrint()
  sendLog(url, params)
}
// 重新计时
function resetTM() {
  params.tm = 0
  startTime = +new Date()
}

function bindStatistic() {
  const browser = new Browser()
  timer(FIRST_TIME_CELL)
  window.onbeforeunload = () => {
    // close = true;
    // doSend()
    callLogger(params)
    clearTimer()
  }

  if (browser.IE) {
    // document.addEventListener("msvisibilitychange",() =>{
    //     if (close === true) {
    //         return;
    //     }
    // });
  } else if (document.hidden !== undefined) {
    // document.onvisibilitychange = function(e) {
    //     if (close === true) {
    //         return;
    //     }
    // }
  } else {
    // let v = "visibilitychange";
    // let h = "hidden";
    // if (browser.ff) {
    //     h = "mozHidden";
    //     v = "mozvisibilitychange";
    // } else if (browser.CHROME){
    //     h = "webkitHidden";
    //     v = "webkitvisibilitychange";
    // }
    // document.addEventListener(v,() =>{
    //     if (close === true) {
    //         return;
    //     }
    // });
  }
}

const operateStatistic = param => {
  startTime = +new Date()
  params = {
    t: 30,
    tm: 0,
    p1,
    s1: 1,
    r: '',
    u: getCookies('QC005'),
    rn: generateRandomString(10),
    nu: getCookies('nu'),
    dfp,
    pu: '',
    pagev: param.pagev || '',
    c1: param.cid || '',
    tmlpt: param.tmlpt || '',
    rpage: param.rpage || '',
    qtcurl: window.location.href,
    stime: new Date().getTime(),
    timezone: new Date().toUTCString(),
    mod: getCookies('mod'),
    lang: getCookies('lang'),
    purl: window.location.href
  }
  bindStatistic()
}

export const callLogger = () => {
  if (window && window.dfp && window.lib && window.mainQaInstance) {
    const Qa = window.mainQaInstance // 在global_pcw_qa.js 1798行 注入
    Qa.getHu(hu => {
      Qa.getFingerPrint(dfp => {
        params['dfp'] = dfp
        params['hu'] = hu
        params['pu'] = Qa.getUserInfoUid()
        params['de'] = Qa.getQtsid()
        params['ce'] = Qa.getWeid()

        doSend()
      })
    })
  } else {
    doSend()
  }
}

export default operateStatistic
