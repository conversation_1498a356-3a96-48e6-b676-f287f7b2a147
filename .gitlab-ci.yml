image: docker-registry.qiyi.virtual/tp-tw/node-v16.18.0:latest

stages:
  - dev
  - prod

cache:
  paths:
    - node_modules/ # Node modules and dependencies

before_script:
  - node -v
  - npm install yarn -g
  - npm -v
job-dev:
  stage: dev
  script:
    - yarn install
    - npm run build
    - npm run ci:dev
  only:
    - /^dev-.*$/
  except:
    - \[skip ci\]

job-master:
  stage: prod
  script:
    - yarn install
    - npm run build
    - npm run ci:prodmaster
  only:
    - master
