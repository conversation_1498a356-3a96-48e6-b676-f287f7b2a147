import React from 'react'
import { removeProtocol } from '@/kit/url'
import ImgCardWrapper from './style'
const ImgCard = ({ cardData }) => {
  const { picUrl = '', link = '' } = cardData
  return (
    <ImgCardWrapper>
      <a href={link || '//iq.com'} className="img-card-link">
        <img src={removeProtocol(picUrl)} alt="" className="card-img" />
      </a>
    </ImgCardWrapper>
  )
}
export default ImgCard
