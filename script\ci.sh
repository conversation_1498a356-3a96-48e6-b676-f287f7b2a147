#!/bin/bash

echo "Begin to build global vip sdk..."

env=$1

readonly env

echo ".######..#####...#####...........######..######..##..##...####.."
echo "...##....#####...##..##..######....##....####....##..##...####.."
echo "................................................................"

PKG_VERSION=`node -p "require('./package.json').version"`
echo "$PKG_VERSION"

if [ "${env}" == "dev" ]; then
    echo 'publish alpha----------------0.0.1-alpha.0---------$PKG_VERSION'
    result=$(echo $PKG_VERSION | grep "alpha")
    ## npm version prerelease --preid=alpha
    if [[ "$result" != "" ]]; then
        npm publish
    fi
else
    ## npm version patch
    npm publish
fi